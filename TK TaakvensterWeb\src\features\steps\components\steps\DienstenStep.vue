<template>
  <div id="panel-2">
    <StepHeader :title="stepTitle" :subTitle="stepDescription" />
    <div class="dienst-container">
      <FlowbiteList :items="diensten" v-model="selectedDienst" @item-click="handleDienstClick">
        <template #item="{ item }">
          {{ item.naam }}
        </template>
      </FlowbiteList>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useOffice } from '../../../office'
import { useErrorHandler } from '../../../shared'
import { getStepBehaviorConfig } from '../../../../config/appConfig'
import StepHeader from './StepHeader.vue'
import FlowbiteList from '../../../shared/components/common/FlowbiteList.vue'

// Self-contained step information (no props needed)
const stepTitle = 'Diensten'
const stepDescription = 'Selecteer een dienst om toe te voegen aan het document.'

// New minimal event system
const emit = defineEmits({
  'step-ready': null, // Component loaded and ready
  'step-complete': null, // User completed this step
  'step-previous': null, // User wants to go back
})

const { insertTextInContentControl } = useOffice()
const { withErrorHandling } = useErrorHandler()
const selectedDienst = ref(null)

// Mock data for diensten (services), to be replaced with actual data from SharePoint
const diensten = [
  { naam: 'Dienst 1', value: 'Dienst 1 inhoud', controlTag: 'Dienst' },
  { naam: 'Dienst 2', value: 'Dienst 2 inhoud', controlTag: 'Dienst' },
  { naam: 'Dienst 3', value: 'Dienst 3 inhoud', controlTag: 'Dienst' },
]

// Handle dienst click from FlowbiteList
function handleDienstClick(event) {
  // selectedDienst is already updated via v-model binding
  // so we just need to process the document insertion
  addSelectedDienstToDocument(event.index)
}

// Add the selected dienst to the document
async function addSelectedDienstToDocument(index) {
  try {
    // Validate that a valid index was provided
    if (index === null || index === undefined || index < 0 || index >= diensten.length) {
      await withErrorHandling(
        () => Promise.reject(new Error('Invalid selection')),
        'validating selection',
        { userMessage: 'Selecteer eerst een dienst voordat u verder gaat.' },
      )
      return
    }

    selectedDienst.value = index
    const dienst = diensten[selectedDienst.value]

    await withErrorHandling(
      () => insertTextInContentControl(dienst.controlTag, dienst.value),
      `adding ${dienst.naam}`,
      {
        userMessage: `Failed to add ${dienst.naam}. Please try again.`,
        successMessage: `${dienst.naam} is toegevoegd aan het document.`,
      },
    )

    // Signal step completion (like VBA CommandButton_Click)
    setTimeout(() => {
      emit('step-complete')
    }, getStepBehaviorConfig().completionDelay) // Delay to ensure notification is visible before navigation
  } catch (error) {
    // Reset selection on error
    selectedDienst.value = null
  }
}

// Signal that component is ready (like VBA UserForm_Initialize)
onMounted(() => {
  emit('step-ready')
})
</script>

<style scoped>
.dienst-container {
  margin-top: 1rem !important;
  width: 100%;
}
</style>
