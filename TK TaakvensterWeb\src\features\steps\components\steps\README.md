# Step Components Guide

This guide explains how to work with step components in the TK Taakvenster application.

## Understanding Steps

Each step is like a **VBA UserForm** - it's a complete, self-contained component that:
- Shows a title and description
- Displays a list of options for the user
- Inserts the selected value into a Word document
- Moves to the next step when complete

## File Structure

```
src/components/steps/
├── _StepTemplate.vue          ← Copy this to create new steps
├── RubriceringStep.vue        ← Example: Simple step
├── DienstenStep.vue           ← Example: Simple step  
├── CommissieStep.vue          ← Example: Simple step
└── README.md                  ← This file
```

## Creating a New Step

### 1. Copy the Template
```bash
# Copy the template file
cp _StepTemplate.vue YourNewStep.vue
```

### 2. Follow the TODO Comments
The template has clear TODO comments showing you exactly what to change:

```vue
<!-- TODO: Change panel ID -->
<div id="panel-X">

<!-- TODO: Update these step details -->
const stepTitle = 'Your Step Title'

<!-- TODO: Replace with your step's data -->
const stepOptions = [...]
```

### 3. Register Your Step
Add your new step to `src/composables/useStepConfig.js`:

```javascript
import YourNewStep from '../components/steps/YourNewStep.vue'

export function useStepConfig() {
  return [
    // ... existing steps
    { 
      name: 'Your Step Name', 
      contentControl: 'txtYourContentControl', 
      component: markRaw(YourNewStep) 
    }
  ]
}
```

## Common Patterns

### Simple List Step (Most Common)
This is what 90% of steps will be - just copy RubriceringStep.vue and modify the data.

### Step with Additional Input
```vue
<template>
  <!-- Standard header and list -->
  <StepHeader :title="stepTitle" :subTitle="stepDescription" />
  <FlowbiteList :items="options" @item-click="handleClick" />
  
  <!-- Additional input -->
  <div class="mt-4">
    <input v-model="customText" placeholder="Enter custom text..." />
    <button @click="handleCustomAction">Apply Custom</button>
  </div>
</template>

<script setup>
// Add the extra data
const customText = ref('')

// Add the custom function
async function handleCustomAction() {
  await withErrorHandling(
    async () => {
      // Your custom logic
      await insertTextInContentControl('txtCustom', customText.value)
      emit('step-complete')
    },
    'applying custom text',
    { successMessage: 'Custom text applied!' }
  )
}
</script>
```

### Step with Multiple Lists
```vue
<template>
  <div class="grid grid-cols-2 gap-4">
    <FlowbiteList :items="listA" @item-click="handleListA" />
    <FlowbiteList :items="listB" @item-click="handleListB" />
  </div>
  <button @click="applyBoth">Apply Both</button>
</template>

<script setup>
const selectedA = ref(null)
const selectedB = ref(null)

function handleListA(event) { selectedA.value = event.index }
function handleListB(event) { selectedB.value = event.index }

async function applyBoth() {
  // Apply both selections
}
</script>
```

## Key Concepts

### 1. Content Controls
Each step inserts data into a **Word content control** (like a VBA bookmark):
```javascript
const stepOptions = [
  { 
    label: 'What user sees',
    value: 'What goes in document', 
    controlTag: 'txtContentControlName'  // Must match Word template
  }
]
```

### 2. Error Handling
Always wrap document operations in `withErrorHandling`:
```javascript
await withErrorHandling(
  () => insertTextInContentControl(controlTag, value),
  'description of what you are doing',
  {
    userMessage: 'User-friendly error message',
    successMessage: 'Success message'
  }
)
```

### 3. Step Events
Every step must emit these events:
- `step-ready` - When component loads (in onMounted)
- `step-complete` - When user completes the step
- `step-previous` - If you add a "back" button

## Troubleshooting

### Step Not Showing
1. Check if it's registered in `useStepConfig.js`
2. Check if the `contentControl` exists in the Word template
3. Check browser console for errors

### Document Insertion Fails
1. Verify the `controlTag` matches the Word template exactly
2. Check if Word document is open and active
3. Look at browser console for detailed error messages

### Styling Issues
Each step has scoped CSS - styles only apply to that component:
```vue
<style scoped>
.my-custom-class {
  /* Only applies to this step */
}
</style>
```

## Best Practices

1. **Keep it simple** - Each step should do one clear thing
2. **Copy existing patterns** - Don't reinvent, copy and modify
3. **Test thoroughly** - Always test with real Word documents
4. **Use clear names** - Make controlTag names descriptive
5. **Add comments** - Explain any complex logic

## Getting Help

- Look at existing steps for examples
- Check the browser console for error messages
- Test with the Word template to verify content controls exist
