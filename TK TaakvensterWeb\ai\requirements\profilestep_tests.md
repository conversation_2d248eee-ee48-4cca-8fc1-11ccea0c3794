# ProfielAfzender Step Testing Requirements

## Overview

This document outlines the comprehensive testing strategy for the ProfielAfzender step implementation, based on existing test patterns in the codebase and specific requirements for profile/commission data handling.

## Test Categories

### 1. Unit Tests for ProfielAfzender Component
*Following RubriceringStep.spec.js pattern*

#### Basic Component Tests
- **Component Rendering**: Component mounts without errors
- **Event Emission**: `step-ready` event emitted on mount
- **Component Structure**: Proper template rendering with FlowbiteList

#### Selection and Validation Tests
- **Invalid Selection Validation**: 
  - Null/undefined index handled properly
  - Out-of-bounds index handled properly
  - Error message displayed via withErrorHandling
- **Valid Selection Processing**: 
  - Valid profile selection triggers Office API calls
  - Correct parameters passed to insertTextInContentControl
  - Step completion emitted after successful operations

#### Error Handling Tests
- **Office API Errors**: 
  - Content control insertion failures handled gracefully
  - Component state reset on errors (selectedProfile = null)
  - Error collection and message bar integration
- **Multiple Error Scenarios**:
  - Multiple content control failures collected
  - Single consolidated error message displayed
  - Processing continues despite individual failures

### 2. Data Validation Tests
*Specific to ProfielAfzender requirements*

#### Profile Data Validation
- **Complete Profile Data**: All fields present (FirstName, LastName, Role, EmailAddress, CommissionId)
- **Partial Profile Data**: Missing optional fields (Role, EmailAddress) handled gracefully
- **Invalid Profile Data**: 
  - Missing FirstName: profile skipped, console warning logged
  - Missing LastName: profile skipped, console warning logged
  - Empty string FirstName/LastName: profile skipped, console warning logged
  - Null/undefined FirstName/LastName: profile skipped, console warning logged

#### Commission Data Validation
- **Complete Commission Data**: All fields present (CommissionName, POBoxNumber, ZipCode, City, StreetName, HouseNumber)
- **Partial Commission Data**: Missing optional fields handled gracefully in address formatting
- **Invalid Commission Data**:
  - Missing CommissionName: commission skipped, console warning logged
  - Empty string CommissionName: commission skipped, console warning logged
  - Null/undefined CommissionName: commission skipped, console warning logged

#### Commission Linking Tests
- **Valid Commission Link**: Profile.CommissionId matches existing commission
- **Invalid Commission Link**: 
  - Profile.CommissionId doesn't match any commission
  - Error logged to console
  - Error added to collection for message bar
  - Only txtAfzenderPersoonlijk populated

### 3. Content Control Population Tests
*Core functionality testing*

#### txtAfzenderPersoonlijk Formatting
- **Complete Personal Data**: `{FirstName} {LastName}\n{Role}\n{EmailAddress}`
- **Missing Role**: `{FirstName} {LastName}\n{EmailAddress}`
- **Missing EmailAddress**: `{FirstName} {LastName}\n{Role}`
- **Only Name**: `{FirstName} {LastName}`
- **Field Order**: Proper line breaks and formatting

#### txtAfzenderCommissie1 Formatting
- **With Commission**: `"Tweede Kamer der Staten-Generaal | {CommissionName}"`
- **Missing Commission**: `"Tweede Kamer der Staten-Generaal"` (no pipe)
- **Configuration Integration**: Fixed text loaded from config file

#### txtAfzenderCommissie2 Formatting
- **Complete Address**: `"Postbus 20018, 2500 EA Den Haag | Prinses Irenepad 1, 2500 EA Den Haag"`
- **POBox Only**: `"Postbus 20018, 2500 EA Den Haag"`
- **Street Only**: `"Prinses Irenepad 1, 2500 EA Den Haag"`
- **Partial Fields**: Missing ZipCode, City, HouseNumber handled gracefully
- **Empty Fields**: Empty strings omitted from formatting

#### Multiple Content Control Tests
- **Sequential Population**: All three content controls populated in correct order
- **Individual Failures**: Failure in one doesn't stop others
- **Error Collection**: All failures collected for single message bar display

### 4. Props Validation Tests
*Following StepHeader.test.js pattern*

#### Component Props
- **Required Props**: Component handles missing profile/commission data gracefully
- **Data Structure Validation**: 
  - Profile array structure validation
  - Commission array structure validation
  - Invalid data types handled with console warnings

#### Vue Props Validation
- **Type Validation**: Incorrect prop types trigger Vue warnings
- **Required Props**: Missing required props trigger Vue warnings
- **Default Values**: Proper fallback to default values

### 5. Store Tests
*Pinia store functionality*

#### Profile Store Tests
- **Data Loading**: Static profile data loaded correctly
- **Data Filtering**: Invalid profiles filtered out during load
- **API Readiness**: Store structure supports future API integration
- **Getters**: Profile filtering and selection getters work correctly

#### Commission Store Tests
- **Data Loading**: Static commission data loaded correctly
- **Data Filtering**: Invalid commissions filtered out during load
- **Commission Lookup**: getCommissionById function works correctly
- **API Readiness**: Store structure supports future API integration

#### Store Integration Tests
- **Profile-Commission Linking**: Profiles correctly linked to commissions via CommissionId
- **Missing Commission Handling**: Profiles with invalid CommissionId handled gracefully

### 6. Configuration Tests
*Configuration file functionality*

#### Config File Tests
- **Config Loading**: appConfig loads correctly
- **Fixed Text**: commissionPrefix value accessible
- **Extensibility**: Configuration structure supports future additions
- **Default Values**: Proper fallback if config missing

### 7. Integration Test Priorities
*For future implementation discussion*

#### High Priority Integration Tests
1. **Full Step Workflow**: 
   - Profile selection → content control population → step completion
   - Error handling flow → multiple errors → single message bar
   - Step navigation and state management

2. **Content Control Validation Integration**:
   - Missing content controls → step not activated
   - StepManager integration with ProfielAfzender
   - Error message display in StepManager

3. **Store Integration**:
   - Profile and commission stores working together
   - Data validation across store boundaries
   - Error handling in store operations

#### Medium Priority Integration Tests
1. **Configuration Integration**: Config file → component → content control population
2. **Logging Integration**: Console logging in development vs production
3. **Message Bar Integration**: Error collection → message bar display

## Test Data Structure

### Realistic Dutch Government Test Data

#### Test Profiles
```javascript
// tests/data/profileTestData.js
export const validProfiles = [
  {
    FirstName: "Jan",
    LastName: "de Bont", 
    Role: "Sjabloon-ontwikkelaar",
    EmailAddress: "<EMAIL>",
    CommissionId: "infrastructuur-waterstaat"
  },
  {
    FirstName: "Maria",
    LastName: "van der Berg",
    Role: "Commissievoorzitter", 
    EmailAddress: "<EMAIL>",
    CommissionId: "binnenlandse-zaken"
  },
  {
    FirstName: "Pieter",
    LastName: "Jansen",
    Role: "Commissielid",
    EmailAddress: "<EMAIL>", 
    CommissionId: "financien"
  }
]

export const invalidProfiles = [
  {
    FirstName: "",
    LastName: "Empty",
    CommissionId: "test"
  },
  {
    FirstName: "Missing",
    LastName: null,
    CommissionId: "test"
  }
]
```

#### Test Commissions
```javascript
// tests/data/commissionTestData.js
export const validCommissions = [
  {
    id: "infrastructuur-waterstaat",
    CommissionName: "Vaste commissie voor Infrastructuur en Waterstaat",
    POBoxNumber: "Postbus 20018",
    ZipCode: "2500 EA",
    City: "Den Haag", 
    StreetName: "Prinses Irenepad",
    HouseNumber: "1"
  },
  {
    id: "binnenlandse-zaken",
    CommissionName: "Vaste commissie voor Binnenlandse Zaken",
    POBoxNumber: "Postbus 20018",
    ZipCode: "2500 EA", 
    City: "Den Haag",
    StreetName: "Binnenhof",
    HouseNumber: "4"
  }
]
```

## Test File Organization

```
tests/
├── data/
│   ├── profileTestData.js       # Profile test data
│   └── commissionTestData.js    # Commission test data
├── features/
│   ├── steps/
│   │   └── ProfielAfzenderStep.spec.js  # Main component tests
│   └── stores/
│       ├── profileStore.spec.js          # Profile store tests  
│       └── commissionStore.spec.js       # Commission store tests
└── integration/
    └── profielAfzenderIntegration.spec.js # Integration tests
```

## Success Criteria

- All unit tests pass with >90% code coverage
- Data validation prevents invalid data from causing errors
- Error handling follows existing application patterns
- Content control population handles all edge cases
- Integration with existing step workflow seamless
- Performance acceptable with realistic data volumes (50+ profiles)
