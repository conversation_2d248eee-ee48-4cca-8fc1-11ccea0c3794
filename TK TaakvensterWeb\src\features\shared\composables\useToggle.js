import { ref } from 'vue';
import { useLogger } from './useLogger';

/**
 * Composable for toggle functionality
 * @param {boolean} initialState - Initial visibility state
 * @param {string} name - Name for logging purposes
 * @returns {Object} - Toggle state and methods
 */
export function useToggle(initialState = false, name = 'component') {
  const { debug } = useLogger();
  const isVisible = ref(initialState);
  
  function toggle() {
    isVisible.value = !isVisible.value;
    debug(`${name} visibility ${isVisible.value ? 'shown' : 'hidden'}`);
    return isVisible.value;
  }
  
  function show() {
    if (!isVisible.value) {
      isVisible.value = true;
      debug(`${name} shown`);
    }
    return true;
  }
  
  function hide() {
    if (isVisible.value) {
      isVisible.value = false;
      debug(`${name} hidden`);
    }
    return false;
  }
  
  return {
    isVisible,
    toggle,
    show,
    hide
  };
}
