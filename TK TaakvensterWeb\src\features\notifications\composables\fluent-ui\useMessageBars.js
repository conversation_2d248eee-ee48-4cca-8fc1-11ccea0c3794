/**
 * Composable for managing Fluent UI MessageBars
 * Used for critical top-of-page notifications
 */
import { ref, reactive, provide, readonly, computed } from 'vue';
import { notificationSettings } from '../notificationConfig.js';

export function useMessageBars() {
  // MessageBar state management
  const activeMessageBars = reactive(new Map());
  const messageBarCounter = ref(0);

  /**
   * Generates a unique messagebar ID
   * @returns {string} Unique messagebar identifier
   */
  const generateMessageBarId = () => {
    messageBarCounter.value++;
    return `messagebar-${Date.now()}-${messageBarCounter.value}`;
  };

  /**
   * Shows a messagebar notification (top-of-page, critical)
   * @param {string} intent - MessageBar intent (success, error, info, warning)
   * @param {string} message - MessageBar message
   * @param {Object} options - MessageBar options
   * @returns {string} MessageBar ID
   */
  const showMessageBar = (intent, message, options = {}) => {
    const messageBarId = generateMessageBarId();
    
    const messageBarData = {
      id: messageBarId,
      intent,
      message,
      layout: options.layout || 'singleline',
      dismissible: options.dismissible !== false,
      actions: options.actions || [],
      duration: options.duration || notificationSettings.messageBars.defaultDuration,
      persistent: options.persistent || false,
      createdAt: Date.now()
    };
    
    activeMessageBars.set(messageBarId, messageBarData);

    // Auto-dismiss if duration is set
    if (messageBarData.duration > 0) {
      setTimeout(() => {
        if (activeMessageBars.has(messageBarId)) {
          dismissMessageBar(messageBarId);
        }
      }, Math.min(messageBarData.duration, notificationSettings.messageBars.maxDuration));
    }

    // Enforce maximum visible MessageBars (FIFO)
    if (activeMessageBars.size > notificationSettings.messageBars.maxVisible) {
      const oldestId = activeMessageBars.keys().next().value;
      dismissMessageBar(oldestId);
    }

    return messageBarId;
  };

  /**
   * Dismisses a messagebar
   * @param {string} messageBarId - MessageBar ID to dismiss
   */
  const dismissMessageBar = (messageBarId) => {
    activeMessageBars.delete(messageBarId);
  };

  /**
   * Handles messagebar action
   * @param {Object} actionData - Action data with actionId and messageId
   */
  const handleMessageBarAction = (actionData) => {
    const { actionId, messageId } = actionData;
    const messageBar = activeMessageBars.get(messageId);
    
    if (messageBar) {
      const action = messageBar.actions.find(a => a.id === actionId);
      if (action && action.handler) {
        action.handler();
      }
      
      // Auto-dismiss after action unless persistent
      if (!messageBar.persistent) {
        dismissMessageBar(messageId);
      }
    }
  };

  /**
   * Clears all messagebars
   */
  const clearAllMessageBars = () => {
    activeMessageBars.clear();
  };

  // Provide messagebar state for components
  provide('activeMessageBars', readonly(activeMessageBars));
  provide('handleMessageBarDismiss', dismissMessageBar);
  provide('handleMessageBarAction', handleMessageBarAction);

  return {
    // MessageBar methods (top-of-page, critical)
    success: (message, options) => showMessageBar('success', message, options),
    error: (message, options) => showMessageBar('error', message, options),
    info: (message, options) => showMessageBar('info', message, options),
    warning: (message, options) => showMessageBar('warning', message, options),
    show: showMessageBar,
    dismiss: dismissMessageBar,
    clear: clearAllMessageBars,
    // State access
    activeMessageBars: readonly(activeMessageBars),
    activeCount: computed(() => activeMessageBars.size)
  };
}
