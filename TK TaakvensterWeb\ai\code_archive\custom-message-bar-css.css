/* 
 * Archived CSS from MessageBar.vue component
 * Date: 2025-07-22
 * Reason: Commented-out styles removed during code cleanup
 * These styles were alternative implementations for message bar elements
 */

.message-bar-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 4px;
}

.message-bar-icon svg {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.message-bar-actions {
  display: flex;
  gap: 8px;
  margin-top: 4px;
}

.message-bar-action {
  background: transparent;
  border: 1px solid currentColor;
  border-radius: 4px;
  padding: 4px 8px;
  font-size: 12px;
  cursor: pointer;
  color: inherit;
  font-weight: 500;
  min-width: 60px;
  text-align: center;
}

.message-bar-action:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.message-bar-dismiss {
  display: flex;
  align-items: center;
  justify-content: center;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 4px;
  color: inherit;
  opacity: 0.7;
}

.message-bar-dismiss:hover {
  opacity: 1;
}
