/**
 * Test data for commission testing in ProfielAfzender step
 * Contains realistic Dutch government commission data for various test scenarios
 */

export const validCommissions = [
  {
    id: "infrastructuur-waterstaat",
    CommissionName: "Vaste commissie voor Infrastructuur en Waterstaat",
    POBoxNumber: "Postbus 20018",
    ZipCode: "2500 EA",
    City: "Den Haag",
    StreetName: "Prinses Irenepad",
    HouseNumber: "1"
  },
  {
    id: "binnenlandse-zaken",
    CommissionName: "Vaste commissie voor Binnenlandse Zaken",
    POBoxNumber: "Postbus 20018",
    ZipCode: "2500 EA",
    City: "Den Haag",
    StreetName: "Binnenhof",
    HouseNumber: "4"
  },
  {
    id: "financien",
    CommissionName: "Vaste commissie voor Financiën",
    POBoxNumber: "Postbus 20018",
    ZipCode: "2500 EA",
    City: "Den Haag",
    StreetName: "Lange Voorhout",
    HouseNumber: "8"
  }
]

export const partialCommissions = [
  {
    // Only POBox address
    id: "pobox-only",
    CommissionName: "Test Commissie POBox Only",
    POBoxNumber: "Postbus 12345",
    ZipCode: "1234 AB",
    City: "Test City"
  },
  {
    // Only street address
    id: "street-only",
    CommissionName: "Test Commissie Street Only",
    StreetName: "Teststraat",
    HouseNumber: "123",
    ZipCode: "5678 CD",
    City: "Test City"
  },
  {
    // Minimal commission (name only)
    id: "minimal",
    CommissionName: "Test Commissie Minimaal"
  },
  {
    // Missing some address components
    id: "partial-address",
    CommissionName: "Test Commissie Gedeeltelijk Adres",
    StreetName: "Incomplete Street",
    ZipCode: "9999 ZZ"
    // Missing City and HouseNumber
  }
]

export const invalidCommissions = [
  {
    // Missing CommissionName - should be skipped
    id: "invalid-1",
    CommissionName: "",
    POBoxNumber: "Postbus 12345",
    ZipCode: "1234 AB",
    City: "Test City"
  },
  {
    // Null CommissionName - should be skipped
    id: "invalid-2",
    CommissionName: null,
    POBoxNumber: "Postbus 67890",
    ZipCode: "5678 CD",
    City: "Test City"
  },
  {
    // Undefined CommissionName - should be skipped
    id: "invalid-3",
    CommissionName: undefined,
    StreetName: "Test Street",
    HouseNumber: "1"
  },
  {
    // Whitespace only CommissionName - should be skipped
    id: "invalid-4",
    CommissionName: "   \t\n   ",
    POBoxNumber: "Postbus 99999"
  }
]

/**
 * Expected formatted address outputs for testing
 */
export const expectedAddressFormats = {
  completeAddress: {
    commission: validCommissions[0],
    expectedAddress: "Postbus 20018, 2500 EA Den Haag | Prinses Irenepad 1, 2500 EA Den Haag"
  },
  poboxOnly: {
    commission: partialCommissions[0],
    expectedAddress: "Postbus 12345, 1234 AB Test City"
  },
  streetOnly: {
    commission: partialCommissions[1],
    expectedAddress: "Teststraat 123, 5678 CD Test City"
  },
  minimalCommission: {
    commission: partialCommissions[2],
    expectedAddress: ""
  },
  partialAddress: {
    commission: partialCommissions[3],
    expectedAddress: "Incomplete Street, 9999 ZZ"
  }
}

/**
 * Expected commission text formats for txtAfzenderCommissie1
 */
export const expectedCommissionTexts = {
  withCommission: {
    commission: validCommissions[0],
    expectedText: "Tweede Kamer der Staten-Generaal | Vaste commissie voor Infrastructuur en Waterstaat"
  },
  withoutCommission: {
    commission: null,
    expectedText: "Tweede Kamer der Staten-Generaal"
  }
}

/**
 * Test scenarios for commission lookup and validation
 */
export const commissionTestScenarios = {
  validCommissionId: {
    commissionId: "infrastructuur-waterstaat",
    expectedCommission: validCommissions[0],
    shouldFind: true
  },
  invalidCommissionId: {
    commissionId: "non-existent-commission",
    expectedCommission: null,
    shouldFind: false
  },
  nullCommissionId: {
    commissionId: null,
    expectedCommission: null,
    shouldFind: false
  },
  emptyCommissionId: {
    commissionId: "",
    expectedCommission: null,
    shouldFind: false
  }
}
