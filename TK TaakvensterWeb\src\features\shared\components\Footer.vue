<template>
<div class="footer-container ms-bgColor-themeDarker ms-depth-4">
    <span class="ms-font-xl ms-fontColor-white">{{ title }}</span>
  </div>
</template>
<script setup>
const props = defineProps({ title: String });
</script>

<style scoped>
.footer-container {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 2rem;
  padding: 0.25rem 0.5rem;
  display: flex;
  align-items: center;
}
</style>