import { describe, it, expect, beforeEach, vi } from 'vitest'

// Mock useToastify before importing useToasts
vi.mock('../../../src/features/notifications/composables/toast/useToastTemplates.js', () => ({
  useToastTemplates: () => ({
    hasTemplate: () => true,
    compileTemplate: () => '<div>Toast template</div>',
    isLoaded: { value: true },
  }),
}))

// Import after mocking
import { useToasts } from '../../../src/features/notifications/composables/useToasts.js'

describe('useToasts', () => {
  let toasts

  beforeEach(() => {
    // Reset mocks between tests
    vi.clearAllMocks()
    // Create a fresh instance for each test
    toasts = useToasts()
  })

  it('should show success toast', () => {
    const consoleSpy = vi.spyOn(console, 'log')
    const toastId = toasts.success('Success message')
    expect(toastId).toBeTruthy()
    expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('success'))
  })

  it('should show error toast', () => {
    const consoleSpy = vi.spyOn(console, 'log')
    const toastId = toasts.error('Error message')
    expect(toastId).toBeTruthy()
    expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('error'))
  })

  it('should show info toast', () => {
    const consoleSpy = vi.spyOn(console, 'log')
    const toastId = toasts.info('Info message')
    expect(toastId).toBeTruthy()
    expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('info'))
  })

  it('should show warning toast', () => {
    const consoleSpy = vi.spyOn(console, 'log')
    const toastId = toasts.warning('Warning message')
    expect(toastId).toBeTruthy()
    expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('warning'))
  })

  it('should show toast with custom type', () => {
    const consoleSpy = vi.spyOn(console, 'log')
    const toastId = toasts.show('info', 'Show message')
    expect(toastId).toBeTruthy()
    expect(consoleSpy).toHaveBeenCalledWith(expect.stringContaining('info'))
  })

  it('should remove toast by id', () => {
    const toastId = toasts.success('Test message')
    expect(toasts.activeToasts.has(toastId)).toBe(true)
    toasts.remove(toastId)
    expect(toasts.activeToasts.has(toastId)).toBe(false)
  })

  it('should clear all toasts', () => {
    toasts.success('Message 1')
    toasts.success('Message 2')
    expect(toasts.activeCount.value).toBeGreaterThan(0)
    toasts.clear()
    expect(toasts.activeCount.value).toBe(0)
  })

  it('should initialize with empty state and ready status', () => {
    expect(toasts.activeToasts).toBeInstanceOf(Map)
    expect(toasts.activeCount.value).toBe(0)
    expect(toasts.isReady.value).toBe(true)
    expect(toasts.canShowMore.value).toBe(true)
  })
})
