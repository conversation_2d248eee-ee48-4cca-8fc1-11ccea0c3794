// ErrorHandler - Error handling and reporting utilities for Office Add-ins
const E<PERSON><PERSON><PERSON><PERSON><PERSON> = (function() {
    // Centralized error handling function
    function handleError(error, operation = 'operation', details = {}) {
        // Log to console with details
        console.error(`Error during ${operation}:`, error, details);
        
        // Show user-friendly notification
        window.UIUtils.showNotification(
            "Error", 
            `An error occurred while ${operation}. ${error.userMessage || ''}`
        );
    }
    
    // General error handler for async operations
    function errorHandler(error) {
        handleError(error, 'async operation');
    }
    
    // Public API
    return {
        handleError,
        errorHandler
    };
})();

// Make it globally accessible
window.ErrorHandler = ErrorHandler;
