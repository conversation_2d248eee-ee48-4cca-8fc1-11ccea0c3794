// DebugUtils - Debugging tools and utilities for Office Add-ins
const DebugUtils = (function() {
    // Private variables
    let _useCCReplacementMode = true; // CC Replacement Mode ON by default
    
    // Initialize the debug panel
    function initializeDebugPanel() {
        // Initialize debug toggle checkbox
        $('#debugModeToggle').prop('checked', _useCCReplacementMode);
        
        // Add event listener for the debug mode toggle
        $('#debugModeToggle').on('change', function() {
            const isChecked = $(this).is(':checked');
            setCCReplacementMode(isChecked);
        });
        
        // Add event listener for the clear button
        $('#clearDebugBtn').on('click', clearDebugBox);
        
        // Add event listener for the debug tab to toggle the panel
        $('#debugTab').on('click', function() {
            toggleDebugPanel();
        });
        
        // Initial debug message
        clearDebugBox();
        writeToDebugBox('Debug panel initialized');
        console.log('Debug panel initialized');
        
        // Show the debug button after initialization is complete
        setTimeout(function() {
            const debugButton = document.getElementById('debugTab');
            if (debugButton) {
                debugButton.style.display = 'block';
            }
        }, 1000);
    }
    
    // Write a message to the debug box and console
    function writeToDebugBox(message, tag) {
        // Also log to console for better visibility
        if (tag) {
            console.log(`[${tag}] ${message}`);
        } else {
            console.log(message);
        }
        const debugBox = document.getElementById('debugBox');
        if (!debugBox) return;
        
        // Create timestamp
        const now = new Date();
        const timestamp = now.toLocaleTimeString() + '.' + now.getMilliseconds();
        
        // Format the message
        let formattedMessage = `[${timestamp}] ${message}`;
        
        // Add tag if provided
        if (tag) {
            formattedMessage = `[${tag}] ${formattedMessage}`;
        }
        
        // Add the message to the debug box
        const messageElement = document.createElement('div');
        messageElement.textContent = formattedMessage;
        messageElement.className = 'debug-message';
        debugBox.appendChild(messageElement);
        
        // Auto-scroll to bottom
        debugBox.scrollTop = debugBox.scrollHeight;
    }
    
    // Clear the debug box
    function clearDebugBox() {
        const debugBox = document.getElementById('debugBox');
        if (debugBox) {
            debugBox.innerHTML = '';
            writeToDebugBox('Debug log cleared');
            console.log('Debug log cleared');
        }
    }
    
    // Set CC Replacement mode on or off
    function setCCReplacementMode(isEnabled) {
        _useCCReplacementMode = isEnabled;
        writeToDebugBox(`CC Replacement mode ${isEnabled ? 'enabled' : 'disabled'}`);
        console.log(`CC Replacement mode ${isEnabled ? 'enabled' : 'disabled'}`);
    }
    
    // Get current CC Replacement mode state
    function getCCReplacementMode() {
        return _useCCReplacementMode;
    }
    
    // Toggle the debug panel visibility
    function toggleDebugPanel() {
        const panel = document.getElementById('debugPanel');
        if (panel) {
            panel.classList.toggle('visible');
            const state = panel.classList.contains('visible') ? 'opened' : 'closed';
            writeToDebugBox(`Debug panel ${state}`);
            console.log(`Debug panel ${state}`);
        }
    }
    
    // Public API
    return {
        initialize: initializeDebugPanel,
        writeToDebugBox,
        clearDebugBox,
        setCCReplacementMode,
        getCCReplacementMode,
        toggleDebugPanel
    };
})();

// Make it globally accessible
window.DebugUtils = DebugUtils;
