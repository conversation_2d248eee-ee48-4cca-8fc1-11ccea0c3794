import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createP<PERSON>, setActivePinia } from 'pinia'
import ProfielAfzenderStep from '../../../src/features/steps/components/steps/ProfielAfzenderStep.vue'

// Create mock functions
const insertTextInContentControlMock = vi.fn().mockResolvedValue(true)
const hasContentControlMock = vi.fn().mockResolvedValue(true)
const withErrorHandlingMock = vi.fn(async (callback, context, options) => {
  try {
    return await callback()
  } catch (error) {
    throw error
  }
})

// Mock the useOffice composable
vi.mock('../../../src/features/office', () => ({
  useOffice: () => ({
    insertTextInContentControl: insertTextInContentControlMock,
    hasContentControl: hasContentControlMock,
  }),
}))

// Mock the useErrorHandler composable
vi.mock('../../../src/features/shared', () => ({
  useErrorHandler: () => ({
    withErrorHandling: withErrorHandlingMock,
  }),
}))

// Mock the stores
vi.mock('../../../src/features/steps/stores/profileStore', () => ({
  useProfileStore: () => ({
    profilesForDisplay: [
      {
        label: 'Jan de Bont',
        FirstName: 'Jan',
        LastName: 'de Bont',
        Role: 'Developer',
        EmailAddress: '<EMAIL>',
        CommissionId: 'test-commission',
      },
    ],
    profileCount: 1,
    selectProfile: vi.fn().mockReturnValue({
      FirstName: 'Jan',
      LastName: 'de Bont',
      Role: 'Developer',
      EmailAddress: '<EMAIL>',
      CommissionId: 'test-commission',
    }),
    clearSelection: vi.fn(),
    loadProfiles: vi.fn().mockResolvedValue(),
  }),
}))

vi.mock('../../../src/features/steps/stores/commissionStore', () => ({
  useCommissionStore: () => ({
    getFormattedCommissionData: vi.fn().mockReturnValue({
      hasCommission: true,
      commissionName: 'Test Commission',
      formattedAddress: 'Test Address',
    }),
    loadCommissions: vi.fn().mockResolvedValue(),
  }),
}))

// Mock config
vi.mock('../../../src/config/appConfig', () => ({
  getCommissionPrefix: () => 'Tweede Kamer der Staten-Generaal',
  getFormattingConfig: () => ({
    lineSeparator: '\n',
    commissionSeparator: ' | ',
    addressSeparator: ' | ',
  }),
  getRequiredContentControls: () => [
    'txtAfzenderPersoonlijk',
    'txtAfzenderCommissie1',
    'txtAfzenderCommissie2',
  ],
  getStepBehaviorConfig: () => ({
    completionDelay: 500,
  }),
}))

// Mock components
vi.mock('../../../src/features/steps/components/steps/StepHeader.vue', () => ({
  default: {
    props: ['title', 'subTitle'],
    template: '<div>{{ title }} - {{ subTitle }}</div>',
  },
}))

vi.mock('../../../src/features/shared/components/common/FlowbiteList.vue', () => ({
  default: {
    props: ['items', 'modelValue'],
    emits: ['update:modelValue', 'item-click'],
    template:
      '<div class="flowbite-list"><button @click="$emit(\'item-click\', { index: 0 })">Test Item</button></div>',
  },
}))

describe('ProfielAfzenderStep', () => {
  beforeEach(() => {
    // Create a fresh pinia instance for each test
    setActivePinia(createPinia())

    // Reset all mocks before each test
    vi.clearAllMocks()
  })

  it('should render without errors', () => {
    const wrapper = mount(ProfielAfzenderStep)
    expect(wrapper.exists()).toBe(true)
  })

  it('should emit step-ready event on mount', async () => {
    const wrapper = mount(ProfielAfzenderStep)

    // Wait for all async operations to complete
    await new Promise((resolve) => setTimeout(resolve, 100))

    expect(wrapper.emitted('step-ready')).toBeTruthy()
    expect(wrapper.emitted('step-ready').length).toBe(1)
  })

  it('should validate content controls on mount', async () => {
    const wrapper = mount(ProfielAfzenderStep)

    // Wait for all async operations to complete
    await new Promise((resolve) => setTimeout(resolve, 100))

    // Should check for all required content controls
    expect(hasContentControlMock).toHaveBeenCalledWith('txtAfzenderPersoonlijk')
    expect(hasContentControlMock).toHaveBeenCalledWith('txtAfzenderCommissie1')
    expect(hasContentControlMock).toHaveBeenCalledWith('txtAfzenderCommissie2')
  })

  it('should show validation error for invalid selection', async () => {
    const wrapper = mount(ProfielAfzenderStep)

    // Try to process invalid selection
    await wrapper.vm.processProfileSelection(null)

    // Verify that error state is set
    expect(wrapper.vm.errorState.type).toBe('validation')
    expect(wrapper.vm.errorState.message).toBe('Selecteer eerst een profiel voordat u verder gaat.')
    expect(wrapper.vm.errorState.isRecoverable).toBe(true)

    // Verify that insertTextInContentControl was not called
    expect(insertTextInContentControlMock).not.toHaveBeenCalled()
  })

  it('should show commission lookup warning but continue processing', async () => {
    // This test verifies that when a profile has a CommissionId but the commission
    // cannot be found, a warning is shown but processing continues
    const wrapper = mount(ProfielAfzenderStep)

    // Test the commission lookup logic directly
    const mockProfile = {
      FirstName: 'Jan',
      LastName: 'de Bont',
      CommissionId: 'missing-commission-id',
    }

    const mockCommissionData = {
      hasCommission: false,
      commissionName: '',
      formattedAddress: '',
    }

    // Test the condition directly
    const shouldShowWarning = !mockCommissionData.hasCommission && mockProfile.CommissionId
    expect(shouldShowWarning).toBe(true)

    // Test that the error message would be correct
    const expectedMessage = `Commissie kon niet worden gevonden (ID: ${mockProfile.CommissionId}). Alleen persoonlijke gegevens worden ingevoegd.`
    expect(expectedMessage).toContain('Commissie kon niet worden gevonden')
    expect(expectedMessage).toContain('missing-commission-id')

    // Test the setError function directly
    wrapper.vm.setError('commission-lookup', expectedMessage, true, {
      commissionId: mockProfile.CommissionId,
    })

    expect(wrapper.vm.errorState.type).toBe('commission-lookup')
    expect(wrapper.vm.errorState.message).toBe(expectedMessage)
    expect(wrapper.vm.errorState.isRecoverable).toBe(true)
  })

  it('should clear errors when processing new selection', async () => {
    const wrapper = mount(ProfielAfzenderStep)

    // Set an initial error
    wrapper.vm.setError('validation', 'Test error')
    expect(wrapper.vm.errorState.message).toBe('Test error')

    // Process valid selection
    await wrapper.vm.processProfileSelection(0)

    // Verify that error was cleared (assuming successful processing)
    expect(wrapper.vm.errorState.message).toBe('')
  })

  it('should handle Office API errors by rethrowing them', async () => {
    const wrapper = mount(ProfielAfzenderStep)

    // Create an Office API error
    const officeError = new Error('Office API Error: Content control not found')
    officeError.isOfficeError = true

    // Mock insertTextInContentControl to throw Office error
    insertTextInContentControlMock.mockClear()
    insertTextInContentControlMock.mockRejectedValue(officeError)

    // Process selection that will fail with Office error
    await expect(wrapper.vm.processProfileSelection(0)).rejects.toThrow('Office API Error')

    // Verify that selection was reset
    expect(wrapper.vm.selectedProfileIndex).toBeNull()
  })

  it('should process valid profile selection', async () => {
    const wrapper = mount(ProfielAfzenderStep)

    // Reset and configure mocks
    insertTextInContentControlMock.mockClear()
    insertTextInContentControlMock.mockResolvedValue(true)

    withErrorHandlingMock.mockClear()
    withErrorHandlingMock.mockImplementation(async (callback) => {
      return await callback()
    })

    // Process valid selection
    await wrapper.vm.processProfileSelection(0)

    // Verify that content controls were populated
    expect(insertTextInContentControlMock).toHaveBeenCalledWith(
      'txtAfzenderPersoonlijk',
      expect.any(String),
    )
    expect(insertTextInContentControlMock).toHaveBeenCalledWith(
      'txtAfzenderCommissie1',
      expect.any(String),
    )
    expect(insertTextInContentControlMock).toHaveBeenCalledWith(
      'txtAfzenderCommissie2',
      expect.any(String),
    )

    // Verify step completion is emitted after delay
    await new Promise((resolve) => setTimeout(resolve, 600))
    expect(wrapper.emitted('step-complete')).toBeTruthy()
  })

  it('should format personal data correctly', () => {
    const wrapper = mount(ProfielAfzenderStep)
    const formatting = { lineSeparator: '\n' }

    // Test complete profile
    const completeProfile = {
      FirstName: 'Jan',
      LastName: 'de Bont',
      Role: 'Developer',
      EmailAddress: '<EMAIL>',
    }

    const result = wrapper.vm.formatPersonalData(completeProfile, formatting)
    expect(result).toBe('Jan de Bont\nDeveloper\<EMAIL>')
  })

  it('should format personal data with missing optional fields', () => {
    const wrapper = mount(ProfielAfzenderStep)
    const formatting = { lineSeparator: '\n' }

    // Test profile without role
    const profileWithoutRole = {
      FirstName: 'Jan',
      LastName: 'de Bont',
      EmailAddress: '<EMAIL>',
    }

    const result = wrapper.vm.formatPersonalData(profileWithoutRole, formatting)
    expect(result).toBe('Jan de Bont\<EMAIL>')
  })

  it('should format commission text correctly', () => {
    const wrapper = mount(ProfielAfzenderStep)
    const formatting = { commissionSeparator: ' | ' }

    // Test with commission
    const commissionData = {
      hasCommission: true,
      commissionName: 'Test Commission',
    }

    const result = wrapper.vm.formatCommissionText(commissionData, formatting)
    expect(result).toBe('Tweede Kamer der Staten-Generaal | Test Commission')
  })

  it('should format commission text without commission', () => {
    const wrapper = mount(ProfielAfzenderStep)
    const formatting = { commissionSeparator: ' | ' }

    // Test without commission
    const commissionData = {
      hasCommission: false,
    }

    const result = wrapper.vm.formatCommissionText(commissionData, formatting)
    expect(result).toBe('Tweede Kamer der Staten-Generaal')
  })

  it('should handle Office API errors correctly', async () => {
    const wrapper = mount(ProfielAfzenderStep)

    // Configure mocks to simulate error
    const error = new Error('Office API error')
    insertTextInContentControlMock.mockClear()
    insertTextInContentControlMock.mockRejectedValue(error)

    withErrorHandlingMock.mockClear()
    withErrorHandlingMock.mockImplementation(async (callback) => {
      try {
        return await callback()
      } catch (e) {
        throw e
      }
    })

    // Process selection that will fail
    try {
      await wrapper.vm.processProfileSelection(0)
    } catch (e) {
      // Error is expected
    }

    // Verify that step-complete is not emitted
    expect(wrapper.emitted('step-complete')).toBeFalsy()
  })
})
