# Code Migration Guide - Feature-Based Structure

This guide documents the systematic approach for migrating from type-based to feature-based folder structure, including lessons learned from our migration.

## 🎯 Migration Strategy Overview

### Before You Start
1. **Create a backup** of your current codebase
2. **Document current structure** - take screenshots or create a folder tree
3. **Identify all features** and their boundaries
4. **Plan the migration order** - start with least dependent features
5. **Set up task tracking** to monitor progress

## 📋 Step-by-Step Migration Process

### Phase 1: Planning and Preparation

#### 1.1 Analyze Current Structure
```bash
# Generate current folder structure
tree src/ > migration-before.txt

# Find all import statements to understand dependencies
grep -r "import.*from" src/ > current-imports.txt
```

#### 1.2 Define Feature Boundaries
Create a mapping document:
```
Feature: steps
- Components: StepManager.vue, PanelToggle.vue, steps/*
- Composables: useStepConfig.js, useStepLogic.js, usePanelKeyboardShortcuts.js
- Dependencies: office (useOffice), shared (use<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, useLogger)

Feature: notifications  
- Components: ToastContainer.vue, MessageBar/*
- Composables: useToasts.js, useMessageBar/*, fluent-ui/*
- Stores: messageBarStore.js
- Dependencies: office (fluent-ui wrappers)
```

### Phase 2: Feature-by-Feature Migration

**CRITICAL: Complete each feature fully before moving to the next one!**

#### 2.1 Create Feature Folder Structure
```bash
mkdir -p src/features/[feature-name]/components
mkdir -p src/features/[feature-name]/composables  
mkdir -p src/features/[feature-name]/stores
```

#### 2.2 Move Files for One Feature
```bash
# Move all files for the feature at once
mv src/components/StepManager.vue src/features/steps/components/
mv src/components/steps/* src/features/steps/components/steps/
mv src/composables/useStepConfig.js src/features/steps/composables/
```

#### 2.3 **IMMEDIATELY** Find and Update All References
This is the critical step we learned from our migration:

```bash
# Search for ALL references to moved files
grep -r "components/StepManager" src/
grep -r "components/steps/" src/
grep -r "composables/useStepConfig" src/
grep -r "composables/useStepLogic" src/

# Update each reference found:
# OLD: import StepManager from './components/StepManager.vue'
# NEW: import { StepManager } from './features/steps'
```

#### 2.4 Create Feature Index File
```javascript
// src/features/steps/index.js
export { default as StepManager } from './components/StepManager.vue'
export { default as PanelToggle } from './components/PanelToggle.vue'
export { useStepConfig } from './composables/useStepConfig.js'
export { useStepLogic } from './composables/useStepLogic.js'
```

#### 2.5 Test Feature Migration
```bash
# Test build after EACH feature migration
npm run build

# If build fails, fix imports immediately
# Don't proceed to next feature until this one works
```

#### 2.6 Update Internal Feature Imports
Check imports within the feature itself:
```bash
# Look for relative imports that might be broken
grep -r "\.\./\.\./composables" src/features/steps/
grep -r "\.\./\.\./components" src/features/steps/

# Update to use feature boundaries:
# OLD: import { useOffice } from '../../composables/useOffice'  
# NEW: import { useOffice } from '../../office'
```

### Phase 3: Cross-Feature Dependencies

#### 3.1 Identify Cross-Feature Imports
```bash
# Find imports that cross feature boundaries
grep -r "features/[^/]*/.*features/" src/

# These should use the feature's public API (index.js)
```

#### 3.2 Update to Use Feature APIs
```javascript
// BAD - reaching into feature internals
import StepManager from './features/steps/components/StepManager.vue'

// GOOD - using feature's public API  
import { StepManager } from './features/steps'
```

## 🔧 Tools and Commands

### Useful Search Commands
```bash
# Find all import statements
grep -r "import.*from" src/

# Find imports to specific folder
grep -r "import.*components/" src/
grep -r "import.*composables/" src/

# Find relative imports that might be broken
grep -r "import.*\.\./\.\." src/

# Check for old folder references
grep -r "src/components" src/
grep -r "src/composables" src/
```

### Build and Test Commands
```bash
# Quick syntax check
npm run build

# Full development test
npm run dev

# Run tests if available
npm run test
```

## ⚠️ Common Pitfalls and Solutions

### 1. Import Path Errors
**Problem**: `Could not resolve "./useOffice" from "useStepLogic.js"`

**Solution**: Update relative imports to use feature boundaries
```javascript
// Before
import { useOffice } from './useOffice'

// After  
import { useOffice } from '../../office'
```

### 2. Circular Dependencies
**Problem**: Feature A imports from Feature B, which imports from Feature A

**Solution**: Extract shared functionality to `shared` feature
```javascript
// Move common utilities to features/shared
// Both features import from shared instead of each other
```

### 3. Missing Feature Exports
**Problem**: `Cannot find export 'ComponentName' from './features/myfeature'`

**Solution**: Add missing exports to feature's index.js
```javascript
// features/myfeature/index.js
export { default as ComponentName } from './components/ComponentName.vue'
```

### 4. Broken Test Files
**Problem**: Test files can't find components after migration

**Solution**: Update test imports to use new paths
```javascript
// Before
import StepManager from '../components/StepManager.vue'

// After
import { StepManager } from '../features/steps'
```

## 📊 Migration Checklist

### For Each Feature:
- [ ] Create feature folder structure
- [ ] Move all feature files
- [ ] **Search for ALL references to moved files**
- [ ] **Update ALL import paths immediately**
- [ ] Create feature index.js with exports
- [ ] Test build - must pass before continuing
- [ ] Update internal feature imports
- [ ] Test feature functionality

### After All Features:
- [ ] Remove empty old folders
- [ ] Update documentation
- [ ] Run full test suite
- [ ] Update CI/CD if needed
- [ ] Create migration summary document

## 🎓 Lessons Learned

### What Worked Well:
1. **Feature-by-feature approach** - easier to debug issues
2. **Immediate import fixing** - prevents cascading problems
3. **Feature index files** - clean public APIs
4. **Systematic searching** - grep commands catch all references

### What We'd Do Differently:
1. **Fix imports immediately** after each move (not in bulk)
2. **Test build after each feature** (not at the end)
3. **Document dependencies first** (understand the web of imports)
4. **Use automated tools** for import path updates where possible

### Key Success Factors:
- **Patience** - don't rush the process
- **Systematic approach** - follow the checklist religiously  
- **Immediate feedback** - test after each step
- **Good tooling** - use grep, build commands, and IDE features

## 🚀 Future Improvements

### Automation Opportunities:
1. **Script to find all references** to a moved file
2. **Automated import path updates** using AST parsing
3. **Dependency graph visualization** before migration
4. **Migration validation script** to check for broken imports

### Process Improvements:
1. **Smaller batches** - move one component at a time for complex features
2. **Dependency-first migration** - move shared utilities first
3. **Rollback plan** - clear steps to undo if issues arise
4. **Team coordination** - ensure no one else is making changes during migration

This systematic approach ensures a smooth migration with minimal downtime and debugging.
