// Office Integration Feature - Public API
// This file exports all public components and composables from the office feature

// Components
export { default as IncompatibleWordVersionPanel } from './components/IncompatibleWordVersionPanel.vue'

// Composables
export { useOffice } from './composables/useOffice.js'
export { useWordCompatibility } from './composables/useWordCompatibility.js'

// Note: Office-specific utilities and wrappers are available but not exported
// as they are typically used internally within this feature
