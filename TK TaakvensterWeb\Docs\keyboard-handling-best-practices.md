# Keyboard Handling Best Practices Implementation

## Overview

This document outlines the refactored keyboard handling system that follows Vue.js and accessibility best practices, replacing the previous anti-pattern implementation.

## Problems with Previous Implementation

### Major Issues Fixed:
1. **Global Wildcard Handler**: Used `'*'` to capture ALL keystrokes - extremely inefficient
2. **Mixed Responsibilities**: Single component handled both global and local shortcuts
3. **No Event Prevention**: Missing `preventDefault()` calls caused browser conflicts
4. **Accessibility Issues**: No proper ARIA keyboard navigation patterns
5. **Performance Problems**: Every keystroke triggered handler regardless of context
6. **No Context Awareness**: Ran in input fields and other inappropriate contexts

## New Architecture

### 1. Improved `useKeyboardShortcuts.js` Composable

**Key Features:**
- **Context Awareness**: Automatically ignores input fields and contenteditable elements
- **Modifier Key Support**: Handles `ctrl+key`, `alt+key`, `shift+key`, `meta+key` combinations
- **Proper Event Prevention**: Calls `preventDefault()` when handling shortcuts
- **Scoped Debugging**: Each instance has its own scope for better debugging
- **Enable/Disable**: Can be activated/deactivated dynamically
- **Target Filtering**: Custom `shouldHandle` function for additional filtering

**Usage:**
```javascript
const shortcuts = useKeyboardShortcuts({
  shortcuts: {
    'ctrl+s': handleSave,
    'escape': handleEscape,
    '1': selectStep1
  },
  global: true,
  scope: 'my-component',
  shouldHandle: (event) => someCondition
});
```

### 2. New `usePanelShortcuts.js` Composable

**Purpose:** Handles global panel shortcuts that work anywhere in the app.

**Features:**
- Global `'d'` key to toggle panel visibility
- Separated from local panel navigation
- Respects input field contexts automatically

### 3. Refactored `PanelToggle.vue` Component

**Architecture:**
- **Global Shortcuts**: Panel toggle (`'d'` key) via `usePanelShortcuts`
- **Local Shortcuts**: Step navigation only when panel is visible
- **Proper Accessibility**: ARIA-compliant keyboard navigation
- **Focus Management**: Automatic focus handling for keyboard users

**Keyboard Navigation:**
- `d`: Toggle panel visibility (global)
- `1-9`: Select step by number (when panel visible)
- `Arrow Keys`: Navigate between steps
- `Home`: Go to first step
- `End`: Go to last step
- `Escape`: Close panel

## Accessibility Improvements

### ARIA Compliance:
- Proper `role="tablist"` and `role="tab"` attributes
- `aria-selected` and `aria-controls` for screen readers
- `tabindex` management for keyboard navigation
- Focus management with `nextTick()` for smooth transitions

### Keyboard Navigation Patterns:
- Follows WAI-ARIA tab panel patterns
- Arrow key navigation between tabs
- Home/End key support for first/last navigation
- Escape key to close overlay

## Performance Optimizations

### Before:
- Every keystroke triggered global handler
- No context filtering
- Inefficient wildcard matching

### After:
- Context-aware filtering (ignores input fields)
- Scoped handlers only when needed
- Efficient key combination matching
- Dynamic enable/disable based on visibility

## Code Examples

### Global Panel Toggle:
```javascript
// usePanelShortcuts.js
const globalShortcuts = usePanelShortcuts({
  onTogglePanel: toggleVisibility
});
```

### Local Panel Navigation:
```javascript
// PanelToggle.vue
const localShortcuts = computed(() => {
  if (!isVisible.value) return {};
  
  return {
    'arrowleft': handleArrowNavigation,
    'arrowright': handleArrowNavigation,
    'escape': () => isVisible.value = false,
    '1': () => selectStep(0),
    '2': () => selectStep(1)
    // ... etc
  };
});
```

### Context-Aware Handling:
```javascript
const shortcuts = useKeyboardShortcuts({
  shortcuts: localShortcuts.value,
  shouldHandle: (event) => isVisible.value, // Only when panel visible
  scope: 'panel-local'
});
```

## Benefits

### Developer Experience:
- Clear separation of concerns
- Easier to test and maintain
- Better debugging with scoped logging
- Follows Vue.js composition patterns

### User Experience:
- Proper accessibility support
- No interference with typing in inputs
- Consistent keyboard navigation patterns
- Better performance

### Maintainability:
- Modular composables
- Clear responsibility boundaries
- Easy to extend with new shortcuts
- Proper cleanup and lifecycle management

## Migration Guide

### Old Pattern (Anti-pattern):
```javascript
// DON'T DO THIS
const shortcuts = {
  '*': handleAllKeys // Captures EVERYTHING
};
```

### New Pattern (Best Practice):
```javascript
// DO THIS
const shortcuts = {
  'd': handleToggle,
  '1': handleStep1,
  'escape': handleEscape
};

const keyboard = useKeyboardShortcuts({
  shortcuts,
  scope: 'my-component',
  shouldHandle: (event) => myCondition
});
```

## Testing

The new architecture is much easier to test:
- Individual composables can be tested in isolation
- Mock functions can be passed to test callbacks
- Enable/disable functionality can be tested
- Context filtering can be verified

## Future Enhancements

Potential improvements:
- Keyboard shortcut help overlay
- User-customizable shortcuts
- Shortcut conflict detection
- Internationalization support for different keyboard layouts
