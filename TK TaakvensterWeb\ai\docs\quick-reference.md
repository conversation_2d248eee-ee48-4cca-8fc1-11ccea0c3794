# Quick Reference for VBA Developers

This is your cheat sheet for common Vue.js patterns and their VBA equivalents.

## 🚀 Daily Tasks

### Adding a New Step
1. Copy `_StepTemplate.vue` → `YourStep.vue`
2. Edit the template (update title, options, controlTag)
3. Register in `useStepConfig.js`

### Finding Code
- **Steps**: `/src/components/steps/`
- **Shared Logic**: `/src/composables/`
- **Step Registration**: `/src/composables/useStepConfig.js`

## 💻 Code Patterns

### Variables and Data

| VBA | Vue | Notes |
|-----|-----|-------|
| `Dim name As String` | `const name = ref('')` | Reactive variable |
| `name = "<PERSON>"` | `name.value = "John"` | Setting value |
| `MsgBox name` | `console.log(name.value)` | Reading value |

### Collections and Arrays

| VBA | Vue | Notes |
|-----|-----|-------|
| `Dim items As Collection` | `const items = ref([])` | Empty list |
| `items.Add "Item1"` | `items.value.push("Item1")` | Add item |
| `items.Count` | `items.value.length` | Get count |
| `For Each item In items` | `items.value.forEach(item => {})` | Loop through |

### Form Events

| VBA | Vue | Notes |
|-----|-----|-------|
| `UserForm_Initialize()` | `onMounted(() => {})` | Form loads |
| `CommandButton1_Click()` | `@click="handleClick"` | Button click |
| `TextBox1_Change()` | `@input="handleInput"` | Text changes |
| `ListBox1_Click()` | `@item-click="handleClick"` | List selection |

### Conditional Logic

| VBA | Vue Template | Notes |
|-----|--------------|-------|
| `If condition Then` | `v-if="condition"` | Show/hide elements |
| `Control.Visible = False` | `v-show="false"` | Toggle visibility |
| `Select Case value` | `v-if / v-else-if / v-else` | Multiple conditions |

## 🎯 Common Step Patterns

### Basic Step Structure
```vue
<template>
  <div id="panel-yourname">
    <StepHeader :title="stepTitle" :subTitle="stepDescription" />
    <FlowbiteList :items="stepOptions" @item-click="handleItemClick" />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import StepHeader from './StepHeader.vue'
import FlowbiteList from '../common/FlowbiteList.vue'
import { useOffice } from '../../composables/useOffice'
import { useErrorHandler } from '../../composables/useErrorHandler'

const stepTitle = 'Your Step Title'
const stepDescription = 'Description of what this step does'

const stepOptions = ref([
  { label: 'Option 1', value: 'value1', controlTag: 'txtYourControl' },
  { label: 'Option 2', value: 'value2', controlTag: 'txtYourControl' }
])

const emit = defineEmits(['step-ready', 'step-complete'])
const { insertTextInContentControl } = useOffice()
const { withErrorHandling } = useErrorHandler()

onMounted(() => {
  emit('step-ready')
})

async function handleItemClick(event) {
  const selectedItem = stepOptions.value[event.index]
  
  await withErrorHandling(
    async () => {
      await insertTextInContentControl(selectedItem.controlTag, selectedItem.value)
      emit('step-complete')
    },
    'inserting selection',
    {
      successMessage: `${selectedItem.label} inserted successfully!`
    }
  )
}
</script>
```

### Step with Custom Input
```vue
<template>
  <div id="panel-custom">
    <StepHeader :title="stepTitle" :subTitle="stepDescription" />
    <FlowbiteList :items="stepOptions" @item-click="handleItemClick" />
    
    <!-- Custom input section -->
    <div class="mt-4 p-4 border-t">
      <input 
        v-model="customText" 
        type="text" 
        placeholder="Enter custom value..."
        class="w-full p-2 border rounded"
      />
      <button 
        @click="handleCustomInput"
        class="mt-2 px-4 py-2 bg-blue-500 text-white rounded"
      >
        Use Custom Value
      </button>
    </div>
  </div>
</template>

<script setup>
// Add to your existing script:
const customText = ref('')

async function handleCustomInput() {
  if (!customText.value.trim()) return
  
  await withErrorHandling(
    async () => {
      await insertTextInContentControl('txtYourControl', customText.value)
      emit('step-complete')
    },
    'inserting custom value'
  )
}
</script>
```

## 🔧 Word Integration

### Insert Text into Document
```javascript
// VBA equivalent: Selection.TypeText "Hello"
await insertTextInContentControl('txtControlName', 'Hello')
```

### Error Handling
```javascript
// VBA equivalent: On Error GoTo ErrorHandler
await withErrorHandling(
  async () => {
    // Your Word operation here
    await insertTextInContentControl('txtControl', 'value')
  },
  'description of what you are doing',
  {
    userMessage: 'User-friendly error message',
    successMessage: 'Success message'
  }
)
```

## 🎨 Styling Classes

### Common CSS Classes (Tailwind)
```html
<!-- Layout -->
<div class="flex">                    <!-- Display: flex -->
<div class="grid grid-cols-2">        <!-- 2-column grid -->
<div class="w-full">                  <!-- Width: 100% -->
<div class="h-screen">                <!-- Height: 100vh -->

<!-- Spacing -->
<div class="p-4">                     <!-- Padding: 1rem -->
<div class="m-4">                     <!-- Margin: 1rem -->
<div class="mt-4">                    <!-- Margin-top: 1rem -->
<div class="mb-2">                    <!-- Margin-bottom: 0.5rem -->

<!-- Colors -->
<div class="bg-blue-500">             <!-- Blue background -->
<div class="text-white">              <!-- White text -->
<div class="border">                  <!-- Border -->

<!-- Interactive -->
<button class="hover:bg-blue-600">    <!-- Hover effect -->
<button class="disabled:bg-gray-300"> <!-- Disabled state -->
```

## 🐛 Debugging

### Browser Console (F12)
```javascript
// Check if Office is available
console.log('Office available:', window.Office !== undefined)

// Check current step
console.log('Current step:', getCurrentStep())

// Check step configuration
console.log('All steps:', useStepConfig())

// Check reactive data
console.log('Step options:', stepOptions.value)
```

### Common Error Messages
- **"Cannot read property of undefined"** → Check if data is loaded
- **"Office is not defined"** → Word add-in not loaded properly
- **"Content control not found"** → Check controlTag name matches Word template

## 📁 File Locations

### Key Files to Know
```
src/
├── components/steps/
│   ├── _StepTemplate.vue           ← Copy this for new steps
│   └── [YourStep].vue              ← Your step files
├── composables/
│   ├── useStepConfig.js            ← Register steps here
│   ├── useOffice.js                ← Word integration
│   └── useErrorHandler.js          ← Error handling
└── App.vue                         ← Main application
```

### Import Statements
```javascript
// Standard step imports
import { ref, onMounted } from 'vue'
import StepHeader from './StepHeader.vue'
import FlowbiteList from '../common/FlowbiteList.vue'
import { useOffice } from '../../composables/useOffice'
import { useErrorHandler } from '../../composables/useErrorHandler'
```

## 🚨 Emergency Fixes

### Step Not Showing
1. Check if registered in `useStepConfig.js`
2. Check browser console for errors
3. Verify import path is correct

### Word Integration Not Working
1. Check if Word document is open
2. Verify content control name matches exactly
3. Check browser console for Office errors

### Styling Issues
1. Check if CSS classes are spelled correctly
2. Add `scoped` to `<style>` section
3. Use browser DevTools to inspect elements

## 📚 Need More Help?

- [Creating New Steps](./creating-new-steps.md) - Detailed step creation guide
- [Common Patterns](./common-patterns.md) - More code examples
- [Troubleshooting](./troubleshooting.md) - Problem-solving guide
- [Vue vs VBA Comparison](./vue-vs-vba-comparison.md) - Concept mapping
