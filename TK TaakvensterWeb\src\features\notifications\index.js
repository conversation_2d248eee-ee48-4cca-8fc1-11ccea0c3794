// Notifications Feature - Public API
// This file exports all public components and composables from the notifications feature

// Components
export { default as ToastContainer } from './components/ToastContainer.vue'
export { default as ToastTemplate } from './components/ToastTemplate.vue'

// Message Bar Components
export { default as MessageBar } from './components/MessageBar/MessageBar.vue'
export { default as MessageBarList } from './components/MessageBar/MessageBarList.vue'

// Composables
export { useToasts } from './composables/useToasts.js'
export { useMessageBars } from './composables/fluent-ui/useMessageBars.js'

// Re-export MessageBar index for backward compatibility
export * from './components/MessageBar/index.js'

// Stores
export { useMessageBarStore } from './stores/messageBarStore.js'
