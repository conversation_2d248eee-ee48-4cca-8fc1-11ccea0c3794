<!DOCTYPE html>
<html lang="en" class="h-full">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Toast & MessageBar System Test</title>
    <!-- Tailwind CSS from CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Vue 3 from CDN -->
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <!-- Toastify.js from CDN -->
    <script src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
    <style>
        /* Override Toastify default styles */
        .toastify {
            background: transparent !important;
            box-shadow: none !important;
            padding: 0 !important;
            margin: 0 0 8px 0 !important;
            border-radius: 0 !important;
        }
        
        /* Hide default Toastify close button */
        .toastify .toast-close {
            display: none !important;
        }
        
        /* Custom toast container positioning */
        .toastify-custom {
            background: transparent !important;
        }
        
        /* MessageBar Styles */
        .messagebar-container {
            position: sticky;
            top: 0;
            z-index: 1000;
            width: 100%;
        }
        
        .fluent-messagebar {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 16px;
            border-radius: 6px;
            border-left: 4px solid;
            font-family: 'Segoe UI', system-ui, sans-serif;
            font-size: 14px;
            line-height: 1.4;
            margin-bottom: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            animation: slideDown 0.3s ease;
        }
        
        .messagebar-content {
            display: flex;
            align-items: center;
            gap: 8px;
            flex: 1;
        }
        
        .messagebar-icon {
            font-size: 16px;
            font-weight: 600;
            flex-shrink: 0;
        }
        
        .messagebar-text {
            flex: 1;
            word-wrap: break-word;
        }
        
        .messagebar-actions {
            display: flex;
            gap: 8px;
            align-items: center;
        }
        
        .messagebar-dismiss {
            background: transparent;
            border: none;
            cursor: pointer;
            padding: 4px;
            border-radius: 4px;
            color: inherit;
            opacity: 0.7;
            transition: opacity 0.2s;
        }
        
        .messagebar-dismiss:hover {
            opacity: 1;
            background: rgba(0, 0, 0, 0.05);
        }
        
        /* Intent-based styling */
        .messagebar-success {
            background: #f3f9f1;
            border-color: #0e700e;
            color: #0e700e;
        }
        
        .messagebar-error {
            background: #fdf3f4;
            border-color: #c50e1f;
            color: #c50e1f;
        }
        
        .messagebar-warning {
            background: #fffef5;
            border-color: #8a6c00;
            color: #8a6c00;
        }
        
        .messagebar-info {
            background: #f5f9ff;
            border-color: #0e4775;
            color: #0e4775;
        }
        
        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-100%);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .messagebar-action-btn {
            padding: 4px 8px;
            font-size: 12px;
            border: 1px solid currentColor;
            background: transparent;
            color: inherit;
            border-radius: 4px;
            cursor: pointer;
            transition: background 0.2s;
        }
        
        .messagebar-action-btn:hover {
            background: rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body class="h-full bg-gray-50">
    <div id="test-app" class="h-full">
        <!-- Header -->
        <header class="bg-white shadow-sm border-b">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-4">
                    <h1 class="text-2xl font-bold text-gray-900">Toast & MessageBar System Test</h1>
                    <div class="flex items-center space-x-4">
                        <span class="text-sm text-gray-500">Active Toasts: {{ activeToastCount }} | MessageBars: {{ activeMessageBarCount }}</span>
                        <div class="flex space-x-2">
                            <button 
                                @click="clearAllToasts"
                                class="px-3 py-1 text-sm bg-red-100 text-red-700 rounded-md hover:bg-red-200 transition-colors"
                            >
                                Clear Toasts
                            </button>
                            <button 
                                @click="clearAllMessageBars"
                                class="px-3 py-1 text-sm bg-orange-100 text-orange-700 rounded-md hover:bg-orange-200 transition-colors"
                            >
                                Clear MessageBars
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </header>

        <!-- MessageBar Container - Top of page for critical notifications -->
        <div id="messagebar-container" class="messagebar-container"></div>

        <!-- Main Content -->
        <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Test Controls -->
                <div class="space-y-6">
                    <div class="bg-white rounded-lg shadow p-6">
                        <h2 class="text-lg font-semibold mb-4">Toast Controls</h2>
                        
                        <!-- Message Input -->
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Custom Message</label>
                            <input 
                                v-model="customMessage"
                                type="text" 
                                placeholder="Enter custom toast message..."
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                            >
                        </div>

                        <!-- Duration Control -->
                        <div class="mb-6">
                            <label class="block text-sm font-medium text-gray-700 mb-2">
                                Duration: {{ duration }}ms
                            </label>
                            <input 
                                v-model="duration"
                                type="range" 
                                min="1000" 
                                max="10000" 
                                step="500"
                                class="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                            >
                        </div>

                        <!-- Toast Type Buttons -->
                        <div class="grid grid-cols-1 sm:grid-cols-3 gap-3 mb-6">
                            <button 
                                @click="showSuccessToast"
                                class="flex items-center justify-center px-4 py-3 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors font-medium"
                            >
                                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                Success
                            </button>
                            
                            <button 
                                @click="showErrorToast"
                                class="flex items-center justify-center px-4 py-3 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors font-medium"
                            >
                                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                                </svg>
                                Error
                            </button>
                            
                            <button 
                                @click="showInfoToast"
                                class="flex items-center justify-center px-4 py-3 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors font-medium"
                            >
                                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                </svg>
                                Info
                            </button>
                        </div>

                        <!-- Stress Test -->
                        <div class="border-t pt-4">
                            <h3 class="text-sm font-medium text-gray-700 mb-3">Stress Testing</h3>
                            <div class="flex space-x-3">
                                <button 
                                    @click="spamToasts"
                                    class="px-4 py-2 bg-yellow-100 text-yellow-700 rounded-md hover:bg-yellow-200 transition-colors text-sm"
                                >
                                    Spam 5 Toasts
                                </button>
                                <button 
                                    @click="testMaxLimit"
                                    class="px-4 py-2 bg-purple-100 text-purple-700 rounded-md hover:bg-purple-200 transition-colors text-sm"
                                >
                                    Test Max Limit
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- MessageBar Controls -->
                    <div class="bg-white rounded-lg shadow p-6">
                        <h2 class="text-lg font-semibold mb-4">MessageBar Controls</h2>
                        
                        <!-- MessageBar Type Buttons -->
                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-3 mb-6">
                            <button 
                                @click="showSuccessMessageBar"
                                class="flex items-center justify-center px-4 py-3 bg-green-100 text-green-700 rounded-lg hover:bg-green-200 transition-colors font-medium"
                            >
                                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                Success MessageBar
                            </button>
                            <button 
                                @click="showErrorMessageBar"
                                class="flex items-center justify-center px-4 py-3 bg-red-100 text-red-700 rounded-lg hover:bg-red-200 transition-colors font-medium"
                            >
                                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                                </svg>
                                Error MessageBar
                            </button>
                            <button 
                                @click="showWarningMessageBar"
                                class="flex items-center justify-center px-4 py-3 bg-yellow-100 text-yellow-700 rounded-lg hover:bg-yellow-200 transition-colors font-medium"
                            >
                                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                </svg>
                                Warning MessageBar
                            </button>
                            <button 
                                @click="showInfoMessageBar"
                                class="flex items-center justify-center px-4 py-3 bg-blue-100 text-blue-700 rounded-lg hover:bg-blue-200 transition-colors font-medium"
                            >
                                <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                </svg>
                                Info MessageBar
                            </button>
                        </div>

                        <!-- MessageBar with Actions -->
                        <div class="border-t pt-4">
                            <h3 class="text-sm font-medium text-gray-700 mb-3">MessageBar with Actions</h3>
                            <div class="flex flex-wrap gap-2">
                                <button 
                                    @click="showMessageBarWithActions"
                                    class="px-4 py-2 bg-indigo-100 text-indigo-700 rounded-md hover:bg-indigo-200 transition-colors text-sm"
                                >
                                    Error with Retry Action
                                </button>
                                <button 
                                    @click="showPersistentMessageBar"
                                    class="px-4 py-2 bg-purple-100 text-purple-700 rounded-md hover:bg-purple-200 transition-colors text-sm"
                                >
                                    Persistent Warning
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Debug Panel -->
                    <div class="bg-white rounded-lg shadow p-6">
                        <h2 class="text-lg font-semibold mb-4">Debug Information</h2>
                        <div class="space-y-3">
                            <!-- Toast Statistics -->
                            <div class="border-b pb-3">
                                <h3 class="text-sm font-medium text-gray-700 mb-2">Toast Statistics</h3>
                                <div class="space-y-2">
                                    <div class="flex justify-between text-sm">
                                        <span class="text-gray-600">Active Toasts:</span>
                                        <span class="font-mono">{{ activeToastCount }}</span>
                                    </div>
                                    <div class="flex justify-between text-sm">
                                        <span class="text-gray-600">Total Created:</span>
                                        <span class="font-mono">{{ totalCreated }}</span>
                                    </div>
                                    <div class="flex justify-between text-sm">
                                        <span class="text-gray-600">Auto Dismissed:</span>
                                        <span class="font-mono">{{ autoDismissed }}</span>
                                    </div>
                                    <div class="flex justify-between text-sm">
                                        <span class="text-gray-600">Manually Closed:</span>
                                        <span class="font-mono">{{ manuallyClosed }}</span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- MessageBar Statistics -->
                            <div>
                                <h3 class="text-sm font-medium text-gray-700 mb-2">MessageBar Statistics</h3>
                                <div class="space-y-2">
                                    <div class="flex justify-between text-sm">
                                        <span class="text-gray-600">Active MessageBars:</span>
                                        <span class="font-mono">{{ activeMessageBarCount }}</span>
                                    </div>
                                    <div class="flex justify-between text-sm">
                                        <span class="text-gray-600">Total MessageBars:</span>
                                        <span class="font-mono">{{ totalMessageBarsCreated }}</span>
                                    </div>
                                    <div class="flex justify-between text-sm">
                                        <span class="text-gray-600">Dismissed:</span>
                                        <span class="font-mono">{{ messageBarsDismissed }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Toast Preview Area -->
                <div class="space-y-6">
                    <div class="bg-white rounded-lg shadow p-6">
                        <h2 class="text-lg font-semibold mb-4">Toast Templates Preview</h2>
                        <div class="space-y-4">
                            <!-- Success Template Preview -->
                            <div class="p-4 border border-green-200 rounded-lg">
                                <h3 class="text-sm font-medium text-gray-700 mb-2">Success Toast</h3>
                                <div class="flex items-center w-full max-w-xs p-4 text-gray-500 bg-white rounded-lg shadow">
                                    <div class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 text-green-500 bg-green-100 rounded-lg">
                                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-3 text-sm font-normal">Success message here</div>
                                    <button type="button" class="ml-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex h-8 w-8">
                                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>

                            <!-- Error Template Preview -->
                            <div class="p-4 border border-red-200 rounded-lg">
                                <h3 class="text-sm font-medium text-gray-700 mb-2">Error Toast</h3>
                                <div class="flex items-center w-full max-w-xs p-4 text-gray-500 bg-white rounded-lg shadow">
                                    <div class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 text-red-500 bg-red-100 rounded-lg">
                                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-3 text-sm font-normal">Error message here</div>
                                    <button type="button" class="ml-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex h-8 w-8">
                                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>

                            <!-- Info Template Preview -->
                            <div class="p-4 border border-blue-200 rounded-lg">
                                <h3 class="text-sm font-medium text-gray-700 mb-2">Info Toast</h3>
                                <div class="flex items-center w-full max-w-xs p-4 text-gray-500 bg-white rounded-lg shadow">
                                    <div class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 text-blue-500 bg-blue-100 rounded-lg">
                                        <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                            <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                                        </svg>
                                    </div>
                                    <div class="ml-3 text-sm font-normal">Info message here</div>
                                    <button type="button" class="ml-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex h-8 w-8">
                                        <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- Toast Container -->
        <div id="toast-container" class="fixed top-4 right-4 z-50 space-y-2"></div>
    </div>

    <!-- Hidden templates for Toastify.js -->
    <div style="display: none;">
        <!-- Success Template -->
        <div id="success-template">
            <div class="flex items-center w-full max-w-xs p-4 text-gray-500 bg-white rounded-lg shadow">
                <div class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 text-green-500 bg-green-100 rounded-lg">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div class="ml-3 text-sm font-normal toast-message">{{message}}</div>
                <button type="button" data-toast-close="{{toastId}}" class="ml-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex h-8 w-8 transition-colors" aria-label="Close">
                    <span class="sr-only">Close</span>
                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>
        
        <!-- Error Template -->
        <div id="error-template">
            <div class="flex items-center w-full max-w-xs p-4 text-gray-500 bg-white rounded-lg shadow">
                <div class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 text-red-500 bg-red-100 rounded-lg">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div class="ml-3 text-sm font-normal toast-message">{{message}}</div>
                <button type="button" data-toast-close="{{toastId}}" class="ml-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex h-8 w-8 transition-colors" aria-label="Close">
                    <span class="sr-only">Close</span>
                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>
        
        <!-- Info Template -->
        <div id="info-template">
            <div class="flex items-center w-full max-w-xs p-4 text-gray-500 bg-white rounded-lg shadow">
                <div class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 text-blue-500 bg-blue-100 rounded-lg">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div class="ml-3 text-sm font-normal toast-message">{{message}}</div>
                <button type="button" data-toast-close="{{toastId}}" class="ml-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex h-8 w-8 transition-colors" aria-label="Close">
                    <span class="sr-only">Close</span>
                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>
        
        <!-- Warning Template -->
        <div id="warning-template">
            <div class="flex items-center w-full max-w-xs p-4 text-gray-500 bg-white rounded-lg shadow">
                <div class="inline-flex items-center justify-center flex-shrink-0 w-8 h-8 text-orange-500 bg-orange-100 rounded-lg">
                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                        <path fill-rule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                    </svg>
                </div>
                <div class="ml-3 text-sm font-normal toast-message">{{message}}</div>
                <button type="button" data-toast-close="{{toastId}}" class="ml-auto -mx-1.5 -my-1.5 bg-white text-gray-400 hover:text-gray-900 rounded-lg focus:ring-2 focus:ring-gray-300 p-1.5 hover:bg-gray-100 inline-flex h-8 w-8 transition-colors" aria-label="Close">
                    <span class="sr-only">Close</span>
                    <svg class="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                </button>
            </div>
        </div>
    </div>

    <script>
        const { createApp, ref, computed } = Vue;

        // Toast Renderer using Toastify.js
        class ToastRenderer {
            constructor() {
                this.activeToasts = new Map();
                this.maxToasts = 3;
                this.defaultDuration = 5000;
                this.templates = new Map();
                this.toastCounter = 0;
                this.loadTemplates();
            }

            loadTemplates() {
                const templateTypes = ['success', 'error', 'info', 'warning'];
                templateTypes.forEach(type => {
                    const templateElement = document.getElementById(`${type}-template`);
                    if (templateElement) {
                        const templateHTML = templateElement.innerHTML.trim();
                        this.templates.set(type, templateHTML);
                    }
                });
            }

            generateToastId() {
                return `toast-${Date.now()}-${++this.toastCounter}`;
            }

            compileTemplate(type, message, toastId) {
                const template = this.templates.get(type);
                if (!template) return null;
                return template
                    .replace(/\{\{message\}\}/g, this.escapeHtml(message))
                    .replace(/\{\{toastId\}\}/g, toastId);
            }

            escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }

            enforceMaxLimit() {
                while (this.activeToasts.size >= this.maxToasts) {
                    const oldestId = this.activeToasts.keys().next().value;
                    const oldestToast = this.activeToasts.get(oldestId);
                    if (oldestToast && oldestToast.toastifyInstance) {
                        oldestToast.toastifyInstance.hideToast();
                    }
                    this.activeToasts.delete(oldestId);
                }
            }

            showToast(type, message, options = {}) {
                this.enforceMaxLimit();

                const toastId = this.generateToastId();
                const duration = options.duration !== undefined ? options.duration : this.defaultDuration;
                const persistent = options.persistent || false;

                const toastHTML = this.compileTemplate(type, message, toastId);
                if (!toastHTML) return null;

                const toastifyInstance = Toastify({
                    text: toastHTML,
                    duration: persistent ? -1 : duration,
                    gravity: "top",
                    position: "right",
                    stopOnFocus: true,
                    escapeMarkup: false,
                    className: "toastify-custom",
                    destination: "#toast-container",
                    style: {
                        background: "transparent",
                        boxShadow: "none",
                        padding: "0",
                        margin: "0 0 8px 0"
                    },
                    onClick: function() {},
                    callback: () => {
                        this.activeToasts.delete(toastId);
                        this.emitToastEvent('toast-removed', {
                            toastId,
                            isManual: false,
                            type,
                            message
                        });
                    }
                });

                toastifyInstance.showToast();

                this.activeToasts.set(toastId, {
                    toastifyInstance,
                    type,
                    message,
                    persistent,
                    createdAt: Date.now()
                });

                setTimeout(() => {
                    const closeButton = document.querySelector(`[data-toast-close="${toastId}"]`);
                    if (closeButton) {
                        closeButton.addEventListener('click', (e) => {
                            e.preventDefault();
                            e.stopPropagation();
                            this.removeToast(toastId, true);
                        });
                    }
                }, 100);

                return toastId;
            }

            removeToast(toastId, isManual = false) {
                const toastData = this.activeToasts.get(toastId);
                if (!toastData) return;

                if (toastData.toastifyInstance) {
                    toastData.toastifyInstance.hideToast();
                }

                this.activeToasts.delete(toastId);

                this.emitToastEvent('toast-removed', {
                    toastId,
                    isManual,
                    type: toastData.type,
                    message: toastData.message
                });
            }

            clearAllToasts() {
                const toastIds = Array.from(this.activeToasts.keys());
                toastIds.forEach(id => this.removeToast(id, true));
            }

            emitToastEvent(eventName, detail) {
                const event = new CustomEvent(eventName, {
                    detail,
                    bubbles: true,
                    cancelable: true
                });
                document.dispatchEvent(event);
            }

            success(message, options) {
                return this.showToast('success', message, options);
            }

            error(message, options) {
                return this.showToast('error', message, options);
            }

            info(message, options) {
                return this.showToast('info', message, options);
            }

            warning(message, options) {
                return this.showToast('warning', message, options);
            }

            get activeCount() {
                return this.activeToasts.size;
            }
        }

        // MessageBar Manager Class
        class MessageBarManager {
            constructor() {
                this.activeMessageBars = new Map();
                this.messageBarCounter = 0;
                this.container = document.getElementById('messagebar-container');
            }

            generateId() {
                this.messageBarCounter++;
                return `messagebar-${Date.now()}-${this.messageBarCounter}`;
            }

            showMessageBar(intent, message, options = {}) {
                const messageBarId = this.generateId();
                const { actions = [], persistent = false, duration = 0 } = options;

                const iconMap = {
                    success: '✓',
                    error: '✕',
                    warning: '⚠',
                    info: 'ℹ'
                };

                // Create MessageBar HTML
                const messageBarHTML = `
                    <div class="fluent-messagebar messagebar-${intent}" data-messagebar-id="${messageBarId}">
                        <div class="messagebar-content">
                            <span class="messagebar-icon">${iconMap[intent] || iconMap.info}</span>
                            <span class="messagebar-text">${this.escapeHtml(message)}</span>
                        </div>
                        ${actions.length > 0 ? `
                            <div class="messagebar-actions">
                                ${actions.map(action => `
                                    <button class="messagebar-action-btn" data-action-id="${action.id}">
                                        ${this.escapeHtml(action.text)}
                                    </button>
                                `).join('')}
                            </div>
                        ` : ''}
                        <button class="messagebar-dismiss" data-messagebar-dismiss="${messageBarId}">
                            ✕
                        </button>
                    </div>
                `;

                // Insert into container
                const messageBarElement = document.createElement('div');
                messageBarElement.innerHTML = messageBarHTML;
                const messageBar = messageBarElement.firstElementChild;
                this.container.appendChild(messageBar);

                // Store reference
                this.activeMessageBars.set(messageBarId, {
                    element: messageBar,
                    intent,
                    message,
                    persistent,
                    actions,
                    createdAt: Date.now()
                });

                // Set up event listeners
                this.setupEventListeners(messageBarId, actions);

                // Auto-dismiss if not persistent
                if (!persistent && duration > 0) {
                    setTimeout(() => {
                        this.dismissMessageBar(messageBarId);
                    }, duration);
                }

                // Emit event
                this.emitEvent('messagebar-created', { messageBarId, intent, message });

                return messageBarId;
            }

            setupEventListeners(messageBarId, actions) {
                const messageBar = this.activeMessageBars.get(messageBarId);
                if (!messageBar) return;

                // Dismiss button
                const dismissBtn = messageBar.element.querySelector(`[data-messagebar-dismiss="${messageBarId}"]`);
                if (dismissBtn) {
                    dismissBtn.addEventListener('click', () => {
                        this.dismissMessageBar(messageBarId, true);
                    });
                }

                // Action buttons
                actions.forEach(action => {
                    const actionBtn = messageBar.element.querySelector(`[data-action-id="${action.id}"]`);
                    if (actionBtn) {
                        actionBtn.addEventListener('click', () => {
                            if (action.handler) {
                                action.handler();
                            }
                            this.emitEvent('messagebar-action', { messageBarId, actionId: action.id });
                            
                            // Auto-dismiss after action unless persistent
                            if (!messageBar.persistent) {
                                this.dismissMessageBar(messageBarId);
                            }
                        });
                    }
                });
            }

            dismissMessageBar(messageBarId, isManual = false) {
                const messageBar = this.activeMessageBars.get(messageBarId);
                if (!messageBar) return;

                // Animate out
                messageBar.element.style.animation = 'slideUp 0.3s ease forwards';
                
                setTimeout(() => {
                    if (messageBar.element.parentNode) {
                        messageBar.element.parentNode.removeChild(messageBar.element);
                    }
                    this.activeMessageBars.delete(messageBarId);
                    
                    this.emitEvent('messagebar-dismissed', {
                        messageBarId,
                        isManual,
                        intent: messageBar.intent,
                        message: messageBar.message
                    });
                }, 300);
            }

            clearAllMessageBars() {
                const messageBarIds = Array.from(this.activeMessageBars.keys());
                messageBarIds.forEach(id => this.dismissMessageBar(id));
            }

            escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }

            emitEvent(eventName, detail) {
                document.dispatchEvent(new CustomEvent(eventName, { detail }));
            }

            // Convenience methods
            success(message, options) {
                return this.showMessageBar('success', message, options);
            }

            error(message, options) {
                return this.showMessageBar('error', message, options);
            }

            info(message, options) {
                return this.showMessageBar('info', message, options);
            }

            warning(message, options) {
                return this.showMessageBar('warning', message, options);
            }

            get activeCount() {
                return this.activeMessageBars.size;
            }
        }

        // Add slideUp animation
        const style = document.createElement('style');
        style.textContent = `
            @keyframes slideUp {
                from {
                    opacity: 1;
                    transform: translateY(0);
                }
                to {
                    opacity: 0;
                    transform: translateY(-100%);
                }
            }
        `;
        document.head.appendChild(style);

        // Initialize renderers
        const toastRenderer = new ToastRenderer();
        const messageBarManager = new MessageBarManager();

        createApp({
            setup() {
                // Reactive state
                const customMessage = ref('Test toast message');
                const duration = ref(5000);
                const activeToastCount = ref(0);
                const totalCreated = ref(0);
                const autoDismissed = ref(0);
                const manuallyClosed = ref(0);
                
                // MessageBar reactive state
                const activeMessageBarCount = ref(0);
                const totalMessageBarsCreated = ref(0);
                const messageBarsDismissed = ref(0);

                // Listen for toast events
                document.addEventListener('toast-removed', (event) => {
                    activeToastCount.value = toastRenderer.activeCount;
                    if (event.detail.isManual) {
                        manuallyClosed.value++;
                    } else {
                        autoDismissed.value++;
                    }
                });
                
                // Listen for MessageBar events
                document.addEventListener('messagebar-created', (event) => {
                    activeMessageBarCount.value = messageBarManager.activeCount;
                    totalMessageBarsCreated.value++;
                });
                
                document.addEventListener('messagebar-dismissed', (event) => {
                    activeMessageBarCount.value = messageBarManager.activeCount;
                    messageBarsDismissed.value++;
                });

                // Toast methods
                const showSuccessToast = () => {
                    toastRenderer.success(customMessage.value || 'Operation completed successfully!', { duration: duration.value });
                    activeToastCount.value = toastRenderer.activeCount;
                    totalCreated.value++;
                };

                const showErrorToast = () => {
                    toastRenderer.error(customMessage.value || 'An error occurred. Please try again.', { duration: duration.value });
                    activeToastCount.value = toastRenderer.activeCount;
                    totalCreated.value++;
                };

                const showInfoToast = () => {
                    toastRenderer.info(customMessage.value || 'Here is some important information.', { duration: duration.value });
                    activeToastCount.value = toastRenderer.activeCount;
                    totalCreated.value++;
                };

                const showWarningToast = () => {
                    toastRenderer.warning(customMessage.value || 'Warning: Please review your input.', { duration: duration.value });
                    activeToastCount.value = toastRenderer.activeCount;
                    totalCreated.value++;
                };

                const clearAllToasts = () => {
                    toastRenderer.clearAllToasts();
                    activeToastCount.value = toastRenderer.activeCount;
                };

                const spamToasts = () => {
                    for (let i = 0; i < 5; i++) {
                        setTimeout(() => {
                            const types = ['success', 'error', 'info', 'warning'];
                            const type = types[Math.floor(Math.random() * types.length)];
                            toastRenderer[type](`Spam toast #${i + 1}`, { duration: duration.value });
                            activeToastCount.value = toastRenderer.activeCount;
                            totalCreated.value++;
                        }, i * 200);
                    }
                };

                const testMaxLimit = () => {
                    for (let i = 0; i < 7; i++) {
                        setTimeout(() => {
                            toastRenderer.info(`Toast #${i + 1} - Testing max limit`, { duration: duration.value });
                            activeToastCount.value = toastRenderer.activeCount;
                            totalCreated.value++;
                        }, i * 100);
                    }
                };
                
                // MessageBar methods
                const showSuccessMessageBar = () => {
                    messageBarManager.success(customMessage.value || 'Operation completed successfully!');
                    activeMessageBarCount.value = messageBarManager.activeCount;
                };
                
                const showErrorMessageBar = () => {
                    messageBarManager.error(customMessage.value || 'An error occurred. Please check your input.', {
                        duration: duration.value
                    });
                    activeMessageBarCount.value = messageBarManager.activeCount;
                };
                
                const showWarningMessageBar = () => {
                    messageBarManager.warning(customMessage.value || 'Warning: This action may have consequences.', {
                        duration: duration.value
                    });
                    activeMessageBarCount.value = messageBarManager.activeCount;
                };
                
                const showInfoMessageBar = () => {
                    messageBarManager.info(customMessage.value || 'Here is some important information.', {
                        duration: duration.value
                    });
                    activeMessageBarCount.value = messageBarManager.activeCount;
                };
                
                const showMessageBarWithActions = () => {
                    messageBarManager.error('Connection failed. Please try again.', {
                        actions: [
                            {
                                id: 'retry',
                                text: 'Retry',
                                handler: () => {
                                    console.log('Retry action clicked');
                                    // Simulate retry
                                    setTimeout(() => {
                                        messageBarManager.success('Connection restored successfully!');
                                    }, 1000);
                                }
                            },
                            {
                                id: 'cancel',
                                text: 'Cancel',
                                handler: () => {
                                    console.log('Cancel action clicked');
                                }
                            }
                        ],
                        persistent: false
                    });
                    activeMessageBarCount.value = messageBarManager.activeCount;
                };
                
                const showPersistentMessageBar = () => {
                    messageBarManager.warning('This is a persistent warning that requires manual dismissal.', {
                        persistent: true
                    });
                    activeMessageBarCount.value = messageBarManager.activeCount;
                };
                
                const clearAllMessageBars = () => {
                    messageBarManager.clearAllMessageBars();
                    activeMessageBarCount.value = messageBarManager.activeCount;
                };

                return {
                    customMessage,
                    duration,
                    activeToastCount,
                    totalCreated,
                    autoDismissed,
                    manuallyClosed,
                    activeMessageBarCount,
                    totalMessageBarsCreated,
                    messageBarsDismissed,
                    showSuccessToast,
                    showErrorToast,
                    showInfoToast,
                    showWarningToast,
                    clearAllToasts,
                    spamToasts,
                    testMaxLimit,
                    showSuccessMessageBar,
                    showErrorMessageBar,
                    showWarningMessageBar,
                    showInfoMessageBar,
                    showMessageBarWithActions,
                    showPersistentMessageBar,
                    clearAllMessageBars
                };
            }
        }).mount('#test-app');
    </script>
</body>
</html>
