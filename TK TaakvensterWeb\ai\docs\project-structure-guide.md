# Project Structure Guide

This guide explains how the TK Taakvenster project is organized and how to navigate it effectively.

## 🏗️ Current Structure Overview

```
TK TaakvensterWeb/
├── src/                    # Main application code
│   ├── components/         # Vue components (like VBA UserForms)
│   ├── composables/        # Reusable logic (like VBA Modules)
│   ├── stores/            # Application state management
│   ├── assets/            # Styles and static files
│   ├── plugins/           # Vue plugins and configurations
│   ├── utils/             # Utility functions
│   ├── wrappers/          # Third-party library wrappers
│   └── main.js            # Application entry point
├── ai/docs/               # Documentation for developers
└── [config files]         # Project configuration
```

## 📁 Detailed Folder Breakdown

### `/src/components/` - Vue Components
*Think: VBA UserForms and their code*

```
components/
├── StepManager.vue         # Main workflow controller
├── Footer.vue              # Application footer
├── PanelToggle.vue         # Step navigation
├── steps/                  # Individual workflow steps
│   ├── CommissieStep.vue   # Commissie selection step
│   ├── DienstenStep.vue    # Diensten selection step
│   ├── RubriceringStep.vue # Rubricering selection step
│   ├── StepHeader.vue      # Reusable step header
│   └── _StepTemplate.vue   # Template for new steps
├── ui/                     # Reusable UI components
│   └── MessageBar/         # Message bar components
├── common/                 # Shared components
│   └── FlowbiteList.vue    # Reusable list component
└── remember/               # Test/development components
```

**Key Files:**
- `StepManager.vue` - Controls the entire step workflow
- `steps/_StepTemplate.vue` - Copy this to create new steps
- `steps/README.md` - Detailed guide for working with steps

### `/src/composables/` - Reusable Logic
*Think: VBA Modules with shared functions*

```
composables/
├── useStepConfig.js        # Step configuration and registration
├── useStepLogic.js         # Step behavior logic
├── useOffice.js            # Word document integration
├── useErrorHandler.js      # Error handling utilities
├── useToasts.js            # Toast notification logic
├── useMessageBar/          # Message bar functionality
├── office/                 # Office-specific utilities
├── toast/                  # Toast-specific utilities
└── [other utilities]
```

**Key Files:**
- `useStepConfig.js` - Register new steps here
- `useOffice.js` - Word document operations
- `useErrorHandler.js` - Centralized error handling

### `/src/stores/` - Application State
*Think: Global variables that multiple forms can access*

```
stores/
└── messageBarStore.js      # Message bar state management
```

### `/src/assets/` - Styles and Static Files
```
assets/
├── base.css               # Base application styles
└── fluent-toast.css       # Toast notification styles
```

## 🎯 Planned Feature-Based Structure

The project will be reorganized into this cleaner structure:

```
src/features/
├── step-workflow/          # Document workflow functionality
│   ├── components/         # Step-related components
│   │   ├── StepManager.vue
│   │   ├── StepHeader.vue
│   │   └── steps/          # Individual step components
│   ├── composables/        # Step-related logic
│   │   ├── useStepConfig.js
│   │   └── useStepLogic.js
│   └── stores/             # Step-related state (if needed)
│
├── notifications/          # Toast and message bar systems
│   ├── components/         # Notification components
│   │   ├── ToastContainer.vue
│   │   └── MessageBar/
│   ├── composables/        # Notification logic
│   │   ├── useToasts.js
│   │   └── useMessageBar/
│   └── stores/             # Notification state
│       └── messageBarStore.js
│
├── office-integration/     # Word document integration
│   ├── components/         # Office-related components
│   │   └── IncompatibleWordVersionPanel.vue
│   ├── composables/        # Office integration logic
│   │   ├── useOffice.js
│   │   └── useWordCompatibility.js
│   └── wrappers/           # Office API wrappers
│
├── shared/                 # Reusable across features
│   ├── components/         # Shared UI components
│   │   ├── Footer.vue
│   │   └── common/
│   ├── composables/        # Shared utilities
│   │   ├── useErrorHandler.js
│   │   ├── useLogger.js
│   │   └── useEnvironment.js
│   └── utils/              # Pure utility functions
│
└── development/            # Testing and development tools
    ├── components/         # Test components
    │   ├── TestPanelControls.vue
    │   └── MessageBarTest.vue
    └── composables/        # Test utilities
```

## 🔍 How to Navigate the Code

### Finding Step-Related Code
1. **Step Components**: Look in `/src/components/steps/`
2. **Step Configuration**: Check `/src/composables/useStepConfig.js`
3. **Step Logic**: Check `/src/composables/useStepLogic.js`

### Finding Notification Code
1. **Toast Components**: Look in `/src/components/remember/` (temporary location)
2. **Message Bar Components**: Look in `/src/components/ui/MessageBar/`
3. **Notification Logic**: Check `/src/composables/useToasts.js` and `/src/composables/useMessageBar/`

### Finding Office Integration Code
1. **Word Integration**: Check `/src/composables/useOffice.js`
2. **Compatibility Checks**: Check `/src/composables/useWordCompatibility.js`
3. **Office Wrappers**: Look in `/src/wrappers/fluent-ui/`

## 📝 File Naming Conventions

### Components (`.vue` files)
- **PascalCase**: `StepManager.vue`, `CommissieStep.vue`
- **Descriptive**: Name clearly indicates what the component does
- **Suffix**: Always end with `.vue`

### Composables (`.js` files)
- **camelCase with 'use' prefix**: `useStepConfig.js`, `useOffice.js`
- **Descriptive**: Name indicates what functionality it provides
- **Suffix**: Always end with `.js`

### Folders
- **kebab-case**: `step-workflow`, `office-integration`
- **Descriptive**: Name clearly indicates the feature or purpose

## 🎨 Component Structure

Every Vue component follows this structure:

```vue
<template>
  <!-- HTML-like template (like UserForm design) -->
  <div>
    <h1>{{ title }}</h1>
    <button @click="handleClick">Click Me</button>
  </div>
</template>

<script setup>
// JavaScript logic (like UserForm code module)
import { ref } from 'vue'

const title = ref('My Component')

function handleClick() {
  console.log('Button clicked!')
}
</script>

<style scoped>
/* CSS styles (like UserForm appearance) */
.my-class {
  color: blue;
}
</style>
```

## 🔧 Key Configuration Files

### `/src/main.js`
- Application entry point
- Sets up Vue app and plugins
- *Think: Like the main module that starts everything*

### `/src/composables/useStepConfig.js`
- Registers all workflow steps
- **This is where you add new steps!**
- *Think: Like a menu configuration*

### Package Files (in root)
- `package.json` - Project dependencies and scripts
- `vite.config.js` - Build tool configuration
- `tailwind.config.js` - CSS framework configuration

## 🚀 Working with the Structure

### Adding a New Step
1. Copy `/src/components/steps/_StepTemplate.vue`
2. Modify the copied file
3. Register it in `/src/composables/useStepConfig.js`

### Adding New Functionality
1. Create composable in `/src/composables/`
2. Import and use in components
3. Add tests if needed

### Modifying Existing Features
1. Find the relevant component or composable
2. Make your changes
3. Test thoroughly

## 📚 Next Steps

1. Read [Creating New Steps](./creating-new-steps.md) to learn how to add workflow steps
2. Check [Common Patterns](./common-patterns.md) for reusable code examples
3. Review [Vue vs VBA Comparison](./vue-vs-vba-comparison.md) for syntax help
