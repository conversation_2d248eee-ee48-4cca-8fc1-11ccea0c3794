# Integration Tests for TK Word Add-in

## Why Integration Tests?

Integration tests verify that multiple components work together correctly, catching issues that unit tests miss:

### **Current Gap Analysis**
Our unit tests mock everything, which means we're not testing:
- **Real component interactions** - Does StepManager actually communicate properly with step components?
- **State management flow** - Do Pinia stores work correctly with components?
- **Event propagation** - Do step events bubble up correctly through the component hierarchy?
- **Error boundaries** - Does error handling work across component boundaries?

### **Business Value**
Integration tests catch the bugs users actually encounter:
- Step navigation breaking after Office API errors
- State getting out of sync between components
- Error messages not displaying when real failures occur

## Recommended Integration Tests

### **1. Step Flow Integration** (High Priority)
```javascript
describe('Step Flow Integration', () => {
  it('should complete full rubricering workflow', async () => {
    // Mount full StepManager with real RubriceringStep
    // Simulate user selecting rubricering
    // Verify Office API called correctly
    // Verify step completion and navigation
    // Verify state updates
  })
})
```

**Why**: This tests the core user workflow end-to-end.

### **2. Error Recovery Integration** (High Priority)
```javascript
describe('Error Recovery Integration', () => {
  it('should recover gracefully from Office API failures', async () => {
    // Mount real components
    // Trigger Office API failure
    // Verify error message displays
    // Verify component state resets
    // Verify user can retry successfully
  })
})
```

**Why**: Error handling is critical for user experience.

### **3. Component Communication** (Medium Priority)
```javascript
describe('Component Communication', () => {
  it('should properly emit and handle step events', async () => {
    // Mount StepManager with real step component
    // Trigger step-complete event
    // Verify StepManager responds correctly
    // Verify navigation occurs
  })
})
```

**Why**: Ensures our event system works in practice.

## Implementation Strategy

### **Minimal Approach** (Recommended)
1. **Start with 2-3 critical flows** - Don't over-test
2. **Use real components, mock only external APIs** - Office API, SharePoint
3. **Focus on user-visible behavior** - Not internal state
4. **Test failure scenarios** - These are most likely to break

### **Test Structure**
```javascript
// integration/step-workflow.test.js
import { mount } from '@vue/test-utils'
import { createPinia } from 'pinia'
import StepManager from '@/components/StepManager.vue'
import RubriceringStep from '@/components/steps/RubriceringStep.vue'

// Mock only external APIs
vi.mock('@/composables/useOffice', () => ({
  useOffice: () => ({
    insertTextInContentControl: vi.fn().mockResolvedValue(true),
    hasContentControl: vi.fn().mockResolvedValue(true)
  })
}))

describe('Rubricering Workflow Integration', () => {
  it('should complete rubricering selection and navigation', async () => {
    const wrapper = mount(StepManager, {
      props: {
        steps: [
          { name: 'Rubricering', component: RubriceringStep },
          { name: 'Next Step', component: MockNextStep }
        ]
      },
      global: {
        plugins: [createPinia()]
      }
    })

    // User selects rubricering
    const rubriceringOption = wrapper.find('[data-testid="rubricering-option-0"]')
    await rubriceringOption.trigger('click')

    // Verify Office API called
    expect(mockInsertText).toHaveBeenCalledWith('txtRubricering', 'TK-V Extern Deelbaar')

    // Verify navigation to next step
    await wrapper.vm.$nextTick()
    expect(wrapper.vm.currentStepIndex).toBe(1)
  })
})
```

## When NOT to Add Integration Tests

- **Don't test every component combination** - Focus on critical user paths
- **Don't duplicate unit test coverage** - If unit tests cover it well, skip integration
- **Don't test framework behavior** - Vue's event system works, trust it
- **Don't test styling/layout** - Use visual regression tests instead

## Maintenance Strategy

- **Keep integration tests minimal** - They're slower and more brittle
- **Update when user workflows change** - Not when implementation changes
- **Run in CI but not on every save** - Too slow for development feedback
- **Mock external dependencies consistently** - Office API, network calls

## ROI Assessment

**High ROI Integration Tests:**
- ✅ Critical user workflows (rubricering, commissie selection)
- ✅ Error recovery scenarios
- ✅ Cross-component state management

**Low ROI Integration Tests:**
- ❌ Every possible component combination
- ❌ Edge cases already covered by unit tests
- ❌ Framework behavior (Vue reactivity, etc.)

## Conclusion

Add 3-5 focused integration tests covering:
1. **Happy path workflows** - Core user journeys
2. **Error scenarios** - Office API failures, validation errors
3. **State management** - Cross-component data flow

This gives us confidence in the user experience without over-testing implementation details.
