import { describe, it, expect, vi } from 'vitest'
import { mount } from '@vue/test-utils'
import StepHeader from '../../../src/features/steps/components/steps/StepHeader.vue'

describe('StepHeader Props Validation', () => {
  it('should render with valid title and subTitle props', () => {
    const wrapper = mount(StepHeader, {
      props: {
        title: 'Test Title',
        subTitle: 'Test Subtitle',
      },
    })

    expect(wrapper.text()).toContain('Test Title')
    expect(wrapper.text()).toContain('Test Subtitle')
  })

  it('should warn when required props are missing', () => {
    const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})

    mount(StepHeader, {
      props: {
        title: 'Test Title',
        // subTitle is missing
      },
    })

    // Vue should warn about missing required prop
    expect(consoleSpy).toHaveBeenCalled()
    consoleSpy.mockRestore()
  })

  it('should warn when props have wrong type', () => {
    const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})

    mount(StepHeader, {
      props: {
        title: 123, // should be string
        subTitle: 'Valid subtitle',
      },
    })

    // Vue should warn about prop type validation failure
    expect(consoleSpy).toHaveBeenCalled()
    consoleSpy.mockRestore()
  })

  it('should handle empty string props gracefully', () => {
    const wrapper = mount(StepHeader, {
      props: {
        title: '',
        subTitle: '',
      },
    })

    // Should render without errors, even with empty strings
    expect(wrapper.exists()).toBe(true)
  })
})
