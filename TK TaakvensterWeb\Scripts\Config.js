const Config = (function() {
    // DOM Selectors
    const selectors = {
        services: {
            container: '.diensten-list',
            retryButton: '#retryServicesBtn',
            serviceItem: '.ms-ListItem',
            serviceButton: '.dienst-button',
            primaryText: '.ms-ListItem-primaryText'
        }
    };
    
    // Template IDs
    const templates = {
        services: {
            serviceItem: 'service-item-template',
            loadingIndicator: 'services-loading-template',
            errorState: 'services-error-template'
        }
    };
    
    // Event names
    const events = {
        services: {
            retry: 'services:retry'
        }
    };
    
    // Public API
    return {
        selectors: selectors,
        templates: templates,
        events: events
    };
})();
