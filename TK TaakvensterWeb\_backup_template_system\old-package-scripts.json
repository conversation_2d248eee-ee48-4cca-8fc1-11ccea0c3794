{"note": "Backup of old package.json scripts from template system", "old_scripts": {"dev": "node build-scripts/inject-assets.js development && vite", "dev:watch": "vite build --watch && node build-scripts/inject-assets.js", "build": "vite build && node build-scripts/inject-assets.js", "build:dev": "vite build --mode development && node build-scripts/inject-assets.js", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "format": "prettier --write src/"}}