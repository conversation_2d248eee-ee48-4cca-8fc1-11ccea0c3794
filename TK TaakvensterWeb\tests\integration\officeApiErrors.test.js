import { describe, it, expect, vi, beforeEach } from 'vitest'
import { useErrorHandler } from '../../src/features/shared'
import { createPinia, setActivePinia } from 'pinia'
import { useMessageBarStore } from '../../src/features/notifications/stores/messageBarStore'

// Mock Office API
global.Office = {
  context: {
    document: {
      getSelectedDataAsync: vi.fn(),
    },
  },
  CoercionType: {
    Text: 'text',
  },
}

describe('Office API Error Handling', () => {
  let messageStore
  let errorHandler

  beforeEach(() => {
    setActivePinia(createPinia())
    messageStore = useMessageBarStore()
    vi.spyOn(messageStore, 'error')
    errorHandler = useErrorHandler()
  })

  it('should handle Office API errors', async () => {
    // Mock Office API failure
    global.Office.context.document.getSelectedDataAsync.mockImplementation((dataType, options) => {
      options.callback({ status: 'failed', error: { message: 'API Error' } })
    })

    // Function that uses Office API
    const getSelectedText = async () => {
      return new Promise((resolve, reject) => {
        Office.context.document.getSelectedDataAsync(Office.CoercionType.Text, {
          callback: (result) => {
            if (result.status === 'succeeded') {
              resolve(result.value)
            } else {
              reject(new Error(result.error.message))
            }
          },
        })
      })
    }

    // Use withErrorHandling to handle potential errors
    await expect(
      errorHandler.withErrorHandling(getSelectedText, 'getting selected text', { rethrow: false }),
    ).resolves.toBeUndefined()

    // Verify error was handled
    expect(messageStore.error).toHaveBeenCalledWith(expect.stringContaining('API Error'))
  })

  it('should handle successful Office API calls', async () => {
    // Mock Office API success
    global.Office.context.document.getSelectedDataAsync.mockImplementation((dataType, options) => {
      options.callback({ status: 'succeeded', value: 'Selected text' })
    })

    // Function that uses Office API
    const getSelectedText = async () => {
      return new Promise((resolve, reject) => {
        Office.context.document.getSelectedDataAsync(Office.CoercionType.Text, {
          callback: (result) => {
            if (result.status === 'succeeded') {
              resolve(result.value)
            } else {
              reject(new Error(result.error.message))
            }
          },
        })
      })
    }

    // Use withErrorHandling to handle potential errors
    const result = await errorHandler.withErrorHandling(getSelectedText, 'getting selected text')

    // Verify success
    expect(result).toBe('Selected text')
    expect(messageStore.error).not.toHaveBeenCalled()
  })
})
