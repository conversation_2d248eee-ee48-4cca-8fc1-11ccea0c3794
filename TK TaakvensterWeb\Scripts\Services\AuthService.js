/**
 * Authentication Service
 * 
 * Provides authentication functionality for the Word add-in using Office SSO.
 * Handles token acquisition, error handling, and fallback authentication.
 * 
 * @module AuthService
 */
const AuthService = (function() {
    /**
     * Helper function to parse JWT tokens if OfficeHelpers is not available
     * 
     * @param {string} token - The JWT token to parse
     * @returns {object|null} The parsed token payload or null if parsing fails
     */
    function parseJwt(token) {
        try {
            // Split the token into parts
            const base64Url = token.split('.')[1];
            const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
            // Decode the base64 string
            const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
                return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
            }).join(''));
            
            return JSON.parse(jsonPayload);
        } catch (e) {
            console.error('Error parsing JWT token:', e);
            return null;
        }
    }
    // No token caching - handled by office.js and Graph .NET library
    
    /**
     * Gets an authentication token using Office SSO
     * 
     * Attempts to acquire a token using OfficeRuntime.auth.getAccessToken
     * and falls back to dialog-based authentication if SSO fails.
     * 
     * @async
     * @returns {Promise<string|null>} The authentication token or null if authentication fails
     */
    async function getToken() {
        try {
            // Log essential Office context information for debugging
            try {
                if (Office && Office.context) {
                    // Only log critical information
                    if (!Office.auth) {
                        console.error('Office.auth is NOT available - this is a critical issue');
                    }
                    
                    // Log URL only if it's undefined (common issue)
                    if (Office.context.diagnostics && !Office.context.diagnostics.url) {
                        console.warn('Office.context.diagnostics.url is undefined - this may cause authentication issues');
                    }
                } else {
                    console.error('Office context is not available');
                }
            } catch (contextError) {
                console.error(`Error checking Office context: ${contextError.message}`);
            }
            
            // No token caching - tokens are handled by office.js
            
            // Get token using SSO with Microsoft Graph options
            const token = await OfficeRuntime.auth.getAccessToken({ allowSignInPrompt: true });
            
            console.log('Token acquired successfully');
            
            // Parse token to get expiry
            const tokenPayload = parseJwt(token);
            if (tokenPayload && tokenPayload.exp) {
                _tokenExpiry = new Date(tokenPayload.exp * 1000);
                console.log(`Token expires at: ${_tokenExpiry.toISOString()}`);
            }
            
            // Store token and return it
            return token;
        } catch (error) {
            // Handle SSO errors
            if (error instanceof OfficeExtension.Error) {
                // Log detailed error information
                console.error(`SSO Error: Code=${error.code}, Message=${error.message}`);
                
                // Show user-friendly notification based on error code
                let userMessage = '';
                
                switch (error.code) {
                    case 13001:
                        console.error('Error 13001: The user is not signed in to Office.');
                        userMessage = 'U bent niet ingelogd in Office. Log in om toegang te krijgen tot SharePoint gegevens.';
                        break;
                    case 13002:
                        console.error('Error 13002: The user aborted sign in or consent.');
                        userMessage = 'Aanmelding geannuleerd. Statische gegevens worden gebruikt.';
                        break;
                    case 13003:
                        console.error('Error 13003: The user is from an unsupported tenant.');
                        userMessage = 'Uw Office-account heeft geen toegang tot deze add-in. Neem contact op met uw beheerder.';
                        break;
                    case 13004:
                        console.error('Error 13004: Invalid resource URL in manifest.');
                        userMessage = 'Configuratiefout in de add-in. Neem contact op met de ontwikkelaar.';
                        break;
                    case 13005:
                        console.error('Error 13005: Service error.');
                        userMessage = 'Er is een fout opgetreden in de authenticatieservice. Probeer het later opnieuw.';
                        break;
                    case 13006:
                        console.error('Error 13006: Insufficient permissions.');
                        userMessage = 'U heeft onvoldoende rechten om toegang te krijgen tot de gevraagde gegevens.';
                        break;
                    case 13007:
                        console.error('Error 13007: Flow type not supported.');
                        userMessage = 'Deze authenticatiemethode wordt niet ondersteund in uw omgeving.';
                        break;
                    case 13008:
                        console.error('Error 13008: Token expired or invalid.');
                        userMessage = 'Uw sessie is verlopen. Vernieuw de pagina om opnieuw in te loggen.';
                        break;
                    case 13009:
                        console.error('Error 13009: Not authorized to call resource.');
                        userMessage = 'U heeft geen toegang tot de gevraagde SharePoint-gegevens.';
                        break;
                    default:
                        console.error(`Unknown SSO error code: ${error.code}`);
                        userMessage = 'Er is een onbekende fout opgetreden tijdens authenticatie.';
                }
                
                // Show notification to the user
                if (OfficeUtils && OfficeUtils.UI) {
                    OfficeUtils.UI.showNotification('Authenticatie fout', userMessage);
                }
                
                // For any error, fallback to dialog auth
                return fallbackToDialogAuth();
            } else {
                // For any other error, log and fallback to dialog auth
                console.error(`Non-Office error: ${error.message}`);
                return fallbackToDialogAuth();
            }
        }
    }
    
    /**
     * Fallback authentication method when SSO fails
     * 
     * Currently shows a notification to the user and returns null,
     * allowing the application to continue with mock data.
     * In a production environment, this could be replaced with a custom auth dialog.
     * 
     * @async
     * @returns {Promise<null>} Always returns null to indicate fallback authentication
     */
    async function fallbackToDialogAuth() {
        try {
            // Since we've removed OfficeHelpers dependency, we'll use our own implementation
            console.log('Fallback naar alternatieve authenticatie...');
            
            // For now, we'll use the mock data as a fallback when authentication fails
            // In a production environment, you might want to implement a custom auth dialog
            const message = 'Authenticatie met SharePoint is mislukt. Statische gegevens worden gebruikt.';
            
            // Show a notification to the user
            if (OfficeUtils && OfficeUtils.UI) {
                OfficeUtils.UI.showNotification('Authenticatie info', message);
            }
            
            // Return null to indicate authentication failed but allow the app to continue
            // The calling code should handle this by using mock data
            return null;
        } catch (error) {
            OfficeUtils.Errors.handleError(error, "authentication", {
                method: "fallbackToDialogAuth"
            });
            throw error;
        }
    }
    
    // Public API
    return {
        getToken: getToken
    };
})();
