<template>
  <div id="panel-profiel-afzender">
    <StepHeader :title="stepTitle" :subTitle="stepDescription" />
    <div class="profiel-container">
      <FlowbiteList
        :items="profilesForDisplay"
        v-model="selectedProfileIndex"
        @item-click="handleProfileClick"
      >
        <template #item="{ item }">
          {{ item.label }}
        </template>
      </FlowbiteList>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useOffice } from '../../../office'
import { useErrorHandler } from '../../../shared'
import { useProfileStore } from '../../stores/profileStore'
import { useCommissionStore } from '../../stores/commissionStore'
import {
  getCommissionPrefix,
  getFormattingConfig,
  getRequiredContentControls,
} from '../../../../config/appConfig'
import StepHeader from './StepHeader.vue'
import FlowbiteList from '../../../shared/components/common/FlowbiteList.vue'

// Emits
const emit = defineEmits(['step-ready', 'step-complete'])

// Step metadata
const stepTitle = 'Profiel Afzender'
const stepDescription = 'Selecteer het profiel van de afzender'

// Composables
const { insertTextInContentControl, hasContentControl } = useOffice()
const { withErrorHandling } = useErrorHandler()

// Stores
const profileStore = useProfileStore()
const commissionStore = useCommissionStore()

// Reactive state
const selectedProfileIndex = ref(null)

// Computed
const profilesForDisplay = computed(() => profileStore.profilesForDisplay)

// Methods
function handleProfileClick(event) {
  processProfileSelection(event.index)
}

async function processProfileSelection(index) {
  try {
    // Validate selection
    if (index === null || index === undefined || index < 0 || index >= profileStore.profileCount) {
      await withErrorHandling(
        () => Promise.reject(new Error('Invalid selection')),
        'validating profile selection',
        { userMessage: 'Selecteer eerst een profiel voordat u verder gaat.' },
      )
      return
    }

    selectedProfileIndex.value = index
    const profile = profileStore.selectProfile(index)

    if (!profile) {
      await withErrorHandling(
        () => Promise.reject(new Error('Profile not found')),
        'retrieving selected profile',
        { userMessage: 'Het geselecteerde profiel kon niet worden gevonden.' },
      )
      return
    }

    // Populate content controls
    await populateContentControls(profile)

    // Signal step completion
    setTimeout(() => {
      emit('step-complete')
    }, 500)
  } catch (error) {
    console.error('Error processing profile selection:', error)
    selectedProfileIndex.value = null
    profileStore.clearSelection()
  }
}

async function populateContentControls(profile) {
  const errors = []
  const formatting = getFormattingConfig()

  try {
    // 1. Populate txtAfzenderPersoonlijk
    const personalData = formatPersonalData(profile, formatting)
    await withErrorHandling(
      () => insertTextInContentControl('txtAfzenderPersoonlijk', personalData),
      'populating personal data',
      {
        userMessage: 'Het invoegen van persoonlijke gegevens is mislukt.',
        successMessage: `Profiel ${profile.FirstName} ${profile.LastName} is toegevoegd.`,
      },
    )

    // 2. Get commission data
    const commissionData = commissionStore.getFormattedCommissionData(profile.CommissionId)

    if (!commissionData.hasCommission && profile.CommissionId) {
      errors.push(`Commissie niet gevonden voor ID: ${profile.CommissionId}`)
    }

    // 3. Populate txtAfzenderCommissie1
    const commissionText = formatCommissionText(commissionData, formatting)
    await withErrorHandling(
      () => insertTextInContentControl('txtAfzenderCommissie1', commissionText),
      'populating commission data',
      {
        userMessage: 'Het invoegen van commissiegegevens is mislukt.',
        rethrow: false, // Continue even if this fails
      },
    )

    // 4. Populate txtAfzenderCommissie2 (only if commission exists)
    if (commissionData.hasCommission && commissionData.formattedAddress) {
      await withErrorHandling(
        () => insertTextInContentControl('txtAfzenderCommissie2', commissionData.formattedAddress),
        'populating commission address',
        {
          userMessage: 'Het invoegen van adresgegevens is mislukt.',
          rethrow: false, // Continue even if this fails
        },
      )
    }

    // Show any collected errors
    if (errors.length > 0) {
      await withErrorHandling(
        () => Promise.reject(new Error(errors.join('; '))),
        'processing commission data',
        {
          userMessage:
            'Enkele gegevens konden niet worden verwerkt. Controleer de console voor details.',
          rethrow: false,
        },
      )
    }
  } catch (error) {
    console.error('Error populating content controls:', error)
    throw error
  }
}

function formatPersonalData(profile, formatting) {
  const parts = []

  // Name (required)
  parts.push(`${profile.FirstName} ${profile.LastName}`)

  // Role (optional)
  if (profile.Role && profile.Role.trim() !== '') {
    parts.push(profile.Role)
  }

  // Email (optional)
  if (profile.EmailAddress && profile.EmailAddress.trim() !== '') {
    parts.push(profile.EmailAddress)
  }

  return parts.join(formatting.lineSeparator)
}

function formatCommissionText(commissionData, formatting) {
  const prefix = getCommissionPrefix()

  if (commissionData.hasCommission) {
    return `${prefix}${formatting.commissionSeparator}${commissionData.commissionName}`
  }

  return prefix
}

// Lifecycle
onMounted(async () => {
  try {
    // Validate all required content controls exist
    const requiredControls = getRequiredContentControls()
    const missingControls = []

    for (const controlTag of requiredControls) {
      try {
        const exists = await hasContentControl(controlTag)
        if (!exists) {
          missingControls.push(controlTag)
        }
      } catch (error) {
        console.error(`Error checking content control ${controlTag}:`, error)
        missingControls.push(controlTag)
      }
    }

    if (missingControls.length > 0) {
      await withErrorHandling(
        () => Promise.reject(new Error(`Missing content controls: ${missingControls.join(', ')}`)),
        'validating content controls',
        {
          userMessage:
            'Een of meer vereiste velden ontbreken in het document. Deze stap kan niet worden gebruikt.',
          rethrow: false,
        },
      )
    }

    // Load data
    await Promise.all([profileStore.loadProfiles(), commissionStore.loadCommissions()])

    emit('step-ready')
  } catch (error) {
    console.error('Error initializing ProfielAfzender step:', error)
    emit('step-ready') // Still emit ready even if data loading fails
  }
})
</script>

<style scoped>
.profiel-container {
  margin-top: 1rem;
  width: 100%;
}
</style>
