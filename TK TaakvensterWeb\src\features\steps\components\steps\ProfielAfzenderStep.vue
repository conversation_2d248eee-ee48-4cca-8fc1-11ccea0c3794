<template>
  <div id="panel-profiel-afzender">
    <StepHeader :title="stepTitle" :subTitle="stepDescription" />

    <!-- Step-specific error display (only show recoverable errors) -->
    <div
      v-if="errorState.message && errorState.isRecoverable"
      :class="[
        'px-4 py-3 rounded-md mb-4 text-left relative',
        errorState.type === 'commission-lookup'
          ? 'bg-yellow-50 border border-yellow-200 text-yellow-800'
          : 'bg-orange-50 border border-orange-200 text-orange-800',
      ]"
      role="alert"
      aria-live="polite"
    >
      <span>{{ errorState.message }}</span>
      <button
        @click="clearErrors"
        class="absolute top-2 right-2 w-6 h-6 flex items-center justify-center rounded-full hover:bg-yellow-200/50 focus:outline-none focus:ring-2 focus:ring-yellow-400 focus:ring-offset-1 transition-colors duration-200"
        aria-label="Dismiss error message"
        type="button"
        title="Dismiss error message"
      >
        ×
      </button>
    </div>

    <div class="profiel-container">
      <FlowbiteList
        :items="profilesForDisplay"
        v-model="selectedProfileIndex"
        @item-click="handleProfileClick"
      >
        <template #item="{ item }">
          {{ item.label }}
        </template>
      </FlowbiteList>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useOffice } from '../../../office'
import { useErrorHandler } from '../../../shared'
import { useProfileStore } from '../../stores/profileStore'
import { useCommissionStore } from '../../stores/commissionStore'
import {
  getCommissionPrefix,
  getFormattingConfig,
  getRequiredContentControls,
  getStepBehaviorConfig,
} from '../../../../config/appConfig'
import StepHeader from './StepHeader.vue'
import FlowbiteList from '../../../shared/components/common/FlowbiteList.vue'

// Emits
const emit = defineEmits(['step-ready', 'step-complete'])

// Step metadata
const stepTitle = 'Profiel Afzender'
const stepDescription = 'Selecteer het profiel van de afzender'

// Composables
const { insertTextInContentControl, hasContentControl } = useOffice()
const { withErrorHandling } = useErrorHandler()

// Stores
const profileStore = useProfileStore()
const commissionStore = useCommissionStore()

// Reactive state
const selectedProfileIndex = ref(null)

// Error state management
const errorState = ref({
  type: null, // 'validation', 'data-loading', 'office-api', 'commission-lookup'
  message: '',
  isRecoverable: true,
  context: null,
})

// Computed
const profilesForDisplay = computed(() => profileStore.profilesForDisplay)

// Helper functions
function setError(type, message, isRecoverable = true, context = null) {
  errorState.value = { type, message, isRecoverable, context }
}

function clearErrors() {
  errorState.value = { type: null, message: '', isRecoverable: true, context: null }
}

function isOfficeApiError(error) {
  return (
    error.isOfficeError ||
    error.message.includes('Office API Error') ||
    error.message.includes('content control') ||
    error.message.includes('Word.run')
  )
}

// Methods
function handleProfileClick(event) {
  processProfileSelection(event.index)
}

async function processProfileSelection(index) {
  try {
    clearErrors()

    // Validation
    if (index === null || index === undefined || index < 0 || index >= profileStore.profileCount) {
      setError('validation', 'Selecteer eerst een profiel voordat u verder gaat.', true, { index })
      return
    }

    selectedProfileIndex.value = index
    const profile = profileStore.selectProfile(index)

    if (!profile) {
      setError('data-loading', 'Het geselecteerde profiel kon niet worden geladen.', true, {
        index,
      })
      return
    }

    // Commission lookup with user notification
    const commissionData = commissionStore.getFormattedCommissionData(profile.CommissionId)
    if (!commissionData.hasCommission && profile.CommissionId) {
      // Show warning but continue processing
      setError(
        'commission-lookup',
        `Commissie kon niet worden gevonden (ID: ${profile.CommissionId}). Alleen persoonlijke gegevens worden ingevoegd.`,
        true,
        { commissionId: profile.CommissionId },
      )
      // Don't return - continue with profile processing
    }

    // Populate content controls
    await populateContentControls(profile)

    // Signal step completion
    setTimeout(() => {
      emit('step-complete')
    }, getStepBehaviorConfig().completionDelay)
  } catch (error) {
    console.error('Error processing profile selection:', error)

    // Reset selection state for any error
    selectedProfileIndex.value = null
    profileStore.clearSelection()

    if (isOfficeApiError(error)) {
      // Let StepManager handle Office errors (non-recoverable)
      throw error // This will be caught by onErrorCaptured in StepManager
    } else {
      // Handle recoverable errors at component level
      setError('processing', 'Er is een fout opgetreden bij het verwerken van het profiel.', true, {
        error,
      })
    }
  }
}

async function populateContentControls(profile) {
  const errors = []
  const formatting = getFormattingConfig()

  try {
    // 1. Populate txtAfzenderPersoonlijk
    const personalData = formatPersonalData(profile, formatting)
    await withErrorHandling(
      () => insertTextInContentControl('txtAfzenderPersoonlijk', personalData),
      'populating personal data',
      {
        userMessage: 'Het invoegen van persoonlijke gegevens is mislukt.',
        successMessage: `Profiel ${profile.FirstName} ${profile.LastName} is toegevoegd.`,
      },
    )

    // 2. Get commission data
    const commissionData = commissionStore.getFormattedCommissionData(profile.CommissionId)

    if (!commissionData.hasCommission && profile.CommissionId) {
      errors.push(`Commissie niet gevonden voor ID: ${profile.CommissionId}`)
    }

    // 3. Populate txtAfzenderCommissie1
    const commissionText = formatCommissionText(commissionData, formatting)
    await withErrorHandling(
      () => insertTextInContentControl('txtAfzenderCommissie1', commissionText),
      'populating commission data',
      {
        userMessage: 'Het invoegen van commissiegegevens is mislukt.',
        rethrow: false, // Continue even if this fails
      },
    )

    // 4. Populate txtAfzenderCommissie2 (only if commission exists)
    if (commissionData.hasCommission && commissionData.formattedAddress) {
      await withErrorHandling(
        () => insertTextInContentControl('txtAfzenderCommissie2', commissionData.formattedAddress),
        'populating commission address',
        {
          userMessage: 'Het invoegen van adresgegevens is mislukt.',
          rethrow: false, // Continue even if this fails
        },
      )
    }

    // Show any collected errors
    if (errors.length > 0) {
      await withErrorHandling(
        () => Promise.reject(new Error(errors.join('; '))),
        'processing commission data',
        {
          userMessage:
            'Enkele gegevens konden niet worden verwerkt. Controleer de console voor details.',
          rethrow: false,
        },
      )
    }
  } catch (error) {
    console.error('Error populating content controls:', error)
    throw error
  }
}

function formatPersonalData(profile, formatting) {
  const parts = []

  // Name (required)
  parts.push(`${profile.FirstName} ${profile.LastName}`)

  // Role (optional)
  if (profile.Role && profile.Role.trim() !== '') {
    parts.push(profile.Role)
  }

  // Email (optional)
  if (profile.EmailAddress && profile.EmailAddress.trim() !== '') {
    parts.push(profile.EmailAddress)
  }

  return parts.join(formatting.lineSeparator)
}

function formatCommissionText(commissionData, formatting) {
  const prefix = getCommissionPrefix()

  if (commissionData.hasCommission) {
    return `${prefix}${formatting.commissionSeparator}${commissionData.commissionName}`
  }

  return prefix
}

// Lifecycle
onMounted(async () => {
  try {
    // Validate all required content controls exist
    const requiredControls = getRequiredContentControls()
    const missingControls = []

    for (const controlTag of requiredControls) {
      try {
        const exists = await hasContentControl(controlTag)
        if (!exists) {
          missingControls.push(controlTag)
        }
      } catch (error) {
        console.error(`Error checking content control ${controlTag}:`, error)
        missingControls.push(controlTag)
      }
    }

    if (missingControls.length > 0) {
      await withErrorHandling(
        () => Promise.reject(new Error(`Missing content controls: ${missingControls.join(', ')}`)),
        'validating content controls',
        {
          userMessage:
            'Een of meer vereiste velden ontbreken in het document. Deze stap kan niet worden gebruikt.',
          rethrow: false,
        },
      )
    }

    // Load data
    await Promise.all([profileStore.loadProfiles(), commissionStore.loadCommissions()])

    emit('step-ready')
  } catch (error) {
    console.error('Error initializing ProfielAfzender step:', error)
    emit('step-ready') // Still emit ready even if data loading fails
  }
})
</script>

<style scoped>
.profiel-container {
  margin-top: 1rem;
  width: 100%;
}
</style>
