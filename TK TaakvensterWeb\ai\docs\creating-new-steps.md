# Creating New Steps - Complete Guide

This guide walks you through creating new workflow steps in the TK Taakvenster application.

## 🎯 What is a Step?

A step is like a **VBA UserForm** that:
- Shows a title and description to the user
- Displays a list of options (like a ListBox)
- Inserts the selected value into a Word document
- Moves to the next step when complete

## 📋 Step-by-Step Process

### Step 1: Copy the Template

Navigate to the steps folder and copy the template:

```bash
# In: /src/components/steps/
cp _StepTemplate.vue YourNewStep.vue
```

**Example:** Creating a "DepartmentStep"
```bash
cp _StepTemplate.vue DepartmentStep.vue
```

### Step 2: Edit Your New Step

Open your new file and follow the TODO comments:

```vue
<!-- DepartmentStep.vue -->
<template>
  <!-- TODO: Change panel ID -->
  <div id="panel-department">
    <StepHeader :title="stepTitle" :subTitle="stepDescription" />
    <FlowbiteList :items="stepOptions" @item-click="handleItemClick" />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import StepHeader from './StepHeader.vue'
import FlowbiteList from '../common/FlowbiteList.vue'
import { useOffice } from '../../composables/useOffice'
import { useErrorHandler } from '../../composables/useErrorHandler'

// TODO: Update these step details
const stepTitle = 'Select Department'
const stepDescription = 'Choose the department for this document'

// TODO: Replace with your step's data
const stepOptions = ref([
  { 
    label: 'Human Resources', 
    value: 'HR',
    controlTag: 'txtDepartment'
  },
  { 
    label: 'Information Technology', 
    value: 'IT',
    controlTag: 'txtDepartment'
  },
  { 
    label: 'Finance', 
    value: 'FIN',
    controlTag: 'txtDepartment'
  }
])

// Standard setup (usually no changes needed)
const emit = defineEmits(['step-ready', 'step-complete', 'step-previous'])
const { insertTextInContentControl } = useOffice()
const { withErrorHandling } = useErrorHandler()

// Component lifecycle
onMounted(() => {
  emit('step-ready')
})

// Handle user selection
async function handleItemClick(event) {
  const selectedItem = stepOptions.value[event.index]
  
  await withErrorHandling(
    async () => {
      await insertTextInContentControl(selectedItem.controlTag, selectedItem.value)
      emit('step-complete')
    },
    'inserting department selection',
    {
      userMessage: 'Could not insert department into document',
      successMessage: `Department "${selectedItem.label}" inserted successfully!`
    }
  )
}
</script>

<style scoped>
/* Add any step-specific styles here */
</style>
```

### Step 3: Register Your Step

Add your new step to the configuration file:

**File:** `/src/composables/useStepConfig.js`

```javascript
import { markRaw } from 'vue'

// Import your new step
import DepartmentStep from '../components/steps/DepartmentStep.vue'
import CommissieStep from '../components/steps/CommissieStep.vue'
import DienstenStep from '../components/steps/DienstenStep.vue'
// ... other imports

export function useStepConfig() {
  return [
    // Existing steps...
    { 
      name: 'Commissie', 
      contentControl: 'txtCommissie', 
      component: markRaw(CommissieStep) 
    },
    { 
      name: 'Diensten', 
      contentControl: 'txtDiensten', 
      component: markRaw(DienstenStep) 
    },
    
    // Add your new step here
    { 
      name: 'Department',           // Display name in navigation
      contentControl: 'txtDepartment',  // Word content control name
      component: markRaw(DepartmentStep)  // Your component
    }
  ]
}
```

### Step 4: Test Your Step

1. **Start the development server:**
   ```bash
   npm run dev
   ```

2. **Open the application in your browser**

3. **Navigate to your new step** using the step navigation

4. **Test the functionality:**
   - Does the step display correctly?
   - Do the options show up?
   - Does clicking an option insert text into Word?
   - Does it move to the next step?

## 🎨 Customization Options

### Adding Custom Input Fields

```vue
<template>
  <div id="panel-custom">
    <StepHeader :title="stepTitle" :subTitle="stepDescription" />
    
    <!-- Standard list -->
    <FlowbiteList :items="stepOptions" @item-click="handleItemClick" />
    
    <!-- Custom input section -->
    <div class="mt-4 p-4 border-t">
      <h3 class="text-lg font-semibold mb-2">Custom Entry</h3>
      <input 
        v-model="customText" 
        type="text" 
        placeholder="Enter custom department..."
        class="w-full p-2 border rounded"
      />
      <button 
        @click="handleCustomEntry"
        class="mt-2 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
      >
        Use Custom Department
      </button>
    </div>
  </div>
</template>

<script setup>
// Add custom data
const customText = ref('')

// Add custom handler
async function handleCustomEntry() {
  if (!customText.value.trim()) {
    alert('Please enter a department name')
    return
  }
  
  await withErrorHandling(
    async () => {
      await insertTextInContentControl('txtDepartment', customText.value)
      emit('step-complete')
    },
    'inserting custom department',
    {
      successMessage: `Custom department "${customText.value}" inserted!`
    }
  )
}
</script>
```

### Multiple Lists in One Step

```vue
<template>
  <div id="panel-multi">
    <StepHeader :title="stepTitle" :subTitle="stepDescription" />
    
    <div class="grid grid-cols-2 gap-4">
      <div>
        <h3 class="font-semibold mb-2">Department</h3>
        <FlowbiteList :items="departments" @item-click="handleDepartmentClick" />
      </div>
      <div>
        <h3 class="font-semibold mb-2">Role</h3>
        <FlowbiteList :items="roles" @item-click="handleRoleClick" />
      </div>
    </div>
    
    <button 
      @click="applyBoth"
      :disabled="!selectedDepartment || !selectedRole"
      class="mt-4 px-4 py-2 bg-green-500 text-white rounded disabled:bg-gray-300"
    >
      Apply Both Selections
    </button>
  </div>
</template>

<script setup>
const departments = ref([
  { label: 'HR', value: 'Human Resources' },
  { label: 'IT', value: 'Information Technology' }
])

const roles = ref([
  { label: 'Manager', value: 'Manager' },
  { label: 'Specialist', value: 'Specialist' }
])

const selectedDepartment = ref(null)
const selectedRole = ref(null)

function handleDepartmentClick(event) {
  selectedDepartment.value = departments.value[event.index]
}

function handleRoleClick(event) {
  selectedRole.value = roles.value[event.index]
}

async function applyBoth() {
  await withErrorHandling(
    async () => {
      await insertTextInContentControl('txtDepartment', selectedDepartment.value.value)
      await insertTextInContentControl('txtRole', selectedRole.value.value)
      emit('step-complete')
    },
    'inserting department and role',
    {
      successMessage: 'Department and role inserted successfully!'
    }
  )
}
</script>
```

## 🔧 Advanced Patterns

### Loading Data from External Source

```vue
<script setup>
import { ref, onMounted } from 'vue'

const stepOptions = ref([])
const isLoading = ref(true)

onMounted(async () => {
  try {
    // Simulate loading data (replace with actual API call)
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    stepOptions.value = [
      { label: 'Loaded Option 1', value: 'opt1', controlTag: 'txtOption' },
      { label: 'Loaded Option 2', value: 'opt2', controlTag: 'txtOption' }
    ]
  } catch (error) {
    console.error('Failed to load options:', error)
  } finally {
    isLoading.value = false
    emit('step-ready')
  }
})
</script>

<template>
  <div v-if="isLoading" class="text-center p-4">
    Loading options...
  </div>
  <div v-else>
    <!-- Your normal step content -->
  </div>
</template>
```

### Conditional Options Based on Previous Steps

```vue
<script setup>
import { computed } from 'vue'
import { useStepLogic } from '../../composables/useStepLogic'

const { getStepData } = useStepLogic()

// Get data from previous steps
const previousCommissie = computed(() => getStepData('Commissie'))

// Conditional options based on previous selection
const stepOptions = computed(() => {
  if (previousCommissie.value === 'Finance') {
    return [
      { label: 'Budget Planning', value: 'budget', controlTag: 'txtTask' },
      { label: 'Financial Analysis', value: 'analysis', controlTag: 'txtTask' }
    ]
  } else {
    return [
      { label: 'General Task 1', value: 'task1', controlTag: 'txtTask' },
      { label: 'General Task 2', value: 'task2', controlTag: 'txtTask' }
    ]
  }
})
</script>
```

## ⚠️ Common Mistakes to Avoid

1. **Forgetting to register the step** in `useStepConfig.js`
2. **Mismatched content control names** between step and Word template
3. **Not emitting 'step-ready'** in onMounted
4. **Not using error handling** for Word operations
5. **Hardcoding values** instead of using reactive data

## 🧪 Testing Your Step

### Manual Testing Checklist
- [ ] Step displays correctly
- [ ] Options are visible and clickable
- [ ] Clicking an option inserts text into Word
- [ ] Success message appears
- [ ] Step advances to next step
- [ ] Error handling works (test with Word closed)

### Browser Console Debugging
```javascript
// Check if your step is registered
console.log('Step config:', useStepConfig())

// Check current step data
console.log('Current step:', getCurrentStep())

// Test Word integration
console.log('Office available:', window.Office !== undefined)
```

## 📚 Next Steps

- Read [Common Patterns](./common-patterns.md) for more code examples
- Check [Troubleshooting](./troubleshooting.md) if you encounter issues
- Review existing steps in `/src/components/steps/` for inspiration
