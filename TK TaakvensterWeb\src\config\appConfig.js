/**
 * Application Configuration
 *
 * Central configuration for the TK Taakvenster Vue application.
 * This file contains settings that can be easily modified without
 * changing component code.
 */

export const appConfig = {
  /**
   * Fixed text strings used throughout the application
   */
  fixedText: {
    /**
     * Commission prefix used in txtAfzenderCommissie1 content control
     * Format: "{commissionPrefix} | {CommissionName}"
     */
    commissionPrefix: 'Tweede Kamer der Staten-Generaal',
  },

  /**
   * ProfielAfzender step specific configuration
   */
  profielAfzender: {
    /**
     * Content control tags that must be present for step activation
     */
    requiredContentControls: [
      'txtAfzenderPersoonlijk',
      'txtAfzenderCommissie1',
      'txtAfzenderCommissie2',
    ],

    /**
     * Formatting configuration for content controls
     */
    formatting: {
      /**
       * Line separator for multi-line content controls
       */
      lineSeparator: '\n',

      /**
       * Separator between commission prefix and name
       */
      commissionSeparator: ' | ',

      /**
       * Separator between address parts (POBox and Street)
       */
      addressSeparator: ' | ',

      /**
       * Separator between address components (e.g., "ZipCode City")
       */
      addressComponentSeparator: ' ',

      /**
       * Separator between address line components (e.g., "POBox, ZipCode City")
       */
      addressLineSeparator: ', ',
    },
  },

  /**
   * Data validation configuration
   */
  validation: {
    /**
     * Profile validation settings
     */
    profile: {
      /**
       * Required fields that must be non-empty
       */
      requiredFields: ['FirstName', 'LastName'],

      /**
       * Optional fields that can be empty or missing
       */
      optionalFields: ['Role', 'EmailAddress', 'CommissionId'],
    },

    /**
     * Commission validation settings
     */
    commission: {
      /**
       * Required fields that must be non-empty
       */
      requiredFields: ['CommissionName'],

      /**
       * Optional fields that can be empty or missing
       */
      optionalFields: ['POBoxNumber', 'ZipCode', 'City', 'StreetName', 'HouseNumber'],
    },
  },

  /**
   * Error handling configuration
   */
  errorHandling: {
    /**
     * Whether to show detailed error information in development mode
     */
    showDetailedErrors: true,

    /**
     * Default error messages
     */
    messages: {
      invalidSelection: 'Selecteer eerst een profiel voordat u verder gaat.',
      contentControlMissing: 'Een of meer vereiste velden ontbreken in het document.',
      profileDataInvalid: 'Profielgegevens zijn ongeldig of onvolledig.',
      commissionNotFound: 'Commissiegegevens konden niet worden gevonden.',
      insertionFailed: 'Het invoegen van gegevens in het document is mislukt.',
      multipleErrors: 'Er zijn meerdere fouten opgetreden. Controleer de console voor details.',
    },
  },

  /**
   * Performance configuration
   */
  performance: {
    /**
     * Delay before step completion (allows user to see success message)
     */
    stepCompletionDelay: 500,

    /**
     * Maximum number of profiles to display without pagination
     */
    maxProfilesWithoutPagination: 100,
  },

  /**
   * Step behavior configuration
   */
  stepBehavior: {
    /**
     * Delay before step completion (allows user to see success message)
     */
    completionDelay: 500,

    /**
     * Default validation messages
     */
    defaultMessages: {
      validation: 'Selecteer eerst een optie voordat u verder gaat.',
      success: 'Actie succesvol uitgevoerd.',
      error: 'Er is een fout opgetreden. Probeer het opnieuw.',
    },
  },
}

/**
 * Get configuration value by path
 * @param {string} path - Dot-separated path to configuration value
 * @param {*} defaultValue - Default value if path not found
 * @returns {*} Configuration value
 *
 * @example
 * getConfig('fixedText.commissionPrefix') // Returns 'Tweede Kamer der Staten-Generaal'
 * getConfig('profielAfzender.formatting.lineSeparator') // Returns '\n'
 */
export function getConfig(path, defaultValue = null) {
  return path.split('.').reduce((obj, key) => {
    return obj && obj[key] !== undefined ? obj[key] : defaultValue
  }, appConfig)
}

/**
 * Check if a configuration path exists
 * @param {string} path - Dot-separated path to check
 * @returns {boolean} True if path exists
 */
export function hasConfig(path) {
  return getConfig(path) !== null
}

/**
 * Get all required content controls for ProfielAfzender step
 * @returns {string[]} Array of content control tags
 */
export function getRequiredContentControls() {
  return getConfig('profielAfzender.requiredContentControls', [])
}

/**
 * Get commission prefix text
 * @returns {string} Commission prefix
 */
export function getCommissionPrefix() {
  return getConfig('fixedText.commissionPrefix', 'Tweede Kamer der Staten-Generaal')
}

/**
 * Get formatting configuration for ProfielAfzender
 * @returns {object} Formatting configuration
 */
export function getFormattingConfig() {
  return getConfig('profielAfzender.formatting', {})
}

/**
 * Get validation configuration
 * @returns {object} Validation configuration
 */
export function getValidationConfig() {
  return getConfig('validation', {})
}

/**
 * Get error handling configuration
 * @returns {object} Error handling configuration
 */
export function getErrorConfig() {
  return getConfig('errorHandling', {})
}

/**
 * Get step behavior configuration
 * @returns {object} Step behavior configuration
 */
export function getStepBehaviorConfig() {
  return getConfig('stepBehavior', {})
}
