/**
 * Services Repository
 * Provides access to parliament services data from SharePoint
 */
const ServicesRepository = (function() {
    // SharePoint list name for services from configuration
    const _servicesListName = AppConfig.sharePoint.lists.services;
    
    // Field mappings from SharePoint field names to application model property names
    const _fieldMappings = {
        id: "Id",
        name: "Title", 
        description: "Description",
        category: "Category",
        contactPerson: "ContactPerson"
    };
    
    // Default select fields for services
    const _defaultSelect = ["Id", "Title", "Description", "Category", "ContactPerson"];
    
    // Default order by for services
    const _defaultOrderBy = "Title asc";
    
    // Map SharePoint item to service model
    function _mapToServiceModel(item) {
        // Debug the raw item to see what fields are available
        console.debug(`Raw item: ${JSON.stringify(item)}`);
        
        // Handle both camelCase and PascalCase property names from SharePoint
        // This ensures compatibility with different API responses
        return {
            id: (item.id || item.Id || '').toString(),
            title: item.Title || item.title || "",
            name: item.Title || item.title || "", // Duplicate for compatibility
            description: item.Description || item.description || "",
            category: item.Category || item.category || "",
            contactPerson: item.ContactPerson || item.contactPerson || ""
        };
    }
    
    // Fallback services data if SharePoint is unavailable
    const _fallbackServices = [
        {
            id: "1",
            name: "Beleidsanalyse",
            description: "Ondersteuning bij beleidsanalyse",
            category: "Beleid",
            contactPerson: ""
        },
        {
            id: "2",
            name: "Onderzoek & Informatie",
            description: "Onderzoek en informatiediensten",
            category: "Onderzoek",
            contactPerson: ""
        },
        {
            id: "3",
            name: "Financiële Analyse",
            description: "Financiële analyse en ondersteuning",
            category: "Financieel",
            contactPerson: ""
        },
        {
            id: "4",
            name: "Digitale Ondersteuning",
            description: "Digitale en technische ondersteuning",
            category: "Technisch",
            contactPerson: ""
        },
        {
            id: "5",
            name: "Facilitaire Diensten",
            description: "Facilitaire ondersteuningsdiensten",
            category: "Facilitair",
            contactPerson: ""
        },
        {
            id: "6",
            name: "Kennismanagement",
            description: "Kennismanagement en -deling",
            category: "Kennis",
            contactPerson: ""
        }
    ];
    
    // Public methods
    return {
        /**
         * Get all available services
         * @param {boolean} forceRefresh - Whether to force refresh from SharePoint
         * @param {boolean} useStaticData - Whether to use static data instead of SharePoint
         * @returns {Promise<Array>} Promise resolving to array of service objects
         */
        getAllServices: async function(forceRefresh = false, useStaticData = false) {
            try {
                // If useStaticData is true, return fallback data directly
                if (useStaticData) {
                    console.log('Using static services data');
                    return _fallbackServices;
                }
                
                // Get services from SharePoint - removed orderBy parameter that was causing issues
                const items = await SharePointDataProvider.getListItems(_servicesListName, {
                    select: _defaultSelect,
                    // orderBy removed to prevent 'Title' field error
                    forceRefresh: forceRefresh,
                    useStaticData: false // Explicitly request SharePoint data
                });
                
                // Map items to service model
                return items.map(_mapToServiceModel);
            } catch (error) {
                // Error already logged by handleError
                
                // Return fallback data
                return _fallbackServices;
            }
        },
        
        /**
         * Get services by category
         * @param {string} category - Category to filter by
         * @returns {Promise<Array>} Promise resolving to array of service objects
         */
        getServicesByCategory: async function(category) {
            try {
                // Get services from SharePoint with filter
                const items = await SharePointDataProvider.getListItems(_servicesListName, {
                    select: _defaultSelect,
                    orderBy: _defaultOrderBy,
                    filter: `Category eq '${category}'`
                });
                
                // Map items to service model
                return items.map(_mapToServiceModel);
            } catch (error) {
                // Error already logged by handleError
                
                // Return filtered fallback data
                return _fallbackServices.filter(service => service.category === category);
            }
        },
        
        /**
         * Get service by ID
         * @param {string} id - Service ID
         * @returns {Promise<Object>} Promise resolving to service object
         */
        getServiceById: async function(id) {
            try {
                // Get service from SharePoint with filter
                const items = await SharePointDataProvider.getListItems(_servicesListName, {
                    select: _defaultSelect,
                    filter: `Id eq ${id}`
                });
                
                // Check if service was found
                if (items.length === 0) {
                    throw new Error(`Service with ID ${id} not found`);
                }
                
                // Map item to service model
                return _mapToServiceModel(items[0]);
            } catch (error) {
                // Error already logged by handleError
                
                // Try to find service in fallback data
                const fallbackService = _fallbackServices.find(service => service.id === id);
                
                if (!fallbackService) {
                    throw new Error(`Service with ID ${id} not found`);
                }
                
                return fallbackService;
            }
        }
    };
})();
