import { markRaw } from 'vue'
import RubriceringStep from '../components/steps/RubriceringStep.vue'
import DienstenStep from '../components/steps/DienstenStep.vue'
import CommissieStep from '../components/steps/CommissieStep.vue'
import EmptyStep from '../components/steps/EmptyStep.vue'
import ProfielAfzenderStep from '../components/steps/ProfielAfzenderStep.vue'

export function useStepConfig() {
  return [
    { name: 'Rubricering', contentControl: 'txtRubricering', component: markRaw(RubriceringStep) },
    { name: '<PERSON><PERSON><PERSON>', contentControl: 'txtDiensten', component: markRaw(DienstenStep) },
    { name: 'Commissies', contentControl: 'txtCommissies', component: markRaw(CommissieStep) },
    {
      name: 'Profiel Afzender',
      contentControl: 'txtAfzenderPersoonlijk',
      component: markRaw(ProfielAfzenderStep),
    },
    { name: '<PERSON><PERSON>', contentControl: 'txtDerdeStap', component: markRaw(EmptyStep) },
  ]
}
