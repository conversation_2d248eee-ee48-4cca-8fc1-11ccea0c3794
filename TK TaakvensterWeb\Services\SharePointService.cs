using Microsoft.Graph;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;

namespace TK_TaakvensterWeb
{
    /// <summary>
    /// Service for handling SharePoint operations
    /// </summary>
    public class SharePointService : ISharePointService
    {
        /// <summary>
        /// Gets a SharePoint site by URL
        /// </summary>
        public async Task<Site> GetSiteByUrl(GraphServiceClient graphClient, string siteUrl, string hostName)
        {
            try
            {
                return await graphClient.Sites.GetByPath(siteUrl, hostName).Request().GetAsync();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting site by URL: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Gets a SharePoint list by name
        /// </summary>
        public async Task<List> GetListByName(GraphServiceClient graphClient, string siteId, string listName)
        {
            try
            {
                var lists = await graphClient.Sites[siteId].Lists.Request().GetAsync();
                return lists.FirstOrDefault(l => l.DisplayName.Equals(listName, StringComparison.OrdinalIgnoreCase));
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting list by name: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Gets SharePoint list items with optional filtering
        /// </summary>
        public async Task<IListItemsCollectionPage> GetListItems(
            GraphServiceClient graphClient, 
            string siteId, 
            string listId, 
            string filter = null, 
            string orderBy = null, 
            int? top = null)
        {
            try
            {
                // Build query options
                var queryOptions = new List<QueryOption>();
                if (!string.IsNullOrEmpty(filter))
                    queryOptions.Add(new QueryOption("$filter", filter));
                
                if (!string.IsNullOrEmpty(orderBy))
                    queryOptions.Add(new QueryOption("$orderby", orderBy));
                
                if (top.HasValue)
                    queryOptions.Add(new QueryOption("$top", top.Value.ToString()));

                // Request items with fields expanded
                return await graphClient
                    .Sites[siteId]
                    .Lists[listId]
                    .Items
                    .Request(queryOptions)
                    .Expand("fields($select=*,Title,LinkTitle,FileLeafRef,LinkFilename,Name,BaseName)")
                    .GetAsync();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting list items: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Processes a SharePoint list item to extract fields
        /// </summary>
        public IDictionary<string, object> ProcessListItem(ListItem item)
        {
              // Create a new dynamic object for the processed item
            // Using ExpandoObject allows for dynamic property access (as we don't know the fields beforehand) and easy serialization to JSON (instead of a key/value )
            // Using "as IDictionary<string, object>" makes it easier to add items as a key/value pair and benefit from LINQ, Collection operations, 
            // better errorhandling (e.g. checking if a key exists with ContainsKey before accessing it ), field enumeration.
            // More info onf serialization:
            // ExpandoObject serializes to:
            //      { "id": "123", "title": "Document" }
            // Dictionary might serialize to:
            //      [{"Key":"id","Value":"123"},{"Key":"title","Value":"Document"}]
            var processedItem = new System.Dynamic.ExpandoObject() as IDictionary<string, object>;
            
            // Add basic item properties
            processedItem["id"] = item.Id;
            processedItem["createdDateTime"] = item.CreatedDateTime;
            processedItem["lastModifiedDateTime"] = item.LastModifiedDateTime;
            
            // Extract fields from AdditionalData if available
            if (item.Fields?.AdditionalData != null)
            {
                foreach (var field in item.Fields.AdditionalData)
                {
                    // Skip internal fields that start with underscore or @ symbol
                    if (field.Key.StartsWith("_") || field.Key.StartsWith("@"))
                        continue;
                        
                    // Process field value based on type
                    if (field.Value is JsonElement jsonElement)
                    {
                        switch (jsonElement.ValueKind)
                        {
                            case JsonValueKind.String:
                                processedItem[field.Key] = jsonElement.GetString();
                                break;
                            case JsonValueKind.Number:
                                // Convert to appropriate numeric type
                                if (jsonElement.TryGetInt32(out int intValue))
                                    processedItem[field.Key] = intValue;
                                else if (jsonElement.TryGetInt64(out long longValue))
                                    processedItem[field.Key] = longValue;
                                else
                                    processedItem[field.Key] = jsonElement.GetDouble();
                                break;
                            case JsonValueKind.True:
                                processedItem[field.Key] = true;
                                break;
                            case JsonValueKind.False:
                                processedItem[field.Key] = false;
                                break;
                            case JsonValueKind.Null:
                                processedItem[field.Key] = null;
                                break;
                            default:
                                processedItem[field.Key] = jsonElement.ToString();
                                break;
                        }
                    }
                    else
                    {
                        processedItem[field.Key] = field.Value;
                    }
                }
                
                // Ensure Title is always available
                if (!processedItem.ContainsKey("Title"))
                {
                    // Try common field names for title in SharePoint
                    string[] possibleTitleFields = new[] { "Title", "LinkTitle", "FileLeafRef", "LinkFilename", "Name", "BaseName" };
                    
                    foreach (var fieldName in possibleTitleFields)
                    {
                        if (item.Fields.AdditionalData.TryGetValue(fieldName, out var titleValue))
                        {
                            processedItem["Title"] = (titleValue is JsonElement titleElement && titleElement.ValueKind == JsonValueKind.String) 
                                ? titleElement.GetString() 
                                : titleValue?.ToString();
                            break;
                        }
                    }
                    
                    // If still no title found, use the ID
                    if (!processedItem.ContainsKey("Title"))
                        processedItem["Title"] = $"Item {item.Id}";
                }
            }
            
            return processedItem;
        }
    }
}
