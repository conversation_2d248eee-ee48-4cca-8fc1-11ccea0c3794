<!DOCTYPE html>
<html>
<head>
    <title>API Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        button { padding: 10px; margin: 10px 0; }
        #result { margin-top: 20px; border: 1px solid #ccc; padding: 10px; white-space: pre-wrap; }
        textarea { width: 100%; height: 100px; }
        .section { margin-bottom: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        h2 { margin-top: 0; }
    </style>
    <!-- Add MSAL.js library -->
    <script src="https://alcdn.msauth.net/browser/2.30.0/js/msal-browser.min.js"></script>
</head>
<body>
    <h1>Test SharePoint List API</h1>
    
    <div class="section">
        <h2>1. Get Access Token</h2>
        <div>
            <p>Get an access token using MSAL.js:</p>
            <div>
                <input type="text" id="clientId" placeholder="Enter Azure AD Client ID" value="a6bff1ae-205b-4896-9794-2b3c7db472a7" style="width: 300px;" />
            </div>
            <div style="margin-top: 10px;">
                <input type="text" id="tenantId" placeholder="Enter Tenant ID" value="9fa1ed77-7e0c-4655-9891-aeeadbeb914d" style="width: 300px;" />
            </div>
            <div style="margin-top: 10px;">
                <div>
                    <label><input type="radio" name="scopeOption" value="api" checked> API Scope Only (clientId/.default)</label>
                </div>
                <div>
                    <label><input type="radio" name="scopeOption" value="graph"> Graph Scope Only (graph.microsoft.com/.default)</label>
                </div>
                <div>
                    <label><input type="radio" name="scopeOption" value="both"> Both Scopes</label>
                </div>
            </div>
            <div style="margin-top: 10px;">
                <button id="getToken">Get Token</button>
                <span id="tokenStatus"></span>
            </div>
        </div>
        <div style="margin-top: 10px;">
            <h3>Access Token</h3>
            <textarea id="token" placeholder="Token will appear here or paste your access token"></textarea>
        </div>
    </div>
    
    <div class="section">
        <h2>2. Test API Endpoint</h2>
        <div>
            <h3>Request Parameters</h3>
            <div style="margin-top: 10px;">
                <label>Site URL:</label>
                <input type="text" id="siteUrl" value="https://denobelsoftware.sharepoint.com/sites/DNSTest" style="width: 400px;" />
            </div>
            <div style="margin-top: 10px;">
                <label>List Name:</label>
                <input type="text" id="listName" value="TestLijst" style="width: 200px;" />
            </div>
        </div>
        
        <button id="testApi" style="margin-top: 15px;">Test API</button>
    </div>
    
    <div class="section">
        <h2>3. Test Direct Graph API</h2>
        <p>Test the token directly against Microsoft Graph API:</p>
        <button id="testDirectGraph" style="margin-top: 10px;">Test Direct Graph API</button>
        <div style="margin-top: 10px;">
            <button id="decodeToken">Decode Token Claims</button>
        </div>
    </div>
    
    <div class="section">
        <h2>4. Results</h2>
        <pre id="result">Results will appear here...</pre>
    </div>
    
    <script>
        // Helper function to decode JWT token
        function parseJwt(token) {
            try {
                const base64Url = token.split('.')[1];
                const base64 = base64Url.replace(/-/g, '+').replace(/_/g, '/');
                const jsonPayload = decodeURIComponent(atob(base64).split('').map(function(c) {
                    return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2);
                }).join(''));
                
                return JSON.parse(jsonPayload);
            } catch (e) {
                console.error('Error parsing JWT token:', e);
                return null;
            }
        }
        // MSAL configuration
        let msalInstance = null;
        
        // Initialize MSAL
        function initializeMsal() {
            const clientId = document.getElementById('clientId').value;
            const tenantId = document.getElementById('tenantId').value;
            
            if (!clientId || !tenantId) {
                alert('Please enter Client ID and Tenant ID');
                return false;
            }
            
            const msalConfig = {
                auth: {
                    clientId: clientId,
                    authority: `https://login.microsoftonline.com/${tenantId}`,
                    redirectUri: window.location.origin + window.location.pathname,
                },
                cache: {
                    cacheLocation: 'sessionStorage',
                    storeAuthStateInCookie: false
                }
            };
            
            msalInstance = new msal.PublicClientApplication(msalConfig);
            return true;
        }
        
        // Get token using MSAL
        document.getElementById('getToken').addEventListener('click', async function() {
            const statusElement = document.getElementById('tokenStatus');
            statusElement.textContent = 'Initializing...';
            
            if (!initializeMsal()) {
                statusElement.textContent = 'Failed to initialize MSAL';
                return;
            }
            
            statusElement.textContent = 'Requesting token...';
            
            try {
                // Define login request for backend API
                const clientIdValue = document.getElementById('clientId').value;
                
                // Get the selected scope option
                const scopeOption = document.querySelector('input[name="scopeOption"]:checked').value;
                let scopes = [];
                
                switch(scopeOption) {
                    case 'api':
                        // Request token with your API as audience
                        scopes = [`${clientIdValue}/.default`];
                        break;
                    case 'graph':
                        // Request token with Microsoft Graph as audience
                        scopes = ['https://graph.microsoft.com/.default'];
                        break;
                    case 'both':
                        // Request token with both scopes
                        scopes = [`${clientIdValue}/.default`, 'https://graph.microsoft.com/.default'];
                        break;
                }
                
                const loginRequest = {
                    scopes: scopes
                };
                
                // Login and get token
                const response = await msalInstance.loginPopup(loginRequest);
                
                if (response && response.accessToken) {
                    document.getElementById('token').value = response.accessToken;
                    statusElement.textContent = 'Token acquired successfully!';
                } else {
                    statusElement.textContent = 'Failed to get token';
                }
            } catch (error) {
                console.error('Error getting token:', error);
                statusElement.textContent = `Error: ${error.message}`;
            }
        });
        
        // Decode token claims
        document.getElementById('decodeToken').addEventListener('click', function() {
            const token = document.getElementById('token').value;
            const resultElement = document.getElementById('result');
            
            if (!token) {
                resultElement.textContent = 'No token available to decode.';
                return;
            }
            
            try {
                const decodedToken = parseJwt(token);
                if (decodedToken) {
                    resultElement.textContent = 'Token Claims:\n' + JSON.stringify(decodedToken, null, 2);
                    
                    // Highlight important claims
                    const audienceClaim = decodedToken.aud || 'Not found';
                    const scopeClaim = decodedToken.scp || 'Not found';
                    const issuerClaim = decodedToken.iss || 'Not found';
                    
                    resultElement.textContent += '\n\nImportant Claims:\n';
                    resultElement.textContent += `Audience (aud): ${audienceClaim}\n`;
                    resultElement.textContent += `Scope (scp): ${scopeClaim}\n`;
                    resultElement.textContent += `Issuer (iss): ${issuerClaim}\n`;
                } else {
                    resultElement.textContent = 'Failed to decode token.';
                }
            } catch (error) {
                resultElement.textContent = 'Error decoding token: ' + error.message;
            }
        });
        
        // Test Direct Graph API
        document.getElementById('testDirectGraph').addEventListener('click', async function() {
            const token = document.getElementById('token').value;
            const siteUrl = document.getElementById('siteUrl').value;
            
            if (!token) {
                alert('Please enter an access token');
                return;
            }
            
            const resultElement = document.getElementById('result');
            resultElement.textContent = 'Testing direct Graph API call...';
            
            try {
                // Extract site ID from site URL
                const siteUrlParts = siteUrl.split('/');
                const siteName = siteUrlParts[siteUrlParts.length - 1];
                
                // Call Microsoft Graph API directly
                const response = await fetch(`https://graph.microsoft.com/v1.0/sites/root:/sites/${siteName}:/lists`, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Accept': 'application/json'
                    }
                });
                
                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`Graph API Error (${response.status}): ${errorText}`);
                }
                
                const data = await response.json();
                resultElement.textContent = 'Direct Graph API call successful:\n' + JSON.stringify(data, null, 2);
            } catch (error) {
                resultElement.textContent = 'Direct Graph API Error: ' + error.message;
            }
        });
        
        // Test API endpoint
        document.getElementById('testApi').addEventListener('click', async function testApi() {
            const token = document.getElementById('token').value;
            const siteUrl = document.getElementById('siteUrl').value;
            const listName = document.getElementById('listName').value;
            const resultElement = document.getElementById('result');
            
            if (!token) {
                resultElement.textContent = 'No token available. Get a token first.';
                return;
            }
            
            if (!siteUrl || !listName) {
                resultElement.textContent = 'Please enter Site URL and List Name.';
                return;
            }
            
            resultElement.textContent = 'Calling API...';
            
            try {
                // Extract domain from site URL
                const urlParts = siteUrl.match(/https:\/\/([^/]+)\/sites\/([^/]+)/);
                if (!urlParts || urlParts.length < 3) {
                    resultElement.textContent = `Invalid SharePoint site URL format: ${siteUrl}\nExpected format: https://domain.sharepoint.com/sites/sitename`;
                    return;
                }
                
                const domain = urlParts[1]; // e.g., denobelsoftware.sharepoint.com
                const siteName = urlParts[2]; // e.g., DNSTest
                
                const response = await fetch('/api/graph/getlist', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ 
                        token: token,
                        siteUrl: `/sites/${siteName}`,
                        hostName: domain,
                        listName: listName
                    })
                });
                
                const data = await response.text();
                const isJson = data.startsWith('{') || data.startsWith('[');
                
                if (isJson) {
                    try {
                        const jsonData = JSON.parse(data);
                        
                        // Check if we have value array (standard Graph API response format)
                        if (jsonData.value && Array.isArray(jsonData.value)) {
                            // Create a table to display the items
                            let tableHtml = '<h3>SharePoint List Items</h3>';
                            tableHtml += '<table border="1" style="border-collapse: collapse; width: 100%;">';
                            tableHtml += '<thead><tr><th>ID</th><th>Title</th><th>Created</th><th>Modified</th><th>All Fields</th></tr></thead>';
                            tableHtml += '<tbody>';
                            
                            jsonData.value.forEach((item, index) => {
                                // Extract fields from item
                                let title = 'N/A';
                                let created = 'N/A';
                                let modified = 'N/A';
                                let id = item.id || 'N/A';
                                let fieldsJson = '{}';
                                
                                // Try to extract fields from different possible locations
                                if (item.fields) {
                                    if (item.fields.additionalData) {
                                        // Extract from additionalData
                                        const fields = item.fields.additionalData;
                                        title = fields.Title || fields.LinkTitle || 'N/A';
                                        created = fields.Created || 'N/A';
                                        modified = fields.Modified || 'N/A';
                                        fieldsJson = JSON.stringify(fields, null, 2);
                                    }
                                } else {
                                    // Item might already be processed
                                    title = item.Title || 'N/A';
                                    created = item.createdDateTime || item.Created || 'N/A';
                                    modified = item.lastModifiedDateTime || item.Modified || 'N/A';
                                    fieldsJson = JSON.stringify(item, null, 2);
                                }
                                
                                tableHtml += `<tr>
                                    <td>${id}</td>
                                    <td>${title}</td>
                                    <td>${created}</td>
                                    <td>${modified}</td>
                                    <td><pre style="max-height: 100px; overflow: auto;">${fieldsJson}</pre></td>
                                </tr>`;
                            });
                            
                            tableHtml += '</tbody></table>';
                            tableHtml += `<p>Total items: ${jsonData.value.length}</p>`;
                            
                            // Display the table
                            resultElement.innerHTML = tableHtml;
                        } else {
                            // Fall back to standard JSON display
                            resultElement.textContent = `API Response (JSON):\n${JSON.stringify(jsonData, null, 2)}`;
                        }
                    } catch (jsonError) {
                        resultElement.textContent = `API Response (failed to parse as JSON):\n${data}`;
                    }
                } else {
                    resultElement.textContent = `API Response:\n${data}`;
                }
                
                if (!response.ok) {
                    resultElement.textContent = `API Error (${response.status}):\n${data}`;
                }
            } catch (error) {
                resultElement.textContent = `Error: ${error.message}`;
            }
        });
    </script>
</body>
</html>
