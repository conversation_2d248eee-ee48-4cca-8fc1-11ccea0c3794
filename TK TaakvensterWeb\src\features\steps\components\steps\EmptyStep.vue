<template>
  <div id="panel-3">
      <p class="ms-font-xl ms-fontColor-neutralPrimary ms-fontWeight-semilight" id="thirdStepTitle">{{ stepTitle }}</p>
      <p class="ms-font-m ms-fontColor-neutralSecondary" id="thirdStepDesc">{{ stepDescription }}</p>
      <div class="button-container">
        <button class="ms-Button ms-Button--default" @click="emit('step-previous')">Vorige</button>
      </div>
  </div>
</template>

<script setup>
import { defineEmits, onMounted } from 'vue';

// Self-contained step information (no props needed)
const stepTitle = 'Laatste Stap';
const stepDescription = 'Dit is de laatste stap van het proces. U kunt teruggaan of het proces afsluiten.';

// New minimal event system
const emit = defineEmits({
  'step-ready': null,        // Component loaded and ready
  'step-complete': null,     // User completed this step
  'step-previous': null      // User wants to go back
});

// Signal that component is ready (like VBA UserForm_Initialize)
onMounted(() => {
  emit('step-ready');
});
</script>

<style scoped>
/* Scoped styles for EmptyStep */
.button-container {
  margin-top: 1rem;
  display: flex;
  justify-content: space-between;
}
</style>
