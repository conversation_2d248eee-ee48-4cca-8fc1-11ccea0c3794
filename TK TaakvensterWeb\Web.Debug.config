<?xml version="1.0" encoding="utf-8"?>

<!-- For more information on using web.config transformation visit https://go.microsoft.com/fwlink/?LinkId=125889 -->

<configuration xmlns:xdt="http://schemas.microsoft.com/XML-Document-Transform">
  <!--
    In the example below, the "SetAttributes" transform will change the value of 
    "connectionString" to use "ReleaseSQLServer" only when the "Match" locator 
    finds an attribute "name" that has a value of "MyDB".
    
    <connectionStrings>
      <add name="MyDB" 
        connectionString="Data Source=ReleaseSQLServer;Initial Catalog=MyReleaseDB;Integrated Security=True" 
        xdt:Transform="SetAttributes" xdt:Locator="Match(name)"/>
    </connectionStrings>
  -->
  <system.web>
    <!--
      In the example below, the "Replace" transform will replace the entire 
      <customErrors> section of your web.config file.
      Note that because there is only one customErrors section under the 
      <system.web> node, there is no need to use the "xdt:Locator" attribute.
      
      <customErrors defaultRedirect="GenericError.htm"
        mode="RemoteOnly" xdt:Transform="Replace">
        <error statusCode="500" redirect="InternalError.htm"/>
      </customErrors>
    -->
  </system.web>
  <system.webServer xdt:Transform="InsertIfMissing">
    <staticContent xdt:Transform="InsertIfMissing">
      <clientCache cacheControlMode="DisableCache" xdt:Transform="SetAttributes"/>
    </staticContent>
    <httpProtocol xdt:Transform="InsertIfMissing">
      <customHeaders xdt:Transform="InsertIfMissing">
        <add name="Cache-Control" value="no-cache, no-store, must-revalidate" xdt:Transform="InsertIfMissing"/>
        <add name="Pragma" value="no-cache" xdt:Transform="InsertIfMissing"/>
        <add name="Expires" value="0" xdt:Transform="InsertIfMissing"/>
      </customHeaders>
    </httpProtocol>
  </system.webServer>
</configuration>