<template>
  <ul class="text-sm font-medium text-gray-900 bg-white border border-gray-200 rounded-lg w-full">
    <li 
      v-for="(item, index) in items" 
      :key="index"
      :class="{
        'text-white bg-blue-700 rounded-t-lg': selectedIndex === index && index === 0,
        'text-white bg-blue-700': selectedIndex === index && index !== 0 && index !== items.length - 1,
        'text-white bg-blue-700 rounded-b-lg': selectedIndex === index && index === items.length - 1,
        'rounded-t-lg': index === 0 && selectedIndex !== index,
        'rounded-b-lg': index === items.length - 1 && selectedIndex !== index
      }"
      class="block w-full px-4 py-2 border-b border-gray-200 cursor-pointer hover:bg-gray-100 hover:text-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-700 focus:text-blue-700"
      @click="handleItemClick(index)">
      <slot name="item" :item="item" :index="index">
        {{ item.label || item }}
      </slot>
    </li>
  </ul>
</template>

<script setup>
import { ref, watch } from 'vue';

const props = defineProps({
  items: {
    type: Array,
    required: true
  },
  modelValue: {
    type: Number,
    default: null
  }
});

const emit = defineEmits(['update:modelValue', 'item-click']);

// Use v-model compatible approach
const selectedIndex = ref(props.modelValue);

// Watch for external changes to modelValue
watch(() => props.modelValue, (newValue) => {
  selectedIndex.value = newValue;
});

function handleItemClick(index) {
  // Update local state immediately
  selectedIndex.value = index;
  // Emit events
  emit('update:modelValue', index);
  emit('item-click', { index, item: props.items[index] });
}
</script>

<style scoped>
/* Remove the bottom border from the last item */
ul li:last-child {
  border-bottom: none;
}

/* Ensure proper spacing between items */
li {
  transition: all 0.2s ease;
}

/* Ensure text wraps properly */
li {
  white-space: normal;
  line-height: 1.3;
}
</style>
