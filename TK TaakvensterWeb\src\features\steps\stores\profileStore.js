import { defineStore } from 'pinia'
import {
  profileData,
  getProfileByIndex,
  formatProfileDisplayName,
} from '../../../data/profileData.js'
import { useLogger } from '../../shared'
import { DataValidationService } from '../../../services/DataValidationService.js'

// Cache variables for profilesForDisplay optimization
let _cachedDisplayProfiles = null
let _lastValidProfilesHash = null

/**
 * Create a simple hash of the profiles to detect changes
 * @param {Array} profiles - Array of profile objects
 * @returns {string} Hash string representing the profiles
 */
function createProfilesHash(profiles) {
  return profiles.length + '_' + profiles.map((p) => `${p.FirstName}${p.LastName}`).join('_')
}

/**
 * Pinia store for managing profile data in ProfielAfzender step
 *
 * This store handles profile data loading, validation, and filtering.
 * It's designed to be easily switched from static data to API calls
 * in the future.
 */
export const useProfileStore = defineStore('profile', {
  state: () => ({
    /**
     * Raw profile data (including potentially invalid profiles)
     */
    rawProfiles: [],

    /**
     * Validated and filtered profiles ready for display
     */
    validProfiles: [],

    /**
     * Currently selected profile index
     */
    selectedProfileIndex: null,

    /**
     * Loading state for future API integration
     */
    isLoading: false,

    /**
     * Error state for data loading
     */
    error: null,
  }),

  getters: {
    /**
     * Get the currently selected profile
     * @returns {Object|null} Selected profile or null
     */
    selectedProfile: (state) => {
      if (state.selectedProfileIndex === null || state.selectedProfileIndex < 0) {
        return null
      }
      return state.validProfiles[state.selectedProfileIndex] || null
    },

    /**
     * Get profiles formatted for display in FlowbiteList
     * Uses caching to prevent unnecessary recalculations
     * @returns {Array} Array of profile objects with label property
     */
    profilesForDisplay: (state) => {
      const currentHash = createProfilesHash(state.validProfiles)

      // Only recalculate if profiles actually changed
      if (_cachedDisplayProfiles === null || _lastValidProfilesHash !== currentHash) {
        _cachedDisplayProfiles = state.validProfiles.map((profile, index) => ({
          ...profile,
          label: formatProfileDisplayName(profile),
          index: index,
        }))
        _lastValidProfilesHash = currentHash
      }

      return _cachedDisplayProfiles
    },

    /**
     * Get number of valid profiles
     * @returns {number} Count of valid profiles
     */
    profileCount: (state) => state.validProfiles.length,

    /**
     * Get number of invalid profiles that were filtered out
     * @returns {number} Count of invalid profiles
     */
    invalidProfileCount: (state) => state.rawProfiles.length - state.validProfiles.length,

    /**
     * Check if any profiles are available
     * @returns {boolean} True if profiles are available
     */
    hasProfiles: (state) => state.validProfiles.length > 0,

    /**
     * Get profiles by commission ID
     * @returns {Function} Function that takes commissionId and returns filtered profiles
     */
    getProfilesByCommissionId: (state) => (commissionId) => {
      return state.validProfiles.filter((profile) => profile.CommissionId === commissionId)
    },
  },

  actions: {
    /**
     * Load profile data (currently from static data, future: API)
     * @param {boolean} forceReload - Force reload even if data exists
     */
    async loadProfiles(forceReload = false) {
      const { warn, error } = useLogger()

      // Skip loading if data already exists and not forcing reload
      if (this.validProfiles.length > 0 && !forceReload) {
        return
      }

      try {
        this.isLoading = true
        this.error = null

        // Invalidate cache
        _cachedDisplayProfiles = null
        _lastValidProfilesHash = null

        // TODO: Replace with API call in the future
        // const response = await api.getProfiles()
        // this.rawProfiles = response.data

        // For now, use static data
        this.rawProfiles = [...profileData]

        // Validate and filter profiles
        this.validProfiles = this._validateAndFilterProfiles(this.rawProfiles)

        // Log validation results (warnings are now logged directly in validation)
        const validCount = this.validProfiles.length
        const totalCount = this.rawProfiles.length
        if (validCount < totalCount) {
          warn(`Profile validation: ${validCount}/${totalCount} profiles are valid`)
        }
      } catch (err) {
        this.error = `Failed to load profiles: ${err.message}`
        error(this.error)
        throw err
      } finally {
        this.isLoading = false
      }
    },

    /**
     * Select a profile by index
     * @param {number} index - Index of the profile to select
     * @returns {Object|null} Selected profile or null if invalid
     */
    selectProfile(index) {
      if (
        index === null ||
        index === undefined ||
        index < 0 ||
        index >= this.validProfiles.length
      ) {
        this.selectedProfileIndex = null
        return null
      }

      this.selectedProfileIndex = index
      return this.selectedProfile
    },

    /**
     * Clear the current selection
     */
    clearSelection() {
      this.selectedProfileIndex = null
    },

    /**
     * Get profile by index (without selecting it)
     * @param {number} index - Profile index
     * @returns {Object|null} Profile or null if not found
     */
    getProfileByIndex(index) {
      return getProfileByIndex(index)
    },

    /**
     * Search profiles by name
     * @param {string} searchTerm - Search term to filter by
     * @returns {Array} Filtered profiles
     */
    searchProfiles(searchTerm) {
      if (!searchTerm || searchTerm.trim() === '') {
        return this.validProfiles
      }

      const term = searchTerm.toLowerCase().trim()
      return this.validProfiles.filter((profile) => {
        const fullName = `${profile.FirstName} ${profile.LastName}`.toLowerCase()
        const role = (profile.Role || '').toLowerCase()
        return fullName.includes(term) || role.includes(term)
      })
    },

    /**
     * Validate and filter profiles using DataValidationService
     * @private
     * @param {Array} profiles - Raw profile data
     * @returns {Array} Valid profiles
     */
    _validateAndFilterProfiles(profiles) {
      const validProfiles = []

      profiles.forEach((profile, index) => {
        const validation = DataValidationService.validateProfile(profile, index)

        if (!validation.isValid) {
          // Log validation errors to console only (no warnings array)
          validation.errors.forEach((error) => console.warn(error))
          return
        }

        validProfiles.push(validation.validatedData)
      })

      return validProfiles
    },

    /**
     * Reset store to initial state
     */
    reset() {
      this.rawProfiles = []
      this.validProfiles = []
      this.selectedProfileIndex = null
      this.isLoading = false
      this.error = null

      // Invalidate cache
      _cachedDisplayProfiles = null
      _lastValidProfilesHash = null
    },
  },
})
