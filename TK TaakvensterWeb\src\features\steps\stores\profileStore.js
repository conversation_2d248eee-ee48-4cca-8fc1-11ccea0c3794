import { defineStore } from 'pinia'
import {
  profileData,
  getProfileByIndex,
  formatProfileDisplayName,
} from '../../../data/profileData.js'
import { useLogger } from '../../shared'

/**
 * Pinia store for managing profile data in ProfielAfzender step
 *
 * This store handles profile data loading, validation, and filtering.
 * It's designed to be easily switched from static data to API calls
 * in the future.
 */
export const useProfileStore = defineStore('profile', {
  state: () => ({
    /**
     * Raw profile data (including potentially invalid profiles)
     */
    rawProfiles: [],

    /**
     * Validated and filtered profiles ready for display
     */
    validProfiles: [],

    /**
     * Currently selected profile index
     */
    selectedProfileIndex: null,

    /**
     * Loading state for future API integration
     */
    isLoading: false,

    /**
     * Error state for data loading
     */
    error: null,

    /**
     * Validation warnings collected during data processing
     */
    validationWarnings: [],
  }),

  getters: {
    /**
     * Get the currently selected profile
     * @returns {Object|null} Selected profile or null
     */
    selectedProfile: (state) => {
      if (state.selectedProfileIndex === null || state.selectedProfileIndex < 0) {
        return null
      }
      return state.validProfiles[state.selectedProfileIndex] || null
    },

    /**
     * Get profiles formatted for display in FlowbiteList
     * @returns {Array} Array of profile objects with label property
     */
    profilesForDisplay: (state) => {
      return state.validProfiles.map((profile, index) => ({
        ...profile,
        label: formatProfileDisplayName(profile),
        index: index,
      }))
    },

    /**
     * Get number of valid profiles
     * @returns {number} Count of valid profiles
     */
    profileCount: (state) => state.validProfiles.length,

    /**
     * Get number of invalid profiles that were filtered out
     * @returns {number} Count of invalid profiles
     */
    invalidProfileCount: (state) => state.rawProfiles.length - state.validProfiles.length,

    /**
     * Check if any profiles are available
     * @returns {boolean} True if profiles are available
     */
    hasProfiles: (state) => state.validProfiles.length > 0,

    /**
     * Get profiles by commission ID
     * @returns {Function} Function that takes commissionId and returns filtered profiles
     */
    getProfilesByCommissionId: (state) => (commissionId) => {
      return state.validProfiles.filter((profile) => profile.CommissionId === commissionId)
    },
  },

  actions: {
    /**
     * Load profile data (currently from static data, future: API)
     * @param {boolean} forceReload - Force reload even if data exists
     */
    async loadProfiles(forceReload = false) {
      const { warn, error } = useLogger()

      // Skip loading if data already exists and not forcing reload
      if (this.validProfiles.length > 0 && !forceReload) {
        return
      }

      try {
        this.isLoading = true
        this.error = null
        this.validationWarnings = []

        // TODO: Replace with API call in the future
        // const response = await api.getProfiles()
        // this.rawProfiles = response.data

        // For now, use static data
        this.rawProfiles = [...profileData]

        // Validate and filter profiles
        this.validProfiles = this._validateAndFilterProfiles(this.rawProfiles)

        // Log validation results
        if (this.validationWarnings.length > 0) {
          warn(`Profile validation warnings: ${this.validationWarnings.length} profiles had issues`)
          this.validationWarnings.forEach((warning) => warn(warning))
        }
      } catch (err) {
        this.error = `Failed to load profiles: ${err.message}`
        error(this.error)
        throw err
      } finally {
        this.isLoading = false
      }
    },

    /**
     * Select a profile by index
     * @param {number} index - Index of the profile to select
     * @returns {Object|null} Selected profile or null if invalid
     */
    selectProfile(index) {
      if (
        index === null ||
        index === undefined ||
        index < 0 ||
        index >= this.validProfiles.length
      ) {
        this.selectedProfileIndex = null
        return null
      }

      this.selectedProfileIndex = index
      return this.selectedProfile
    },

    /**
     * Clear the current selection
     */
    clearSelection() {
      this.selectedProfileIndex = null
    },

    /**
     * Get profile by index (without selecting it)
     * @param {number} index - Profile index
     * @returns {Object|null} Profile or null if not found
     */
    getProfileByIndex(index) {
      return getProfileByIndex(index)
    },

    /**
     * Search profiles by name
     * @param {string} searchTerm - Search term to filter by
     * @returns {Array} Filtered profiles
     */
    searchProfiles(searchTerm) {
      if (!searchTerm || searchTerm.trim() === '') {
        return this.validProfiles
      }

      const term = searchTerm.toLowerCase().trim()
      return this.validProfiles.filter((profile) => {
        const fullName = `${profile.FirstName} ${profile.LastName}`.toLowerCase()
        const role = (profile.Role || '').toLowerCase()
        return fullName.includes(term) || role.includes(term)
      })
    },

    /**
     * Validate and filter profiles, collecting warnings for invalid ones
     * @private
     * @param {Array} profiles - Raw profile data
     * @returns {Array} Valid profiles
     */
    _validateAndFilterProfiles(profiles) {
      const validProfiles = []

      profiles.forEach((profile, index) => {
        // Check required fields
        if (!profile.FirstName || profile.FirstName.trim() === '') {
          this.validationWarnings.push(`Profile at index ${index}: Missing or empty FirstName`)
          return
        }

        if (!profile.LastName || profile.LastName.trim() === '') {
          this.validationWarnings.push(`Profile at index ${index}: Missing or empty LastName`)
          return
        }

        // Profile is valid, add to valid list
        validProfiles.push({
          ...profile,
          // Ensure optional fields are properly handled
          Role: profile.Role || '',
          EmailAddress: profile.EmailAddress || '',
          CommissionId: profile.CommissionId || '',
        })
      })

      return validProfiles
    },

    /**
     * Reset store to initial state
     */
    reset() {
      this.rawProfiles = []
      this.validProfiles = []
      this.selectedProfileIndex = null
      this.isLoading = false
      this.error = null
      this.validationWarnings = []
    },
  },
})
