// WordDocumentUtils - Word-specific document manipulation utilities for Office Add-ins
const WordDocumentUtils = (function() {
    // Insert text into a content control
    async function insertTextInContentControl(contentControlTag, text) {
        try {
            // Always write to debug box for visibility
            console.log(text, contentControlTag);
            
            // If CC Replacement mode is OFF, don't update the actual content control
            if (!window.DebugUtils.getCCReplacementMode()) {
                return true;
            }
            
            // Get the context
            const context = new Word.RequestContext();
            
            // Get all content controls
            const contentControls = context.document.contentControls.getByTag(contentControlTag);
            context.load(contentControls, 'items');
            
            // Sync to get the content controls
            await context.sync();
            
            // Check if any content controls with the tag exist
            if (contentControls.items.length === 0) {
                console.log(`No content controls found with tag: ${contentControlTag}`);
                return false;
            }
            
            // Insert text in each content control
            // (Note: multiple controls with the same tag can exist, that's why we have to iterate)
            for (let i = 0; i < contentControls.items.length; i++) {
                contentControls.items[i].insertText(text, 'Replace');
            }
            
            // Sync the document
            await context.sync();
            
            // Log success
            console.log(`Successfully inserted text into ${contentControls.items.length} content control(s) with tag: ${contentControlTag}`);
            return true;
        } catch (error) {
            console.log(`Error inserting text: ${error.message}`);
            throw error;
        }
    }
    
    // Public API
    return {
        insertTextInContentControl
    };
})();

// Make it globally accessible
window.WordDocumentUtils = WordDocumentUtils;
