/**
 * DataValidationService - Centralized data validation for the TK Taakvenster application
 *
 * This service provides consistent validation logic for all data types including
 * profiles, commissions, diensten, and future data types. It uses the configuration
 * from appConfig.js to determine validation rules.
 */

import { getValidationConfig } from '../config/appConfig.js'

export class DataValidationService {
  /**
   * Validate a profile object
   * @param {Object} profile - Profile object to validate
   * @param {number|null} index - Optional index for error reporting
   * @returns {Object} Validation result with isValid, errors, and validatedData
   */
  static validateProfile(profile, index = null) {
    const config = getValidationConfig().profile
    const errors = []

    // Handle null/undefined profile
    if (!profile || typeof profile !== 'object') {
      const location = index !== null ? ` at index ${index}` : ''
      errors.push(`Profile${location}: Invalid profile object`)
      return {
        isValid: false,
        errors,
        validatedData: null,
      }
    }

    // Check required fields
    config.requiredFields.forEach((field) => {
      if (!profile[field] || typeof profile[field] !== 'string' || profile[field].trim() === '') {
        const location = index !== null ? ` at index ${index}` : ''
        errors.push(`Profile${location}: Missing or empty ${field}`)
      }
    })

    return {
      isValid: errors.length === 0,
      errors,
      validatedData: this._normalizeProfile(profile, config),
    }
  }

  /**
   * Validate a commission object
   * @param {Object} commission - Commission object to validate
   * @param {number|null} index - Optional index for error reporting
   * @returns {Object} Validation result with isValid, errors, and validatedData
   */
  static validateCommission(commission, index = null) {
    const config = getValidationConfig().commission
    const errors = []

    // Handle null/undefined commission
    if (!commission || typeof commission !== 'object') {
      const location = index !== null ? ` at index ${index}` : ''
      errors.push(`Commission${location}: Invalid commission object`)
      return {
        isValid: false,
        errors,
        validatedData: null,
      }
    }

    // Check required fields
    config.requiredFields.forEach((field) => {
      if (
        !commission[field] ||
        typeof commission[field] !== 'string' ||
        commission[field].trim() === ''
      ) {
        const location = index !== null ? ` at index ${index}` : ''
        errors.push(`Commission${location}: Missing or empty ${field}`)
      }
    })

    return {
      isValid: errors.length === 0,
      errors,
      validatedData: this._normalizeCommission(commission, config),
    }
  }

  /**
   * Validate a dienst object (future implementation)
   * @param {Object} dienst - Dienst object to validate
   * @param {number|null} index - Optional index for error reporting
   * @returns {Object} Validation result with isValid, errors, and validatedData
   */
  static validateDienst(dienst, index = null) {
    // TODO: Implement when dienst validation is needed
    return {
      isValid: true,
      errors: [],
      validatedData: dienst,
    }
  }

  /**
   * Validate a rubricering object (future implementation)
   * @param {Object} rubricering - Rubricering object to validate
   * @param {number|null} index - Optional index for error reporting
   * @returns {Object} Validation result with isValid, errors, and validatedData
   */
  static validateRubricering(rubricering, index = null) {
    // TODO: Implement when rubricering validation is needed
    return {
      isValid: true,
      errors: [],
      validatedData: rubricering,
    }
  }

  /**
   * Normalize profile data by ensuring all optional fields exist
   * @param {Object} profile - Raw profile object
   * @param {Object} config - Validation configuration
   * @returns {Object} Normalized profile object
   * @private
   */
  static _normalizeProfile(profile, config) {
    const normalized = { ...profile }

    // Ensure optional fields exist as empty strings
    config.optionalFields.forEach((field) => {
      normalized[field] = profile[field] || ''
    })

    return normalized
  }

  /**
   * Normalize commission data by ensuring all optional fields exist
   * @param {Object} commission - Raw commission object
   * @param {Object} config - Validation configuration
   * @returns {Object} Normalized commission object
   * @private
   */
  static _normalizeCommission(commission, config) {
    const normalized = { ...commission }

    // Ensure optional fields exist as empty strings
    config.optionalFields.forEach((field) => {
      normalized[field] = commission[field] || ''
    })

    return normalized
  }

  /**
   * Validate an array of objects using the appropriate validator
   * @param {Array} items - Array of objects to validate
   * @param {string} type - Type of validation ('profile', 'commission', 'dienst', 'rubricering')
   * @returns {Object} Validation result with validItems, invalidItems, and errors
   */
  static validateArray(items, type) {
    const validItems = []
    const invalidItems = []
    const allErrors = []

    const validator = this._getValidator(type)
    if (!validator) {
      throw new Error(`Unknown validation type: ${type}`)
    }

    items.forEach((item, index) => {
      const result = validator.call(this, item, index)

      if (result.isValid) {
        validItems.push(result.validatedData)
      } else {
        invalidItems.push({ item, errors: result.errors, index })
        allErrors.push(...result.errors)
      }
    })

    return {
      validItems,
      invalidItems,
      errors: allErrors,
      validCount: validItems.length,
      invalidCount: invalidItems.length,
    }
  }

  /**
   * Get the appropriate validator function for a type
   * @param {string} type - Validation type
   * @returns {Function|null} Validator function
   * @private
   */
  static _getValidator(type) {
    const validators = {
      profile: this.validateProfile,
      commission: this.validateCommission,
      dienst: this.validateDienst,
      rubricering: this.validateRubricering,
    }

    return validators[type] || null
  }
}
