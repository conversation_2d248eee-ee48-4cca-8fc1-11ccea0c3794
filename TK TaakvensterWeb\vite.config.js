import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'

// https://vite.dev/config/
export default defineConfig(({ mode }) => ({
  base: './',  // Use relative paths for assets
  plugins: [
    vue(),  // No custom element configuration
    // Only include devtools in development
    ...(mode === 'development' ? [vueDevTools()] : []),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },
  },
  build: {
    // Enable source maps in development for debugging
    sourcemap: mode === 'development',
    // Minify in production only
    minify: mode === 'production',
    // Output directory
    outDir: 'wwwroot',
    // Clean output directory before build
    emptyOutDir: true,
    // Generate manifest for asset injection
    manifest: true,
    // Generate single bundle for Office Add-ins (simpler loading)
    rollupOptions: {
      output: {
        // Don't split chunks for Office Add-ins
        manualChunks: undefined,
        // Consistent file naming
        entryFileNames: 'assets/[name].[hash].js',
        chunkFileNames: 'assets/[name].[hash].js',
        assetFileNames: 'assets/[name].[hash].[ext]'
      }
    },
    // Target modern browsers (Office supports ES2020+)
    target: 'es2020'
  },
  define: {
    // Define development flag for conditional code
    __DEV__: JSON.stringify(mode === 'development')
  }
}))
