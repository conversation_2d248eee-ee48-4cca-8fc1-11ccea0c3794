import { defineStore } from 'pinia'
import { commissionData, getValidCommissions, getCommissionById, formatCommissionAddress, getCommissionDisplayName } from '../../../data/commissionData.js'
import { useLogger } from '../../shared'

/**
 * Pinia store for managing commission data in ProfielAfzender step
 * 
 * This store handles commission data loading, validation, and lookup.
 * It's designed to be easily switched from static data to API calls
 * in the future.
 */
export const useCommissionStore = defineStore('commission', {
  state: () => ({
    /**
     * Raw commission data (including potentially invalid commissions)
     */
    rawCommissions: [],
    
    /**
     * Validated and filtered commissions ready for use
     */
    validCommissions: [],
    
    /**
     * Loading state for future API integration
     */
    isLoading: false,
    
    /**
     * Error state for data loading
     */
    error: null,
    
    /**
     * Validation warnings collected during data processing
     */
    validationWarnings: []
  }),

  getters: {
    /**
     * Get commission by ID
     * @returns {Function} Function that takes commissionId and returns commission
     */
    getCommissionById: (state) => (commissionId) => {
      if (!commissionId) return null
      return state.validCommissions.find(commission => commission.id === commissionId) || null
    },

    /**
     * Get all commission IDs
     * @returns {Array} Array of commission IDs
     */
    commissionIds: (state) => state.validCommissions.map(commission => commission.id),

    /**
     * Get commissions formatted for display
     * @returns {Array} Array of commission objects with display properties
     */
    commissionsForDisplay: (state) => {
      return state.validCommissions.map(commission => ({
        ...commission,
        displayName: getCommissionDisplayName(commission),
        formattedAddress: formatCommissionAddress(commission)
      }))
    },

    /**
     * Get number of valid commissions
     * @returns {number} Count of valid commissions
     */
    commissionCount: (state) => state.validCommissions.length,

    /**
     * Get number of invalid commissions that were filtered out
     * @returns {number} Count of invalid commissions
     */
    invalidCommissionCount: (state) => state.rawCommissions.length - state.validCommissions.length,

    /**
     * Check if any commissions are available
     * @returns {boolean} True if commissions are available
     */
    hasCommissions: (state) => state.validCommissions.length > 0
  },

  actions: {
    /**
     * Load commission data (currently from static data, future: API)
     * @param {boolean} forceReload - Force reload even if data exists
     */
    async loadCommissions(forceReload = false) {
      const { warn, error } = useLogger()
      
      // Skip loading if data already exists and not forcing reload
      if (this.validCommissions.length > 0 && !forceReload) {
        return
      }

      try {
        this.isLoading = true
        this.error = null
        this.validationWarnings = []

        // TODO: Replace with API call in the future
        // const response = await api.getCommissions()
        // this.rawCommissions = response.data
        
        // For now, use static data
        this.rawCommissions = [...commissionData]

        // Validate and filter commissions
        this.validCommissions = this._validateAndFilterCommissions(this.rawCommissions)

        // Log validation results
        if (this.validationWarnings.length > 0) {
          warn(`Commission validation warnings: ${this.validationWarnings.length} commissions had issues`)
          this.validationWarnings.forEach(warning => warn(warning))
        }

      } catch (err) {
        this.error = `Failed to load commissions: ${err.message}`
        error(this.error)
        throw err
      } finally {
        this.isLoading = false
      }
    },

    /**
     * Get commission by ID with error handling
     * @param {string} commissionId - Commission ID to find
     * @returns {Object|null} Commission object or null if not found
     */
    findCommissionById(commissionId) {
      if (!commissionId) {
        return null
      }

      const commission = this.getCommissionById(commissionId)
      if (!commission) {
        const { warn } = useLogger()
        warn(`Commission not found for ID: ${commissionId}`)
      }

      return commission
    },

    /**
     * Format commission data for content control population
     * @param {string} commissionId - Commission ID
     * @returns {Object} Formatted commission data for content controls
     */
    getFormattedCommissionData(commissionId) {
      const commission = this.findCommissionById(commissionId)
      
      if (!commission) {
        return {
          commissionName: '',
          formattedAddress: '',
          hasCommission: false
        }
      }

      return {
        commissionName: commission.CommissionName,
        formattedAddress: formatCommissionAddress(commission),
        hasCommission: true,
        commission: commission
      }
    },

    /**
     * Search commissions by name
     * @param {string} searchTerm - Search term to filter by
     * @returns {Array} Filtered commissions
     */
    searchCommissions(searchTerm) {
      if (!searchTerm || searchTerm.trim() === '') {
        return this.validCommissions
      }

      const term = searchTerm.toLowerCase().trim()
      return this.validCommissions.filter(commission => {
        return commission.CommissionName.toLowerCase().includes(term)
      })
    },

    /**
     * Validate and filter commissions, collecting warnings for invalid ones
     * @private
     * @param {Array} commissions - Raw commission data
     * @returns {Array} Valid commissions
     */
    _validateAndFilterCommissions(commissions) {
      const validCommissions = []
      
      commissions.forEach((commission, index) => {
        // Check required fields
        if (!commission.CommissionName || commission.CommissionName.trim() === '') {
          this.validationWarnings.push(`Commission at index ${index}: Missing or empty CommissionName`)
          return
        }

        // Commission is valid, add to valid list
        validCommissions.push({
          ...commission,
          // Ensure optional fields are properly handled
          POBoxNumber: commission.POBoxNumber || '',
          ZipCode: commission.ZipCode || '',
          City: commission.City || '',
          StreetName: commission.StreetName || '',
          HouseNumber: commission.HouseNumber || ''
        })
      })

      return validCommissions
    },

    /**
     * Reset store to initial state
     */
    reset() {
      this.rawCommissions = []
      this.validCommissions = []
      this.isLoading = false
      this.error = null
      this.validationWarnings = []
    }
  }
})
