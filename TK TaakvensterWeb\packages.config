﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Azure.Core" version="1.28.0" targetFramework="net472" />
  <package id="jQuery" version="3.6.0" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.Client" version="5.2.9" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.Core" version="5.2.9" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.Owin" version="5.2.9" targetFramework="net472" />
  <package id="Microsoft.AspNet.WebApi.WebHost" version="5.2.9" targetFramework="net472" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="9.0.7" targetFramework="net472" />
  <package id="Microsoft.CodeDom.Providers.DotNetCompilerPlatform" version="2.0.1" targetFramework="net472" />
  <package id="Microsoft.Graph" version="4.54.0" targetFramework="net472" />
  <package id="Microsoft.Graph.Core" version="2.0.15" targetFramework="net472" />
  <package id="Microsoft.Identity.Client" version="4.73.1" targetFramework="net472" />
  <package id="Microsoft.IdentityModel.Abstractions" version="6.35.0" targetFramework="net472" />
  <package id="Microsoft.IdentityModel.JsonWebTokens" version="6.30.0" targetFramework="net472" />
  <package id="Microsoft.IdentityModel.Logging" version="6.30.0" targetFramework="net472" />
  <package id="Microsoft.IdentityModel.Tokens" version="6.30.0" targetFramework="net472" />
  <package id="Microsoft.Office.js" version="********" targetFramework="net472" />
  <package id="Microsoft.Owin" version="4.2.2" targetFramework="net472" />
  <package id="Microsoft.Owin.Host.SystemWeb" version="4.2.2" targetFramework="net472" />
  <package id="Newtonsoft.Json" version="13.0.1" targetFramework="net472" />
  <package id="OfficeUIFabricCore" version="11.1.0" targetFramework="net472" />
  <package id="Owin" version="1.0" targetFramework="net472" />
  <package id="System.Buffers" version="4.5.1" targetFramework="net472" />
  <package id="System.Diagnostics.DiagnosticSource" version="6.0.1" targetFramework="net472" />
  <package id="System.IdentityModel.Tokens.Jwt" version="6.30.0" targetFramework="net472" />
  <package id="System.IO.Pipelines" version="9.0.7" targetFramework="net472" />
  <package id="System.Memory" version="4.5.5" targetFramework="net472" />
  <package id="System.Net.Http.WinHttpHandler" version="7.0.0" targetFramework="net472" />
  <package id="System.Numerics.Vectors" version="4.5.0" targetFramework="net472" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.0.0" targetFramework="net472" />
  <package id="System.Text.Encodings.Web" version="9.0.7" targetFramework="net472" />
  <package id="System.Text.Json" version="9.0.7" targetFramework="net472" />
  <package id="System.Threading.Tasks.Extensions" version="4.5.4" targetFramework="net472" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net472" />
</packages>