/**
 * Office.js integration for the TK Word Add-in
 * Provides functionality for interacting with Word documents
 */

export function useOffice() {
  /**
   * Insert text into a content control with the specified tag
   * @param {string} controlTag - The tag of the content control
   * @param {string} value - The text to insert
   * @returns {Promise<boolean>} - True if successful, false if no content controls found
   */
  const insertTextInContentControl = async (controlTag, value) => {
    try {
      // Log for debugging purposes
      console.log(`Inserting "${value}" into content control "${controlTag}"`)

      // Check if Office is available
      if (!window.Word) {
        console.log('Word object not available - running in development mode')
        return Promise.resolve(true)
      }

      return Word.run(async (context) => {
        // Get all content controls with the specified tag
        const contentControls = context.document.contentControls.getByTag(controlTag)
        context.load(contentControls, 'items')

        // Sync to get the content controls
        await context.sync()

        // Check if any content controls with the tag exist
        if (contentControls.items.length === 0) {
          console.log(`No content controls found with tag: ${controlTag}`)
          return false
        }

        // Insert text in each content control
        // (Note: multiple controls with the same tag can exist, that's why we iterate)
        for (let i = 0; i < contentControls.items.length; i++) {
          contentControls.items[i].insertText(value, 'Replace')
        }

        // Sync the document
        await context.sync()

        // Log success
        console.log(
          `Successfully inserted text into ${contentControls.items.length} content control(s) with tag: ${controlTag}`,
        )
        return true
      })
    } catch (error) {
      console.error(`Error inserting text: ${error.message}`)

      // Enhance error with Office context for better error detection
      const enhancedError = new Error(`Office API Error: ${error.message}`)
      enhancedError.isOfficeError = true
      enhancedError.originalError = error
      enhancedError.context = 'insertTextInContentControl'
      enhancedError.controlTag = controlTag

      return Promise.reject(enhancedError)
    }
  }

  /**
   * Check if a content control with the specified tag exists in the document
   * @param {string} controlTag - The tag of the content control to check for
   * @returns {Promise<boolean>} - True if the content control exists, false otherwise
   */
  const hasContentControl = (controlTag) => {
    try {
      // Check if Office is available
      if (!window.Word) {
        console.log('Word object not available - running in development mode')
        return Promise.resolve(true)
      }

      return Word.run(async (context) => {
        // Get all content controls
        const contentControls = context.document.contentControls
        contentControls.load('items', 'tag')

        // Sync to get the content controls
        await context.sync()

        // Check if any content control has the specified tag
        const hasControl = contentControls.items.some((control) => control.tag === controlTag)
        console.log(
          `Content control with tag "${controlTag}" ${hasControl ? 'found' : 'not found'}`,
        )

        return hasControl
      })
    } catch (error) {
      console.error(`Error checking for content control: ${error.message}`)

      // Enhance error with Office context for better error detection
      const enhancedError = new Error(`Office API Error: ${error.message}`)
      enhancedError.isOfficeError = true
      enhancedError.originalError = error
      enhancedError.context = 'hasContentControl'
      enhancedError.controlTag = controlTag

      return Promise.reject(enhancedError)
    }
  }

  return {
    insertTextInContentControl,
    hasContentControl,
  }
}
