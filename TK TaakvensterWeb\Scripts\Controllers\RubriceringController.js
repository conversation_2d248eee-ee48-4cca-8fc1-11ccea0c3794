/**
 * RubriceringController.js
 * A controller module that handles document classification operations
 * Uses IIFE module pattern for consistency
 */

const RubriceringController = (function() {
    function _initialize() {
        console.log('Initializing rubricering module...');
        console.log('Rubricering module initialized');
    }
    
    async function _handleClassificationButtonClicked(event) {
        // Prevent default button action
        event.preventDefault();
        
        // Get the button that was clicked
        var button = $(this);
        
        // Get the value and control tag from the button's data attributes
        var value = button.data('value');
        var controlTag = button.data('control-tag');
        
        // Log the action to the debug box
        console.log(`Classification button clicked: ${value} for control ${controlTag}`);
        
        // Check if we should insert into content controls
        var insertIntoContentControls = $('#debugModeToggle').is(':checked');
        
        // Disable the button to prevent multiple clicks
        button.prop('disabled', true);
        
        // Use a try-catch block to handle any errors
        try {
            // Run the async function to insert the value
            Word.run(async (context) => {
                try {
                    if (insertIntoContentControls) {
                        try {
                            const success = await OfficeUtils.Word.insertTextInContentControl(controlTag, value);
                            
                            if (!success) {
                                OfficeUtils.UI.showNotification('Niet gevonden', `Geen content control met tag "${controlTag}" gevonden.`);
                            }
                        } catch (error) {
                            throw error;
                        }
                    } else {
                        // Insert the value at the current selection
                        context.document.getSelection().insertText(value, Word.InsertLocation.replace);
                        
                        // Execute the batch operation to insert the value
                        await context.sync();

                        // Log success to the debug box
                        console.log(`Value "${value}" inserted at current selection`);
                    }
                } catch (error) {
                    // Handle the error through the error handler
                    OfficeUtils.Errors.handleError(error, 'inserting classification');
                } finally {
                    // Re-enable the button
                    button.prop('disabled', false);
                }
            });
        } catch (error) {
            // Handle any errors that occur outside of the Word.run
            OfficeUtils.Errors.handleError(error, 'starting Word.run for classification');
            
            // Re-enable the button
            button.prop('disabled', false);
        }
    }
    
    // Public API
    return {
        initialize: _initialize,
        handleClassificationButtonClicked: _handleClassificationButtonClicked
    };
})();
