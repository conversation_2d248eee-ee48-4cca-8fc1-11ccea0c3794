import { describe, it, expect, beforeEach, vi } from 'vitest'
import { createPinia, setActivePinia } from 'pinia'
import { useProfileStore } from '../../../src/features/steps/stores/profileStore'

// Mock the logger
vi.mock('../../../src/features/shared', () => ({
  useLogger: () => ({
    warn: vi.fn(),
    error: vi.fn(),
  }),
}))

// Mock profile data
vi.mock('../../../src/data/profileData.js', () => ({
  profileData: [
    {
      FirstName: 'Jan',
      LastName: 'de Bont',
      Role: 'Developer',
      EmailAddress: '<EMAIL>',
      CommissionId: 'test-commission'
    },
    {
      FirstName: 'Maria',
      LastName: 'van der <PERSON>',
      Role: 'Manager',
      EmailAddress: 'm.vanden<PERSON>@test.nl',
      CommissionId: 'test-commission-2'
    },
    {
      // Invalid profile - missing FirstName
      FirstName: '',
      LastName: 'Invalid',
      Role: 'Test',
      EmailAddress: '<EMAIL>',
      CommissionId: 'test'
    }
  ],
  getValidProfiles: vi.fn(),
  getProfileByIndex: vi.fn(),
  getProfilesByCommission: vi.fn(),
  formatProfileDisplayName: vi.fn()
}))

describe('ProfileStore', () => {
  let profileStore

  beforeEach(() => {
    // Create a fresh pinia instance for each test
    setActivePinia(createPinia())
    profileStore = useProfileStore()
  })

  describe('Initial State', () => {
    it('should have correct initial state', () => {
      expect(profileStore.rawProfiles).toEqual([])
      expect(profileStore.validProfiles).toEqual([])
      expect(profileStore.selectedProfileIndex).toBeNull()
      expect(profileStore.isLoading).toBe(false)
      expect(profileStore.error).toBeNull()
      expect(profileStore.validationWarnings).toEqual([])
    })
  })

  describe('Getters', () => {
    beforeEach(async () => {
      // Load test data
      await profileStore.loadProfiles()
    })

    it('should return null for selectedProfile when no selection', () => {
      expect(profileStore.selectedProfile).toBeNull()
    })

    it('should return selected profile when valid index is set', () => {
      profileStore.selectedProfileIndex = 0
      expect(profileStore.selectedProfile).toBeTruthy()
      expect(profileStore.selectedProfile.FirstName).toBe('Jan')
    })

    it('should return correct profile count', () => {
      expect(profileStore.profileCount).toBe(2) // Only valid profiles
    })

    it('should return correct invalid profile count', () => {
      expect(profileStore.invalidProfileCount).toBe(1) // One invalid profile
    })

    it('should indicate profiles are available', () => {
      expect(profileStore.hasProfiles).toBe(true)
    })

    it('should format profiles for display', () => {
      const displayProfiles = profileStore.profilesForDisplay
      expect(displayProfiles).toHaveLength(2)
      expect(displayProfiles[0]).toHaveProperty('label')
      expect(displayProfiles[0]).toHaveProperty('index')
    })
  })

  describe('Actions', () => {
    describe('loadProfiles', () => {
      it('should load and validate profiles', async () => {
        await profileStore.loadProfiles()

        expect(profileStore.validProfiles).toHaveLength(2)
        expect(profileStore.rawProfiles).toHaveLength(3)
        expect(profileStore.validationWarnings).toHaveLength(1) // One invalid profile
      })

      it('should not reload if data exists and not forced', async () => {
        await profileStore.loadProfiles()
        const firstLoadCount = profileStore.validProfiles.length

        await profileStore.loadProfiles() // Second call without force
        expect(profileStore.validProfiles).toHaveLength(firstLoadCount)
      })

      it('should reload when forced', async () => {
        await profileStore.loadProfiles()
        await profileStore.loadProfiles(true) // Force reload

        expect(profileStore.validProfiles).toHaveLength(2)
      })
    })

    describe('selectProfile', () => {
      beforeEach(async () => {
        await profileStore.loadProfiles()
      })

      it('should select valid profile by index', () => {
        const profile = profileStore.selectProfile(0)
        
        expect(profileStore.selectedProfileIndex).toBe(0)
        expect(profile).toBeTruthy()
        expect(profile.FirstName).toBe('Jan')
      })

      it('should return null for invalid index', () => {
        const profile = profileStore.selectProfile(-1)
        
        expect(profileStore.selectedProfileIndex).toBeNull()
        expect(profile).toBeNull()
      })

      it('should return null for out of bounds index', () => {
        const profile = profileStore.selectProfile(999)
        
        expect(profileStore.selectedProfileIndex).toBeNull()
        expect(profile).toBeNull()
      })

      it('should return null for null index', () => {
        const profile = profileStore.selectProfile(null)
        
        expect(profileStore.selectedProfileIndex).toBeNull()
        expect(profile).toBeNull()
      })
    })

    describe('clearSelection', () => {
      it('should clear the current selection', async () => {
        await profileStore.loadProfiles()
        profileStore.selectProfile(0)
        
        expect(profileStore.selectedProfileIndex).toBe(0)
        
        profileStore.clearSelection()
        
        expect(profileStore.selectedProfileIndex).toBeNull()
      })
    })

    describe('searchProfiles', () => {
      beforeEach(async () => {
        await profileStore.loadProfiles()
      })

      it('should return all profiles for empty search', () => {
        const results = profileStore.searchProfiles('')
        expect(results).toHaveLength(2)
      })

      it('should filter profiles by name', () => {
        const results = profileStore.searchProfiles('Jan')
        expect(results).toHaveLength(1)
        expect(results[0].FirstName).toBe('Jan')
      })

      it('should filter profiles by role', () => {
        const results = profileStore.searchProfiles('Developer')
        expect(results).toHaveLength(1)
        expect(results[0].Role).toBe('Developer')
      })

      it('should be case insensitive', () => {
        const results = profileStore.searchProfiles('jan')
        expect(results).toHaveLength(1)
        expect(results[0].FirstName).toBe('Jan')
      })
    })

    describe('reset', () => {
      it('should reset store to initial state', async () => {
        await profileStore.loadProfiles()
        profileStore.selectProfile(0)
        
        profileStore.reset()
        
        expect(profileStore.rawProfiles).toEqual([])
        expect(profileStore.validProfiles).toEqual([])
        expect(profileStore.selectedProfileIndex).toBeNull()
        expect(profileStore.isLoading).toBe(false)
        expect(profileStore.error).toBeNull()
        expect(profileStore.validationWarnings).toEqual([])
      })
    })
  })

  describe('Validation', () => {
    it('should filter out profiles with missing FirstName', async () => {
      await profileStore.loadProfiles()
      
      // Should have filtered out the profile with empty FirstName
      const invalidProfile = profileStore.validProfiles.find(p => p.FirstName === '')
      expect(invalidProfile).toBeUndefined()
    })

    it('should collect validation warnings', async () => {
      await profileStore.loadProfiles()
      
      expect(profileStore.validationWarnings).toHaveLength(1)
      expect(profileStore.validationWarnings[0]).toContain('Missing or empty FirstName')
    })

    it('should handle optional fields properly', async () => {
      await profileStore.loadProfiles()
      
      const profile = profileStore.validProfiles[0]
      expect(profile).toHaveProperty('Role')
      expect(profile).toHaveProperty('EmailAddress')
      expect(profile).toHaveProperty('CommissionId')
    })
  })
})
