<template>
  <Transition name="message-bar">
    <FluentMessageBarWrapper
      v-if="visible"
      :id="id"
      :intent="intent"
      :role="role"
      :aria-live="intent === 'error' ? 'assertive' : 'polite'"
      @dismiss="handleDismiss"
      ref="messageBarRef"
    >
      {{ message }}

      <!-- Icon slot - Tabler Icons -->
      <IconInfoCircle v-if="intent === 'info'" slot="icon" :size="20" stroke="currentColor" />
      <IconAlertTriangle
        v-else-if="intent === 'warning'"
        slot="icon"
        :size="20"
        stroke="currentColor"
      />
      <IconCircleCheck
        v-else-if="intent === 'success'"
        slot="icon"
        :size="20"
        stroke="currentColor"
      />
      <IconCircleX v-else-if="intent === 'error'" slot="icon" :size="20" stroke="currentColor" />

      <!-- Actions - Direct buttons without wrappers -->
      <FluentButtonWrapper
        v-for="action in actions"
        :key="action.id"
        slot="actions"
        appearance="outline"
        @click="handleAction(action)"
      >
        {{ action.label }}
      </FluentButtonWrapper>

      <!-- Dismiss button - Direct button without wrapper -->
      <button
        slot="dismiss"
        class="message-bar-dismiss"
        @click="handleDismiss"
        aria-label="Dismiss"
      >
        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 16 16">
          <path
            fill="currentColor"
            d="M2.146 2.854a.5.5 0 1 1 .708-.708L8 7.293l5.146-5.147a.5.5 0 0 1 .708.708L8.707 8l5.147 5.146a.5.5 0 0 1-.708.708L8 8.707l-5.146 5.147a.5.5 0 0 1-.708-.708L7.293 8z"
          />
        </svg>
      </button>
    </FluentMessageBarWrapper>
  </Transition>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { MESSAGE_BAR_DEFAULTS } from '../../composables/useMessageBar/constants'
import FluentMessageBarWrapper from '../../../office/wrappers/fluent-ui/FluentMessageBarWrapper.vue'
import FluentButtonWrapper from '../../../office/wrappers/fluent-ui/FluentButtonWrapper.vue'
import { IconInfoCircle, IconAlertTriangle, IconCircleCheck, IconCircleX } from '@tabler/icons-vue'

export default {
  name: 'MessageBar',
  components: {
    FluentMessageBarWrapper,
    FluentButtonWrapper,
    IconInfoCircle,
    IconAlertTriangle,
    IconCircleCheck,
    IconCircleX,
  },
  props: {
    id: {
      type: String,
      required: true,
    },
    message: {
      type: String,
      required: true,
    },
    intent: {
      type: String,
      default: 'info',
      validator(value) {
        return ['info', 'success', 'warning', 'error'].includes(value)
      },
    },
    actions: {
      type: Array,
      default() {
        return []
      },
    },
    duration: {
      type: Number,
      default: MESSAGE_BAR_DEFAULTS.duration,
    },
    persistent: {
      type: Boolean,
      default: false,
    },
    role: {
      type: String,
      default: 'alert',
    },
  },

  emits: ['dismiss', 'action'],

  setup(props, { emit }) {
    // State
    const visible = ref(true)
    const messageBarRef = ref(null)

    // Computed
    const hasActions = computed(() => props.actions && props.actions.length > 0)

    /**
     * Handle dismiss action
     * - Hides the message bar with animation
     * - Emits dismiss event after transition
     */
    const handleDismiss = () => {
      visible.value = false
      // Emit after transition completes
      setTimeout(() => {
        emit('dismiss', props.id)
      }, 300)
    }

    /**
     * Handle action button click
     * @param {Object} action - The action object that was clicked
     */
    const handleAction = (action) => {
      emit('action', { actionId: action.id, messageId: props.id })

      // Auto-dismiss after action unless persistent
      if (!props.persistent) {
        handleDismiss()
      }
    }

    /**
     * Programmatically dismiss the message bar
     */
    const dismiss = () => {
      handleDismiss()
    }

    // Lifecycle hooks - No timer logic needed here as store handles auto-dismiss
    // Component focuses purely on presentation and user interactions

    // Expose methods for parent components
    return {
      visible,
      messageBarRef,
      hasActions,
      handleDismiss,
      handleAction,
      dismiss,
    }
  },
}
</script>

<style scoped>
fluent-message-bar {
  border-width: 1px; /* reset tailwind's reset styles (::before, ::after from preflight) */
  width: auto; /* fluent-ui stupidly sets this to 100% so we cannot apply margin without pushing the component outside of the window */
  margin-left: 8px; /* apply the same margin as fluent-ui */
  margin-right: 8px;
}

.message-bar-enter-active,
.message-bar-leave-active {
  transition: all 0.3s ease;
}

.message-bar-enter-from {
  opacity: 0;
  transform: translateY(-100%);
}

.message-bar-leave-to {
  opacity: 0;
  transform: translateY(-100%);
}
</style>
