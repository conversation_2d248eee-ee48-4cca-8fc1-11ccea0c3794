/**
 * SharePoint data provider
 * Handles fetching data from SharePoint lists
 */
const SharePointDataProvider = (function() {
    // Private variables
    const _cache = new Map();
    const _cacheExpiry = new Map();
    const _cacheDuration = AppConfig.cache.defaultDuration;
    
    // SharePoint site URL from configuration
    const _siteUrl = AppConfig.sharePoint.siteUrl;
    
    // Generate a cache key based on list name and options
    function getCacheKey(listName, options) {
        return `${listName}-${JSON.stringify(options)}`;
    }
    
    // Mock data for testing purposes
    function getMockListItems(listName, options = {}) {
        // Mock data for Services list
        if (listName === AppConfig.sharePoint.lists.services) {
            const mockServices = [
                { Id: 1, Title: "Juridisch advies", Description: "Juridisch advies voor parlementaire documenten", Category: "Juridisch", ContactPerson: "<PERSON>" },
                { Id: 2, Title: "Taalkundige ondersteuning", Description: "Hulp bij het opste<PERSON> van teks<PERSON>", Category: "Taal", ContactPerson: "<PERSON>" },
                { Id: 3, Title: "Onderzoek", Description: "Onderzoek naar beleidsterreinen", Category: "Onderzoek", ContactPerson: "Mohammed El Amrani" },
                { Id: 4, Title: "Informatievoorziening", Description: "Toegang tot informatiebronnen", Category: "Informatie", ContactPerson: "Sophie Bakker" },
                { Id: 5, Title: "Procedureel advies", Description: "Advies over parlementaire procedures", Category: "Juridisch", ContactPerson: "Willem de Groot" }
            ];
            
            // Store in cache
            _cache.set(getCacheKey(listName, options), mockServices);
            _cacheExpiry.set(getCacheKey(listName, options), Date.now() + _cacheDuration);
            
            return mockServices;
        }
        
        // Default empty response
        return [];
    }
    
    // Get data from SharePoint list using Microsoft Graph API
    async function getListItems(listName, options = {}) {
        try {
            // Check cache first
            const cacheKey = getCacheKey(listName, options);
            if (!options.forceRefresh && _cache.has(cacheKey)) {
                const cacheExpiry = _cacheExpiry.get(cacheKey);
                if (cacheExpiry > Date.now()) {
                    return _cache.get(cacheKey);
                }
            }
            
            // If useStaticData flag is set, return mock data
            if (options.useStaticData) {
                return getMockListItems(listName, options);
            }
            
            // Get authentication token
            const token = await AuthService.getToken();
            if (!token) {
                // If authentication failed, fall back to mock data
                console.log('Authenticatie mislukt, terugvallen op statische gegevens');
                return getMockListItems(listName, options);
            }
            
            console.log('[sharepoint-debug] Fetching data using Microsoft Graph API');
            
            // Extract site and domain from the SharePoint URL
            // Format: https://domain.sharepoint.com/sites/sitename
            const urlParts = _siteUrl.match(/https:\/\/([^/]+)\/sites\/([^/]+)/);
            if (!urlParts || urlParts.length < 3) {
                throw new Error(`Invalid SharePoint site URL format: ${_siteUrl}`);
            }
            
            const domain = urlParts[1]; // e.g., denobelsoftware.sharepoint.com
            const siteName = urlParts[2]; // e.g., DNSTest
            
            console.log(`[sharepoint-debug] Domain: ${domain}, Site: ${siteName}`);
            
            // Use our server-side API that implements the On-Behalf-Of flow
            // instead of calling Microsoft Graph directly
            const apiUrl = '/api/graph/getlist'; // Use relative path like in test-api.html
            
            // Prepare the request payload
            const requestPayload = {
                token: token,
                siteUrl: `/sites/${siteName}`,
                hostName: domain,
                listName: listName
            };
            
            // Add filter if specified
            if (options.filter) {
                requestPayload.filter = options.filter;
            }
            
            // Add order by if specified
            if (options.orderBy) {
                requestPayload.orderBy = options.orderBy;
            }
            
            // Add top if specified
            if (options.top) {
                requestPayload.top = options.top;
            }
            
            console.log(`[sharepoint-debug] Calling server API: ${apiUrl} with payload: ${JSON.stringify(requestPayload)}`);
            
            // Make the request to our server API
            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify(requestPayload)
            });
            
            // Check for errors
            if (!response.ok) {
                const error = await response.json();
                const errorMessage = error.error ? error.error.message : 'Unknown error';
                throw new Error(`Microsoft Graph API error: ${errorMessage}`);
            }
            
            // Parse the response
            const data = await response.json();
            
            console.log(`[sharepoint-debug] Received ${data.value ? data.value.length : 0} items from Graph API`);
            
            // The server now returns pre-processed items, so we can use them directly
            const items = data.value || [];
            
            console.log(`[sharepoint-debug] Using ${items.length} pre-processed items from server`);
            
            // Cache the items
            _cacheData(cacheKey, items);
            
            async function getGraphAccessToken() {
                try {
                    const token = await OfficeRuntime.auth.getAccessToken({ allowSignInPrompt: true });
                    return token;
                } catch (error) {
                    console.error("Error getting token", error);
                    throw error;
                }
            }
            
            return data.value;
        } catch (error) {
            OfficeUtils.Errors.handleError(error, "fetching SharePoint list items", {
                listName: listName,
                options: options
            });
            throw error;
        }
    }
    
    // Cache data with expiration
    function _cacheData(key, data) {
        _cache.set(key, data);
        _cacheExpiry.set(key, Date.now() + _cacheDuration);
    }
    
    // Check if we should use cached data
    function _shouldUseCache(key) {
        if (!_cache.has(key) || !_cacheExpiry.has(key)) {
            return false;
        }
        
        return Date.now() < _cacheExpiry.get(key);
    }
    
    // Clear all cached data
    function clearCache() {
        _cache.clear();
        _cacheExpiry.clear();
    }
    
    // Public API
    return {
        getListItems: getListItems,
        clearCache: clearCache
    };
})();
