import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { createPinia, setActivePinia } from 'pinia'
import RubriceringStep from '../../../src/features/steps/components/steps/RubriceringStep.vue'

// Create mock functions that we'll use in the tests
const insertTextInContentControlMock = vi.fn().mockResolvedValue(true)
const withErrorHandlingMock = vi.fn(async (callback, context, options) => {
  try {
    return await callback()
  } catch (error) {
    throw error
  }
})

// Mock the useOffice composable
vi.mock('../../../src/features/office', () => ({
  useOffice: () => ({
    insertTextInContentControl: insertTextInContentControlMock,
  }),
}))

// Mock the useErrorHandler composable
vi.mock('../../../src/features/shared', () => ({
  useErrorHandler: () => ({
    withErrorHandling: withErrorHandlingMock,
  }),
}))

// Import the mocked modules to verify calls
import { useOffice } from '../../../src/features/office'
import { useErrorHandler } from '../../../src/features/shared'

describe('RubriceringStep', () => {
  beforeEach(() => {
    // Create a fresh pinia instance for each test
    setActivePinia(createPinia())

    // Reset all mocks before each test
    vi.clearAllMocks()
  })

  it('should render without errors', () => {
    const wrapper = mount(RubriceringStep)
    expect(wrapper.exists()).toBe(true)
  })

  it('should emit step-ready event on mount', () => {
    const wrapper = mount(RubriceringStep)
    expect(wrapper.emitted('step-ready')).toBeTruthy()
    expect(wrapper.emitted('step-ready').length).toBe(1)
  })

  it('should validate selection before calling Office API', async () => {
    const wrapper = mount(RubriceringStep)

    // Reset mocks before test
    withErrorHandlingMock.mockClear()
    insertTextInContentControlMock.mockClear()

    // Try to apply rubricering without selecting an item
    await wrapper.vm.applyRubricering(null)

    // Verify that withErrorHandling was called with an error
    expect(withErrorHandlingMock).toHaveBeenCalled()

    // The first argument to withErrorHandling should be a function that rejects
    const callback = withErrorHandlingMock.mock.calls[0][0]
    await expect(callback()).rejects.toThrow('Invalid selection')

    // Verify that insertTextInContentControl was not called
    expect(insertTextInContentControlMock).not.toHaveBeenCalled()
  })

  it('should call Office API when a valid selection is made', async () => {
    const wrapper = mount(RubriceringStep)

    // Reset and configure mocks
    insertTextInContentControlMock.mockClear()
    insertTextInContentControlMock.mockResolvedValue(true)

    withErrorHandlingMock.mockClear()
    withErrorHandlingMock.mockImplementation(async (callback) => {
      return await callback()
    })

    // Apply rubricering with a valid index
    await wrapper.vm.applyRubricering(0)

    // Verify that insertTextInContentControl was called with the right arguments
    expect(insertTextInContentControlMock).toHaveBeenCalled()

    // Verify that step-complete is emitted after a delay
    await new Promise((resolve) => setTimeout(resolve, 600))
    expect(wrapper.emitted('step-complete')).toBeTruthy()
  })

  it('should handle Office API errors correctly', async () => {
    const wrapper = mount(RubriceringStep)

    // Reset and configure mocks
    const error = new Error('Office API error')
    insertTextInContentControlMock.mockClear()
    insertTextInContentControlMock.mockRejectedValue(error)

    withErrorHandlingMock.mockClear()
    withErrorHandlingMock.mockImplementation(async (callback) => {
      try {
        return await callback()
      } catch (e) {
        throw e
      }
    })

    // Apply rubricering with a valid index
    try {
      await wrapper.vm.applyRubricering(0)
    } catch (e) {
      // Error is expected
    }

    // Verify that insertTextInContentControl was called
    expect(insertTextInContentControlMock).toHaveBeenCalled()

    // Verify that step-complete is not emitted
    expect(wrapper.emitted('step-complete')).toBeFalsy()

    // Verify that selectedRubricering is reset to null
    expect(wrapper.vm.selectedRubricering).toBe(null)
  })
})
