/**
 * ServiceRepository.js
 * A simple module that handles services data operations
 * Uses IIFE module pattern for consistency
 */

const ServiceRepository = (function() {
    // Private methods
    function _getServices() {
        console.log('Warning: getServices() is deprecated - services should be accessed from load method returns');
        return [];
    }
    
    function _setServices(services) {
        console.log('Warning: setServices() is deprecated - services should be managed through load methods');
    }
    
    async function _loadFromSharePoint(forceRefresh = true) {
        console.log('Loading services from SharePoint...');
        
        // Get services from SharePoint using the repository
        const services = await ServicesRepository.getAllServices(forceRefresh, false);
        
        console.log('SharePoint data loaded successfully');
        return services;
    }
    
    async function _loadFromStaticData() {
        console.log('Loading static services data...');
        
        // Get services from static data using the repository
        const services = await ServicesRepository.getAllServices(false, true);
        
        console.log('Static data loaded successfully');
        return services;
    }
    
    async function _loadWithFallback() {
        console.log('Loading services data with fallback...');
        
        try {
            // Try to load from SharePoint first
            const services = await ServicesRepository.getAllServices(true, false);
            
            console.log('SharePoint data loaded successfully');
            return { services, usedFallback: false };
        } catch (error) {
            // Handle the error through the error handler
            OfficeUtils.Errors.handleError(error, 'loading SharePoint data');
            
            // Try to load from static data as fallback
            try {
                console.log('Falling back to static data...');
                
                const services = await ServicesRepository.getAllServices(false, true);
                
                console.log('Fallback to static data successful');
                return { services, usedFallback: true };
            } catch (fallbackError) {
                // Handle the fallback error through the error handler
                TKOfficeUtils.Errors.handleError(fallbackError, 'loading fallback data');
                throw fallbackError;
            }
        }
    }
    
    // Public API
    return {
        getServices: _getServices,
        setServices: _setServices,
        loadFromSharePoint: _loadFromSharePoint,
        loadFromStaticData: _loadFromStaticData,
        loadWithFallback: _loadWithFallback
    };
})();
