<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>T<PERSON> Taakvenster</title>
  <link rel="icon" type="image/x-icon" href="/favicon.ico">
  
  <!-- Office.js API -->
  <script src="https://appsforoffice.microsoft.com/lib/1/hosted/office.js" type="text/javascript"></script>
  
  <!-- Fabric Core (base styles) -->
  <link rel="stylesheet" href="https://res-1.cdn.office.net/files/fabric-cdn-prod_20230815.002/office-ui-fabric-core/11.1.0/css/fabric.min.css">
  <!-- Fabric Components (UI components like buttons, lists, etc.) -->
  <link rel="stylesheet" href="https://static2.sharepointonline.com/files/fabric/office-ui-fabric-js/1.4.0/css/fabric.components.min.css">
  
  <!-- Vue.js built assets (injected during build) -->
  {{VITE_CSS_ASSETS}}
  
  <!-- Custom styles (legacy) -->
  <link href="Content/debugpanel.css" rel="stylesheet" type="text/css" />
  <link href="Content/messagebanner-custom.css" rel="stylesheet" type="text/css" />
  <link href="Content/FabricUI/MessageBanner.css" rel="stylesheet" type="text/css" />
</head>
<body class="ms-Fabric m-0 min-h-full relative flex flex-col" dir="ltr">
  <div id="app"></div>
  
  <!-- Vue.js built assets (injected during build) -->
  {{VITE_JS_ASSETS}}
</body>
</html>
