using System;
using System.Threading.Tasks;
using System.Web.Http;
using Owin;
using System.Web.Http.Owin;

namespace TK_TaakvensterWeb
{
    public class Startup
    {
        public void Configuration(IAppBuilder app)
        {
            // Configure Web API for self-host
            HttpConfiguration config = new HttpConfiguration();
            
            // Register Web API configuration
            WebApiConfig.Register(config);
            
            // Use Web API with OWIN pipeline
            app.UseWebApi(config);
        }
    }
}
