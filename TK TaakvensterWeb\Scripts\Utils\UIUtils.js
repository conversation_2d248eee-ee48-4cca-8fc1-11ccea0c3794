// UIUtils - User interface utilities for Office Add-ins
const UIUtils = (function() {
    // Private variables
    let _messageBanner;
    
    // Initialize the notification banner
    function initializeNotificationBanner() {
        let element = document.getElementById('notification-popup');
        _messageBanner = new components.MessageBanner(element);
        _messageBanner.hideBanner();
        return _messageBanner;
    }
    
    // Show a notification in the banner
    function showNotification(header, content, autoDismiss = true, timeout = 5000) {
        if (_messageBanner) {
            // Set content directly in the DOM elements
            document.getElementById('notification-header').textContent = header;
            document.getElementById('notification-body').textContent = content;
            
            // Show the banner by removing the display:none style
            const bannerElement = document.getElementById('notification-popup');
            bannerElement.style.display = 'block';
            
            // Call the showBanner method as well
            _messageBanner.showBanner();
            
            // Auto-dismiss after timeout if enabled
            if (autoDismiss) {
                // Clear any existing timers to prevent multiple dismissals
                if (window._notificationTimer) {
                    clearTimeout(window._notificationTimer);
                }
                
                // Set new timer
                window._notificationTimer = setTimeout(() => {
                    _messageBanner.hideBanner();
                    bannerElement.style.display = 'none';
                }, timeout);
            }
        }
    }
    
    // Public API
    return {
        initializeNotificationBanner,
        showNotification
    };
})();

// Make it globally accessible
window.UIUtils = UIUtils;
