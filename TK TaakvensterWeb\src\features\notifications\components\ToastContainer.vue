<template>
  <div 
    id="toast-container" 
    class="fixed top-4 right-4 z-50 space-y-2 pointer-events-none"
    role="region"
    aria-label="Notifications"
    aria-live="polite"
  >
    <!-- Toasts will be dynamically inserted here by vanilla JS -->
  </div>
  
  <!-- Include template definitions (hidden) -->
  <ToastTemplate />
</template>

<script>
import { onMounted, onUnmounted } from 'vue'
import ToastTemplate from './ToastTemplate.vue'

export default {
  name: 'ToastContainer',
  components: {
    ToastTemplate
  },
  setup() {
    let observer = null

    onMounted(() => {
      // Set up container for toast positioning
      const container = document.getElementById('toast-container')
      
      if (container) {
        // Ensure proper stacking and positioning
        container.style.zIndex = '9999'
        
        // Optional: Set up mutation observer to track toast changes
        observer = new MutationObserver((mutations) => {
          mutations.forEach((mutation) => {
            if (mutation.type === 'childList') {
              // Announce new toasts to screen readers
              mutation.addedNodes.forEach((node) => {
                if (node.nodeType === Node.ELEMENT_NODE && node.classList.contains('toast')) {
                  // Enable pointer events for individual toasts
                  node.style.pointerEvents = 'auto'
                }
              })
            }
          })
        })
        
        observer.observe(container, {
          childList: true,
          subtree: true
        })
        
        console.log('Toast container initialized')
      }
    })

    onUnmounted(() => {
      // Clean up observer
      if (observer) {
        observer.disconnect()
      }
      
      // Clear any remaining toasts
      const container = document.getElementById('toast-container')
      if (container) {
        container.innerHTML = ''
      }
    })

    return {}
  }
}
</script>

<style scoped>
/* Ensure container doesn't interfere with page layout */
#toast-container {
  max-width: 100vw;
  max-height: 100vh;
  overflow: hidden;
}

/* Responsive positioning */
@media (max-width: 640px) {
  #toast-container {
    top: 1rem;
    right: 1rem;
    left: 1rem;
    width: auto;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  #toast-container {
    filter: contrast(1.2);
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  #toast-container * {
    transition: none !important;
    animation: none !important;
  }
}
</style>
