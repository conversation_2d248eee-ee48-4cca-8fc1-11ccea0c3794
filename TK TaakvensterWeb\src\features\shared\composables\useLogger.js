// composables/useLogger.js
import log from 'loglevel';

export function useLogger() {
  // Set log level based on environment
  // TODO: Does this work with our .Net based setup?
  log.setLevel(process.env.NODE_ENV === 'production' ? 'error' : 'debug');

  return {
    debug: (msg) => log.debug(msg),
    info: (msg) => log.info(msg),
    warn: (msg) => log.warn(msg),
    error: (msg) => log.error(msg),
  };
}