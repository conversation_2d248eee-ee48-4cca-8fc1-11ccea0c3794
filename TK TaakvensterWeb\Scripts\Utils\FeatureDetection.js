// FeatureDetection - Feature detection and compatibility utilities for Office Add-ins
const FeatureDetection = (function() {
    // Check if the specified Office API is supported
    function checkApiSupport(apiSet = 'WordApi', version = '1.1') {
        return Office.context.requirements.isSetSupported(apiSet, version);
    }
    
    // Set up fallback mode for older Office versions
    function setupFallbackMode(container, fallbackMessage) {
        if (container) {
            container.innerHTML = fallbackMessage || 
                "This add-in requires Word 2016 or later. Some features may not be available in your version of Office.";
        }
        console.log("API not supported, using fallback mode");
    }
    
    // Public API
    return {
        checkApiSupport,
        setupFallbackMode
    };
})();

// Make it globally accessible
window.FeatureDetection = FeatureDetection;
