{"extends": "@vue/tsconfig/tsconfig.json", "include": ["src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue"], "compilerOptions": {"allowJs": true, "target": "ESNext", "useDefineForClassFields": true, "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "strict": true, "jsx": "preserve", "sourceMap": true, "resolveJsonModule": true, "isolatedModules": true, "esModuleInterop": true, "lib": ["ESNext", "DOM"], "skipLibCheck": true, "noEmit": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}}, "references": [{"path": "./tsconfig.node.json"}]}