using Microsoft.Graph;
using Microsoft.Identity.Client;
using System;
using System.Diagnostics;
using System.IdentityModel.Tokens.Jwt;
using System.Net.Http.Headers;
using System.Threading.Tasks;

namespace TK_TaakvensterWeb
{
    /// <summary>
    /// Service for handling Graph authentication
    /// </summary>
    public class GraphAuthenticationService : IGraphAuthenticationService
    {
        private readonly string _clientId;
        private readonly string _clientSecret;
        private readonly string _tenantId;
        private readonly string _authority;
        private readonly string _graphScope;
        private string _lastError;

        /// <summary>
        /// Constructor
        /// </summary>
        public GraphAuthenticationService(
            string clientId,
            string clientSecret,
            string tenantId,
            string authority,
            string graphScope)
        {
            _clientId = clientId;
            _clientSecret = clientSecret;
            _tenantId = tenantId;
            _authority = authority;
            _graphScope = graphScope;
        }

        /// <summary>
        /// Gets the last error that occurred during authentication
        /// </summary>
        public string GetLastError()
        {
            return _lastError;
        }

        /// <summary>
        /// Gets a Graph client using On-Behalf-Of flow with the provided user token
        /// </summary>
        public async Task<GraphServiceClient> GetGraphClientWithOBO(string userToken)
        {
            try
            {
                _lastError = null;
                
                // Initialize MSAL client for OBO flow
                var app = ConfidentialClientApplicationBuilder
                    .Create(_clientId)
                    .WithClientSecret(_clientSecret)
                    .WithAuthority(_authority)
                    .Build();

                // Perform OBO flow to get Graph token
                var result = await app.AcquireTokenOnBehalfOf(
                    new[] { _graphScope },
                    new UserAssertion(userToken))
                    .ExecuteAsync();
                    
                // Initialize Graph client with the token
                return new GraphServiceClient(new DelegateAuthenticationProvider(
                    async (requestMessage) =>
                    {
                        requestMessage.Headers.Authorization = new AuthenticationHeaderValue("Bearer", result.AccessToken);
                    }));
            }
            catch (MsalUiRequiredException ex)
            {
                // This exception occurs when the user needs to provide additional claims or consent
                _lastError = $"Authentication requires user interaction: {ex.Message}";
                Debug.WriteLine(_lastError);
                return null;
            }
            catch (MsalServiceException ex)
            {
                // This exception occurs when MSAL is unable to get a token from the authority
                _lastError = $"Authentication service error: {ex.Message}";
                Debug.WriteLine(_lastError);
                return null;
            }
            catch (Exception ex)
            {
                _lastError = $"Authentication failed: {ex.Message}";
                Debug.WriteLine(_lastError);
                return null;
            }
        }

        // Token logging method removed for security reasons
    }
}
