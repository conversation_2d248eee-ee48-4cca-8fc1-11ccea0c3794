/**
 * TemplateUtils.js
 * Utility functions for handling HTML templates
 */

const TemplateUtils = (function() {
    // Template IDs for various UI states
    const templateIds = {
        serviceItem: 'service-item-template',
        loadingState: 'loading-template',
        errorState: 'error-template',
        emptyState: 'empty-template'
    };
    
    /**
     * Gets the content of a template by its ID
     * @param {string} templateId - The ID of the template to get
     * @returns {string} The template HTML content
     */
    function getTemplate(templateId) {
        const template = document.getElementById(templateId);
        if (!template) {
            console.warn(`Template with ID '${templateId}' not found`);
            return '';
        }
        return template.innerHTML;
    }
    
    /**
     * Replaces placeholders in a template with actual values
     * @param {string} template - The template string with placeholders
     * @param {Object} data - The data object with values to replace placeholders
     * @returns {string} The template with placeholders replaced
     */
    function applyTemplate(template, data) {
        return template.replace(/\{\{(\w+)\}\}/g, function(match, key) {
            return data[key] !== undefined ? data[key] : match;
        });
    }
    
    // Public API
    return {
        templateIds: templateIds,
        getTemplate: getTemplate,
        applyTemplate: applyTemplate
    };
})();
