import { ref, onMounted } from 'vue'
import { useOffice } from '../../office'
import { useErrorHandler } from '../../shared'
import { getStepBehaviorConfig } from '../../../config/appConfig'

/**
 * Composable for common step functionality
 * Provides reusable logic while keeping template flexibility
 */
export function useStepLogic(options = {}) {
  const {
    autoCompleteOnSuccess = true,
    completionDelay = 500,
    validationMessage = 'Selecteer eerst een optie voordat u verder gaat.',
    onStepReady = null,
    onStepComplete = null,
  } = options

  const { insertTextInContentControl } = useOffice()
  const { withErrorHandling } = useErrorHandler()

  const selectedIndex = ref(null)
  const isProcessing = ref(false)

  /**
   * Standard item selection handler
   * @param {Array} items - The items array
   * @param {number} index - Selected index
   * @param {Function} emit - Vue emit function
   * @param {Object} customOptions - Override options for this specific call
   */
  async function handleItemSelection(items, index, emit, customOptions = {}) {
    if (isProcessing.value) return // Prevent double-clicks

    try {
      isProcessing.value = true

      // Validation
      if (index === null || index === undefined || index < 0 || index >= items.length) {
        await withErrorHandling(
          () => Promise.reject(new Error('Invalid selection')),
          'validating selection',
          { userMessage: customOptions.validationMessage || validationMessage },
        )
        return
      }

      selectedIndex.value = index
      const item = items[index]
      const itemLabel = getItemLabel(item)

      // Insert into document
      await withErrorHandling(
        () => insertTextInContentControl(item.controlTag, item.value),
        `adding ${itemLabel}`,
        {
          userMessage:
            customOptions.errorMessage || `Failed to add ${itemLabel}. Please try again.`,
          successMessage:
            customOptions.successMessage || `${itemLabel} is toegevoegd aan het document.`,
        },
      )

      // Custom success callback
      if (customOptions.onSuccess) {
        await customOptions.onSuccess(item, index)
      }

      // Auto-complete if enabled
      if (customOptions.autoComplete !== false && autoCompleteOnSuccess) {
        setTimeout(() => {
          emit('step-complete')
          if (onStepComplete) onStepComplete()
        }, customOptions.delay || getStepBehaviorConfig().completionDelay)
      }
    } catch (error) {
      // Reset selection on error
      selectedIndex.value = null
      console.error('Error processing selection:', error)

      // Custom error callback
      if (customOptions.onError) {
        customOptions.onError(error)
      }
    } finally {
      isProcessing.value = false
    }
  }

  /**
   * Get consistent item label
   */
  function getItemLabel(item) {
    return item.label || item.naam || item.toString()
  }

  /**
   * Standard step initialization
   */
  function initializeStep(emit) {
    onMounted(() => {
      emit('step-ready')
      if (onStepReady) onStepReady()
    })
  }

  /**
   * Reset step state
   */
  function resetStep() {
    selectedIndex.value = null
    isProcessing.value = false
  }

  return {
    // State
    selectedIndex,
    isProcessing,

    // Methods
    handleItemSelection,
    getItemLabel,
    initializeStep,
    resetStep,

    // Utilities
    withErrorHandling, // Re-export for custom usage
  }
}
