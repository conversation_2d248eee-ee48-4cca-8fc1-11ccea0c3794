# TK Word Add-in

## Overview

This Word Add-in provides document classification functionality for <PERSON><PERSON><PERSON> Ka<PERSON> (Dutch Parliament) documents. It allows users to easily add classification labels and services to Word documents through a panel-based interface.

The project has been migrated from a JavaScript-only approach to a modern Vue.js implementation with improved maintainability, development experience, and performance.

## 🚀 Quick Start

### Option 1: Pure Vue Development (Fastest for UI Work)
1. Open terminal in the project root
2. Run: `npm run dev`
3. This will:
   - Start the Vite dev server on http://localhost:5174 (or 5173 if available)
   - Enable hot module replacement (instant updates)
   - Provide Vue DevTools integration
   - Show helpful error overlays

### Option 2: Visual Studio (Recommended for Office Add-in Testing)
1. Open `TK TaakvensterWeb.sln` in Visual Studio
2. Press **F5** or click **Start**
3. Visual Studio will:
   - Run `npm install` (if needed)
   - Build the Vue app (`npm run build:dev` for Debug, `npm run build` for Release)
   - Output files to `wwwroot/` directory
   - Start IIS Express
   - Launch the Office Add-in

### Option 3: Development with Auto-Rebuild
1. Open terminal in the project root
2. Run: `npm run dev:watch`
3. This will:
   - Build the Vue app in development mode
   - Output files to `wwwroot/` directory
   - Watch for file changes
   - Auto-rebuild when you save Vue files
4. In Visual Studio, press **F5** to start IIS Express
5. Refresh the add-in in Word to see changes

## 📁 Project Structure

```
TK TaakvensterWeb/
├── src/                    # Vue.js source code
│   ├── components/         # Vue components
│   │   └── steps/          # Step components for the multi-step form
│   ├── composables/        # Vue composables (including Office.js integration)
│   ├── stores/             # Pinia stores
│   └── main.js             # Vue app entry point
├── wwwroot/                # Built assets (generated)
│   ├── assets/             # CSS/JS files with hashes
│   └── index.html          # Generated HTML with injected assets
├── build-scripts/          # Build automation scripts
│   └── inject-assets.js    # Script for injecting built assets
├── index.html              # Source HTML template
├── vite.config.js          # Vite configuration
├── package.json            # Node.js dependencies and scripts
└── TK TaakvensterWeb.csproj # Visual Studio project file
```

## 🔧 Build Process

### Static File Approach

The project uses a standard static file approach:

1. Vite builds the Vue app to the `wwwroot/` directory
2. IIS serves the static files directly from `wwwroot/`
3. Vite automatically handles asset versioning and injection

### Build Configurations

**Debug Configuration (Development)**
- Source maps enabled for debugging
- Larger bundle size (readable code)
- Console logs preserved
- Faster build time
- Command: `npm run build:dev`
- Output: `wwwroot/` directory

**Release Configuration (Production)**
- Minified and optimized
- Smaller bundle size
- Source maps excluded
- Console logs removed
- Command: `npm run build`
- Output: `wwwroot/` directory

### Hybrid Development/Production Setup

The project features a hybrid setup that works for both:
- `npm run dev` (development with Vite dev server)
- Visual Studio builds (production with asset injection)

This is achieved through:
1. A Node.js script that automatically injects Vite-built assets into index.html during production builds
2. Updated .csproj file to automatically run Vue builds during Visual Studio build process
3. Maintaining compatibility with Vite dev server for rapid development

## 🛠️ Development Workflow

### UI Development (Fastest)
1. Run `npm run dev` in terminal
2. Open http://localhost:5174 (or 5173) in browser
3. Edit Vue files in the `src/` directory
4. Changes appear instantly with hot module replacement
5. Vue DevTools available for debugging

### Office Add-in Testing
1. Run `npm run dev:watch` in terminal (auto-rebuilds on changes)
2. Press F5 in Visual Studio to start IIS Express
3. Test in Word with the actual Office Add-in
4. Refresh the add-in panel to see changes

### Production Testing
1. Run `npm run build` for production build
2. Press F5 in Visual Studio (Release configuration)
3. Test the add-in with optimized production assets

## 📦 Available Scripts

```bash
# Development server (Vite dev server)
npm run dev

# Watch mode (auto-rebuild on changes)
npm run dev:watch

# Production build
npm run build

# Development build
npm run build:dev

# Preview built app
npm run preview

# Run tests
npm run test:unit

# Lint code
npm run lint

# Format code
npm run format
```

## 🔍 Troubleshooting

### Build Issues

**"npm install" fails**
- Ensure Node.js is installed (version 18+)
- Delete `node_modules` and `package-lock.json`
- Run `npm install` again

**Assets not loading**
- Check if `wwwroot/` folder exists
- Verify `index.html` has injected asset tags
- Run `npm run build:dev` manually

**Visual Studio build fails**
- Ensure Node.js is in PATH
- Check build output for npm errors
- Try building manually: `npm run build:dev`

### Runtime Issues

**Vue app not loading**
- Check browser console for errors
- Verify Office.js is loaded
- Check if assets are accessible via IIS Express

**Office Add-in not starting**
- Verify IIS Express is running
- Check SSL certificate (port 44323)
- Ensure manifest file is correct

## 🎯 Best Practices

### Development
- Use `npm run dev:watch` for active development
- Test with Visual Studio's F5 before committing
- Keep Vue components small and focused
- Use TypeScript for better development experience

### Performance
- The build process generates optimized bundles
- CSS and JS are automatically minified in Release mode
- Tree-shaking removes unused code (saving ~37% in bundle size)
- Single bundle approach for Office Add-ins

### Keyboard Navigation
- The add-in supports keyboard navigation:
  - `d`: Toggle panel visibility (global)
  - `1-9`: Select steps when panel is visible
  - Arrow keys: Navigate between steps
  - Home/End: First/last step
  - Escape: Close panel

## 🔄 Migration Notes

This setup replaces the previous JavaScript-only approach:
- **Old**: Direct JavaScript files, manual asset management
- **New**: Vue.js components, automated build process, optimized assets
- **Benefits**: Better maintainability, modern development experience, optimized performance

### Key Features
- Panel-based UI with services that can be inserted into Word documents
- Templated approach for rendering services data
- Uses Fluent UI (formerly Office UI Fabric) components and Tailwind CSS for styling
- Automatic navigation between steps after actions
- Comprehensive keyboard navigation support

## 📝 Notes

- The `wwwroot/` directory contains generated files and should not be edited directly
- Source files in `src/` are automatically compiled to `wwwroot/`
- IIS Express serves files from `wwwroot/` directory
- Vite handles all asset versioning and injection automatically

### Debugging

**Vue.js Debugging (Development Mode)**
- Source maps are enabled in Debug configuration
- Use browser dev tools to debug Vue components
- Console logs are preserved

**Office Add-in Debugging**
- Use Visual Studio's debugging tools
- Set breakpoints in C# code
- Use Office's developer tools for client-side debugging
