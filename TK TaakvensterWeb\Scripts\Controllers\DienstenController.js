const DienstenController = (function() {
    function _initialize() {
        console.log('Initializing services module...');
        
        ServicesRenderer.initialize();

        console.log('Services module initialized');
    }
    

    async function _loadServicesData() {
        try {
            // Load data with fallback
            const result = await ServiceRepository.loadWithFallback();
            
            // Render the services with the loaded data
            ServicesRenderer.renderServices(result.services);
            
        } catch (error) {
            // Show error notification
            OfficeUtils.UI.showNotification(
                'Fout bij Laden', 
                'Er is een fout opgetreden bij het laden van diensten.'
            );
            
            // Render empty state
            ServicesRenderer.renderServices([]);
        }
    }
    

    async function _refreshSharePointData() {
        try {
            // Load data from SharePoint
            const services = await ServiceRepository.loadFromSharePoint(true);
            
            // Render the services with the loaded data
            ServicesRenderer.renderServices(services);
        } catch (error) {
            // Show error notification
            OfficeUtils.UI.showNotification(
                'Fout bij Vernieuwen', 
                'Er is een fout opgetreden bij het vernieuwen van diensten.'
            );
            
            // Don't render empty state here, keep the current services displayed
        }
    }
    

    async function _loadStaticData() {
        try {
            // Load data from static source
            const services = await ServiceRepository.loadFromStaticData();
            
            // Render the services with the loaded data
            ServicesRenderer.renderServices(services);
            
        } catch (error) {
            // Show error notification
            OfficeUtils.UI.showNotification(
                'Fout bij Laden', 
                'Er is een fout opgetreden bij het laden van statische diensten.'
            );
            
            // Render empty state
            ServicesRenderer.renderServices([]);
        }
    }
    
    // Public API
    return {
        initialize: _initialize,
        loadServicesData: _loadServicesData,
        refreshSharePointData: _refreshSharePointData,
        loadStaticData: _loadStaticData
    };
})();
