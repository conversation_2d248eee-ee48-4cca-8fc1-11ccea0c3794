<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title>Function File</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script type="text/javascript" src="https://appsforoffice.microsoft.com/lib/1/hosted/office.js"></script>
    <script type="text/javascript">
        Office.onReady(function() {
            // Office is ready
        });
        
        // Function to handle commands from the ribbon
        function handleCommand(event) {
            // You can add specific command handling here
            event.completed();
        }
        
        // Register the function
        Office.actions.associate("handleCommand", handleCommand);
    </script>
</head>
<body>
    <!-- Function files don't need any UI -->
</body>
</html>
