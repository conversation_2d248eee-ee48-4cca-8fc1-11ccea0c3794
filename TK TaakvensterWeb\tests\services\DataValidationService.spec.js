import { describe, it, expect, beforeEach, vi } from 'vitest'
import { DataValidationService } from '../../src/services/DataValidationService.js'

// Mock the config
vi.mock('../../src/config/appConfig.js', () => ({
  getValidationConfig: () => ({
    profile: {
      requiredFields: ['FirstName', 'LastName'],
      optionalFields: ['Role', 'EmailAddress', 'CommissionId']
    },
    commission: {
      requiredFields: ['CommissionName'],
      optionalFields: ['POBoxNumber', 'ZipCode', 'City', 'StreetName', 'HouseNumber']
    }
  })
}))

describe('DataValidationService', () => {
  describe('validateProfile', () => {
    it('should validate a complete valid profile', () => {
      const profile = {
        FirstName: 'Jan',
        LastName: 'de Bont',
        Role: 'Developer',
        EmailAddress: '<EMAIL>',
        CommissionId: 'test-commission'
      }

      const result = DataValidationService.validateProfile(profile)

      expect(result.isValid).toBe(true)
      expect(result.errors).toEqual([])
      expect(result.validatedData).toEqual({
        FirstName: 'Jan',
        LastName: 'de Bont',
        Role: 'Developer',
        EmailAddress: '<EMAIL>',
        CommissionId: 'test-commission'
      })
    })

    it('should validate a minimal valid profile', () => {
      const profile = {
        FirstName: 'Jan',
        LastName: 'de Bont'
      }

      const result = DataValidationService.validateProfile(profile)

      expect(result.isValid).toBe(true)
      expect(result.errors).toEqual([])
      expect(result.validatedData).toEqual({
        FirstName: 'Jan',
        LastName: 'de Bont',
        Role: '',
        EmailAddress: '',
        CommissionId: ''
      })
    })

    it('should reject profile with missing FirstName', () => {
      const profile = {
        LastName: 'de Bont',
        Role: 'Developer'
      }

      const result = DataValidationService.validateProfile(profile, 0)

      expect(result.isValid).toBe(false)
      expect(result.errors).toEqual(['Profile at index 0: Missing or empty FirstName'])
    })

    it('should reject profile with empty FirstName', () => {
      const profile = {
        FirstName: '',
        LastName: 'de Bont'
      }

      const result = DataValidationService.validateProfile(profile, 1)

      expect(result.isValid).toBe(false)
      expect(result.errors).toEqual(['Profile at index 1: Missing or empty FirstName'])
    })

    it('should reject profile with whitespace-only FirstName', () => {
      const profile = {
        FirstName: '   \t\n   ',
        LastName: 'de Bont'
      }

      const result = DataValidationService.validateProfile(profile)

      expect(result.isValid).toBe(false)
      expect(result.errors).toEqual(['Profile: Missing or empty FirstName'])
    })

    it('should reject profile with non-string FirstName', () => {
      const profile = {
        FirstName: 123,
        LastName: 'de Bont'
      }

      const result = DataValidationService.validateProfile(profile)

      expect(result.isValid).toBe(false)
      expect(result.errors).toEqual(['Profile: Missing or empty FirstName'])
    })

    it('should reject profile with missing LastName', () => {
      const profile = {
        FirstName: 'Jan',
        Role: 'Developer'
      }

      const result = DataValidationService.validateProfile(profile)

      expect(result.isValid).toBe(false)
      expect(result.errors).toEqual(['Profile: Missing or empty LastName'])
    })

    it('should reject profile with multiple missing required fields', () => {
      const profile = {
        Role: 'Developer',
        EmailAddress: '<EMAIL>'
      }

      const result = DataValidationService.validateProfile(profile, 5)

      expect(result.isValid).toBe(false)
      expect(result.errors).toEqual([
        'Profile at index 5: Missing or empty FirstName',
        'Profile at index 5: Missing or empty LastName'
      ])
    })

    it('should handle null profile gracefully', () => {
      const result = DataValidationService.validateProfile(null)

      expect(result.isValid).toBe(false)
      expect(result.errors.length).toBeGreaterThan(0)
    })

    it('should handle undefined profile gracefully', () => {
      const result = DataValidationService.validateProfile(undefined)

      expect(result.isValid).toBe(false)
      expect(result.errors.length).toBeGreaterThan(0)
    })
  })

  describe('validateCommission', () => {
    it('should validate a complete valid commission', () => {
      const commission = {
        CommissionName: 'Test Commission',
        POBoxNumber: 'Postbus 12345',
        ZipCode: '1234 AB',
        City: 'Test City',
        StreetName: 'Test Street',
        HouseNumber: '123'
      }

      const result = DataValidationService.validateCommission(commission)

      expect(result.isValid).toBe(true)
      expect(result.errors).toEqual([])
      expect(result.validatedData).toEqual(commission)
    })

    it('should validate a minimal valid commission', () => {
      const commission = {
        CommissionName: 'Test Commission'
      }

      const result = DataValidationService.validateCommission(commission)

      expect(result.isValid).toBe(true)
      expect(result.errors).toEqual([])
      expect(result.validatedData).toEqual({
        CommissionName: 'Test Commission',
        POBoxNumber: '',
        ZipCode: '',
        City: '',
        StreetName: '',
        HouseNumber: ''
      })
    })

    it('should reject commission with missing CommissionName', () => {
      const commission = {
        POBoxNumber: 'Postbus 12345',
        City: 'Test City'
      }

      const result = DataValidationService.validateCommission(commission, 2)

      expect(result.isValid).toBe(false)
      expect(result.errors).toEqual(['Commission at index 2: Missing or empty CommissionName'])
    })

    it('should reject commission with empty CommissionName', () => {
      const commission = {
        CommissionName: '',
        City: 'Test City'
      }

      const result = DataValidationService.validateCommission(commission)

      expect(result.isValid).toBe(false)
      expect(result.errors).toEqual(['Commission: Missing or empty CommissionName'])
    })

    it('should reject commission with non-string CommissionName', () => {
      const commission = {
        CommissionName: 123,
        City: 'Test City'
      }

      const result = DataValidationService.validateCommission(commission)

      expect(result.isValid).toBe(false)
      expect(result.errors).toEqual(['Commission: Missing or empty CommissionName'])
    })
  })

  describe('validateArray', () => {
    it('should validate an array of profiles', () => {
      const profiles = [
        { FirstName: 'Jan', LastName: 'de Bont' },
        { FirstName: 'Maria', LastName: 'van der Berg' },
        { FirstName: '', LastName: 'Invalid' }, // Invalid
        { FirstName: 'Valid', LastName: 'User' }
      ]

      const result = DataValidationService.validateArray(profiles, 'profile')

      expect(result.validCount).toBe(3)
      expect(result.invalidCount).toBe(1)
      expect(result.validItems).toHaveLength(3)
      expect(result.invalidItems).toHaveLength(1)
      expect(result.invalidItems[0].index).toBe(2)
      expect(result.errors).toEqual(['Profile at index 2: Missing or empty FirstName'])
    })

    it('should validate an array of commissions', () => {
      const commissions = [
        { CommissionName: 'Valid Commission 1' },
        { CommissionName: '' }, // Invalid
        { CommissionName: 'Valid Commission 2' }
      ]

      const result = DataValidationService.validateArray(commissions, 'commission')

      expect(result.validCount).toBe(2)
      expect(result.invalidCount).toBe(1)
      expect(result.validItems).toHaveLength(2)
      expect(result.invalidItems).toHaveLength(1)
      expect(result.invalidItems[0].index).toBe(1)
    })

    it('should throw error for unknown validation type', () => {
      const items = [{ test: 'data' }]

      expect(() => {
        DataValidationService.validateArray(items, 'unknown')
      }).toThrow('Unknown validation type: unknown')
    })

    it('should handle empty array', () => {
      const result = DataValidationService.validateArray([], 'profile')

      expect(result.validCount).toBe(0)
      expect(result.invalidCount).toBe(0)
      expect(result.validItems).toEqual([])
      expect(result.invalidItems).toEqual([])
      expect(result.errors).toEqual([])
    })
  })

  describe('future validators', () => {
    it('should have placeholder for dienst validation', () => {
      const dienst = { name: 'Test Dienst' }
      const result = DataValidationService.validateDienst(dienst)

      expect(result.isValid).toBe(true)
      expect(result.errors).toEqual([])
      expect(result.validatedData).toEqual(dienst)
    })

    it('should have placeholder for rubricering validation', () => {
      const rubricering = { name: 'Test Rubricering' }
      const result = DataValidationService.validateRubricering(rubricering)

      expect(result.isValid).toBe(true)
      expect(result.errors).toEqual([])
      expect(result.validatedData).toEqual(rubricering)
    })
  })

  describe('edge cases and malformed data', () => {
    it('should handle circular references gracefully', () => {
      const profile = { FirstName: 'Jan', LastName: 'de Bont' }
      profile.self = profile // Create circular reference

      const result = DataValidationService.validateProfile(profile)

      expect(result.isValid).toBe(true)
      // Should not throw error despite circular reference
    })

    it('should handle very long strings', () => {
      const longString = 'a'.repeat(10000)
      const profile = {
        FirstName: longString,
        LastName: 'de Bont'
      }

      const result = DataValidationService.validateProfile(profile)

      expect(result.isValid).toBe(true)
      expect(result.validatedData.FirstName).toBe(longString)
    })

    it('should handle special characters in names', () => {
      const profile = {
        FirstName: 'José-María',
        LastName: "O'Connor-Smith"
      }

      const result = DataValidationService.validateProfile(profile)

      expect(result.isValid).toBe(true)
      expect(result.validatedData.FirstName).toBe('José-María')
      expect(result.validatedData.LastName).toBe("O'Connor-Smith")
    })

    it('should handle unicode characters', () => {
      const profile = {
        FirstName: '张三',
        LastName: '李四'
      }

      const result = DataValidationService.validateProfile(profile)

      expect(result.isValid).toBe(true)
    })
  })
})
