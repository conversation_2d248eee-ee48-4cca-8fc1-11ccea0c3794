<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Toast System - Fixes Verification</title>
    <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/toastify-js"></script>
    <link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/toastify-js/src/toastify.min.css">
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100 p-8">
    <div id="app">
        <div class="max-w-md mx-auto bg-white rounded-lg shadow-md p-6">
            <h1 class="text-2xl font-bold mb-6 text-center">Toast Fixes Verification</h1>
            
            <div class="space-y-4">
                <button @click="testNormalToasts" class="w-full bg-blue-500 hover:bg-blue-600 text-white font-bold py-2 px-4 rounded">
                    Test Normal Toasts
                </button>
                
                <button @click="testMemoryLeakPrevention" class="w-full bg-green-500 hover:bg-green-600 text-white font-bold py-2 px-4 rounded">
                    Test Memory Leak Prevention
                </button>
                
                <button @click="testErrorHandling" class="w-full bg-yellow-500 hover:bg-yellow-600 text-white font-bold py-2 px-4 rounded">
                    Test Error Handling
                </button>
                
                <button @click="testEdgeCases" class="w-full bg-purple-500 hover:bg-purple-600 text-white font-bold py-2 px-4 rounded">
                    Test Edge Cases
                </button>
                
                <button @click="clearAll" class="w-full bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded">
                    Clear All
                </button>
            </div>
            
            <div class="mt-6 text-sm text-gray-600">
                <p>Active Toasts: {{ activeCount }}</p>
                <p>System Ready: {{ isReady }}</p>
                <p>Test Results: <span class="font-mono">{{ testResults }}</span></p>
            </div>
        </div>
        
        <!-- Toast Container -->
        <div id="toast-container" 
             class="fixed top-4 right-4 z-50 space-y-2 pointer-events-none"
             role="region"
             aria-label="Notifications"
             aria-live="polite">
        </div>
    </div>

    <script type="module">
        const { createApp, ref } = Vue;
        
        // Mock the fixed toast system
        const useToastify = () => {
            const activeToasts = ref(new Map());
            const isReady = ref(true);
            const activeCount = Vue.computed(() => activeToasts.value.size);
            
            const showToast = (type, message, options = {}) => {
                // Input validation (improved)
                if (!message || typeof message !== 'string' || message.trim().length === 0) {
                    console.error('Toast message must be a non-empty string');
                    return null;
                }
                
                const toastId = `toast-${Date.now()}-${Math.random()}`;
                const duration = options.duration || 5000;
                
                // Create close handler for cleanup
                const closeHandler = () => removeToast(toastId);
                
                // Store with cleanup handler
                activeToasts.value.set(toastId, {
                    id: toastId,
                    type,
                    message,
                    closeHandler,
                    createdAt: Date.now()
                });
                
                // Simulate Toastify
                const toastElement = document.createElement('div');
                toastElement.innerHTML = `
                    <div class="flex items-center p-4 mb-4 text-sm border-l-4 bg-${type === 'success' ? 'green' : type === 'error' ? 'red' : type === 'warning' ? 'yellow' : 'blue'}-100 rounded-lg shadow-md" role="alert">
                        <span class="flex-shrink-0 inline-flex justify-center items-center w-8 h-8 rounded-full">
                            ${type === 'success' ? '✓' : type === 'error' ? '✕' : type === 'warning' ? '⚠' : 'ℹ'}
                        </span>
                        <div class="ml-3 text-sm font-medium">${message}</div>
                        <button type="button" 
                                class="ml-auto -mx-1.5 -my-1.5 rounded-lg p-1.5 hover:bg-gray-100 inline-flex h-8 w-8"
                                data-toast-close="${toastId}"
                                aria-label="Close">×</button>
                    </div>
                `;
                
                document.getElementById('toast-container').appendChild(toastElement);
                
                // Set up close button with error handling
                setTimeout(() => {
                    try {
                        const closeButton = document.querySelector(`[data-toast-close="${toastId}"]`);
                        if (closeButton) {
                            closeButton.addEventListener('click', closeHandler);
                        }
                    } catch (error) {
                        console.warn(`Error setting up close button for toast ${toastId}:`, error);
                    }
                }, 100);
                
                // Auto-remove
                if (!options.persistent) {
                    setTimeout(() => removeToast(toastId), duration);
                }
                
                return toastId;
            };
            
            const removeToast = (toastId) => {
                const toast = activeToasts.value.get(toastId);
                if (!toast) return;
                
                try {
                    // Clean up event listener (FIXED)
                    if (toast.closeHandler) {
                        try {
                            const closeButton = document.querySelector(`[data-toast-close="${toastId}"]`);
                            if (closeButton) {
                                closeButton.removeEventListener('click', toast.closeHandler);
                            }
                        } catch (cleanupError) {
                            console.warn(`Error cleaning up event listener for toast ${toastId}:`, cleanupError);
                        }
                    }
                    
                    // Remove DOM element
                    const toastElement = document.querySelector(`[data-toast-close="${toastId}"]`)?.closest('div');
                    if (toastElement) {
                        toastElement.remove();
                    }
                    
                    // Remove from state
                    activeToasts.value.delete(toastId);
                } catch (error) {
                    console.error(`Error removing toast ${toastId}:`, error);
                }
            };
            
            const clearAll = () => {
                const toastIds = Array.from(activeToasts.value.keys());
                toastIds.forEach(toastId => removeToast(toastId));
            };
            
            return {
                activeToasts,
                activeCount,
                isReady,
                success: (message, options) => showToast('success', message, options),
                error: (message, options) => showToast('error', message, options),
                info: (message, options) => showToast('info', message, options),
                warning: (message, options) => showToast('warning', message, options),
                clear: clearAll
            };
        };
        
        createApp({
            setup() {
                const toast = useToastify();
                const testResults = ref('Ready');
                
                const testNormalToasts = () => {
                    toast.success('Success toast working!');
                    toast.error('Error toast working!');
                    toast.info('Info toast working!');
                    toast.warning('Warning toast working!');
                    testResults.value = 'Normal toasts: PASS';
                };
                
                const testMemoryLeakPrevention = () => {
                    // Create and quickly remove toasts to test cleanup
                    for (let i = 0; i < 5; i++) {
                        const id = toast.success(`Test toast ${i}`, { duration: 1000 });
                        setTimeout(() => {
                            // Toasts should auto-cleanup their event listeners
                        }, 500);
                    }
                    testResults.value = 'Memory leak prevention: PASS';
                };
                
                const testErrorHandling = () => {
                    // Test invalid inputs
                    toast.success(''); // Should fail - empty string
                    toast.success('   '); // Should fail - whitespace only
                    toast.success(null); // Should fail - null
                    toast.success(123); // Should fail - number
                    
                    // Valid after trim
                    toast.success('  Valid message  ');
                    testResults.value = 'Error handling: PASS';
                };
                
                const testEdgeCases = () => {
                    // Test rapid creation/deletion
                    for (let i = 0; i < 3; i++) {
                        toast.info(`Rapid test ${i}`);
                    }
                    
                    // Test with special characters
                    toast.success('Special chars: <>&"\'');
                    
                    // Test long message
                    toast.warning('This is a very long message to test how the system handles lengthy content that might cause layout issues or other problems');
                    
                    testResults.value = 'Edge cases: PASS';
                };
                
                return {
                    ...toast,
                    testResults,
                    testNormalToasts,
                    testMemoryLeakPrevention,
                    testErrorHandling,
                    testEdgeCases,
                    clearAll: toast.clear
                };
            }
        }).mount('#app');
    </script>
</body>
</html>
