{"name": "vue-project", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "dev:watch": "vite build --watch --mode development", "build": "vite build --mode production && node build-scripts/inject-assets.js production", "build:dev": "vite build --mode development && node build-scripts/inject-assets.js development", "preview": "vite preview --outDir wwwroot", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs --fix --ignore-path .gitignore", "format": "prettier --write src/", "test": "vitest run", "test:watch": "vitest", "test:coverage": "vitest run --coverage"}, "dependencies": {"@fluentui/web-components": "^3.0.0-beta.113", "@tabler/icons-vue": "^3.34.1", "@vueuse/core": "^13.5.0", "loglevel": "^1.9.2", "pinia": "^3.0.3", "toastify-js": "^1.12.0", "vue": "^3.5.17"}, "devDependencies": {"@eslint/js": "^9.29.0", "@vitejs/plugin-vue": "^6.0.0", "@vitest/eslint-plugin": "^1.2.7", "@vue/eslint-config-prettier": "^10.2.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.7.0", "eslint": "^9.29.0", "eslint-plugin-vue": "~10.2.0", "globals": "^16.2.0", "jsdom": "^26.1.0", "prettier": "3.5.3", "typescript": "^5.8.3", "vite": "^7.0.0", "vite-plugin-vue-devtools": "^7.7.7", "vitest": "^3.2.4", "vue-tsc": "^3.0.3"}}