/*
	Copyright (c) Microsoft Corporation.  All rights reserved.
*/

/*
	Your use of this file is governed by the Microsoft Services Agreement http://go.microsoft.com/fwlink/?LinkId=266419.
*/

/*
* @overview es6-promise - a tiny implementation of Promises/A+.
* @copyright Copyright (c) 2014 <PERSON><PERSON><PERSON>, <PERSON>, <PERSON> and contributors (Conversion to ES6 API by <PERSON>)
* @license   Licensed under MIT license
*            See https://raw.githubusercontent.com/jakearchibald/es6-promise/master/LICENSE
* @version   2.3.0
*/


// Sources:
// osfweb: none
// runtime: 16.0\13326.10000
// core: 16.0\13326.10000
// host: 16.0.13327.34950



var OfficeExtension,__extends=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();!function(e){var t;!function(e){e.OfficeRequire=function(){return null}()}(e._Internal||(e._Internal={})),function(e){!function(t){t.Init=function(){return function(){"use strict";function t(e){return"function"===typeof e}var n,r=Array.isArray?Array.isArray:function(e){return"[object Array]"===Object.prototype.toString.call(e)},i=0,o=function(e,t){l[i]=e,l[i+1]=t,2===(i+=2)&&(n?n(p):u())};var s=("undefined"!==typeof window?window:void 0)||{},a=(s.MutationObserver||s.WebKitMutationObserver,"undefined"!==typeof process&&"[object process]"==={}.toString.call(process)),c="undefined"!==typeof Uint8ClampedArray&&"undefined"!==typeof importScripts&&"undefined"!==typeof MessageChannel;var u,l=new Array(1e3);function p(){for(var e=0;e<i;e+=2){(0,l[e])(l[e+1]),l[e]=void 0,l[e+1]=void 0}i=0}function f(){}u=a?function(){var e=process.nextTick,t=process.versions.node.match(/^(?:(\d+)\.)?(?:(\d+)\.)?(\*|\d+)$/);return Array.isArray(t)&&"0"===t[1]&&"10"===t[2]&&(e=window.setImmediate),function(){e(p)}}():c?function(){var e=new MessageChannel;return e.port1.onmessage=p,function(){e.port2.postMessage(0)}}():function(){return function(){setTimeout(p,1)}}();var h=new O;function d(e,n){if(n.constructor===e.constructor)!function(e,t){1===t._state?g(e,t._result):2===t._state?b(e,t._result):_(t,void 0,(function(t){m(e,t)}),(function(t){b(e,t)}))}(e,n);else{var r=function(e){try{return e.then}catch(e){return h.error=e,h}}(n);r===h?b(e,h.error):void 0===r?g(e,n):t(r)?function(e,t,n){o((function(e){var r=!1,i=function(e,t,n,r){try{e.call(t,n,r)}catch(e){return e}}(n,t,(function(n){r||(r=!0,t!==n?m(e,n):g(e,n))}),(function(t){r||(r=!0,b(e,t))}),e._label);!r&&i&&(r=!0,b(e,i))}),e)}(e,n,r):g(e,n)}}function m(e,t){e===t?b(e,function(){return new TypeError("You cannot resolve a promise with itself")}()):!function(e){return"function"===typeof e||"object"===typeof e&&null!==e}(t)?g(e,t):d(e,t)}function y(e){e._onerror&&e._onerror(e._result),v(e)}function g(e,t){void 0===e._state&&(e._result=t,e._state=1,0!==e._subscribers.length&&o(v,e))}function b(e,t){void 0===e._state&&(e._state=2,e._result=t,o(y,e))}function _(e,t,n,r){var i=e._subscribers,s=i.length;e._onerror=null,i[s]=t,i[s+1]=n,i[s+2]=r,0===s&&e._state&&o(v,e)}function v(e){var t=e._subscribers,n=e._state;if(0!==t.length){for(var r,i,o=e._result,s=0;s<t.length;s+=3)r=t[s],i=t[s+n],r?I(n,r,i,o):i(o);e._subscribers.length=0}}function O(){this.error=null}var P=new O;function I(e,n,r,i){var o,s,a,c,u=t(r);if(u){if((o=function(e,t){try{return e(t)}catch(e){return P.error=e,P}}(r,i))===P?(c=!0,s=o.error,o=null):a=!0,n===o)return void b(n,function(){return new TypeError("A promises callback cannot return that same promise.")}())}else o=i,a=!0;void 0!==n._state||(u&&a?m(n,o):c?b(n,s):1===e?g(n,o):2===e&&b(n,o))}function R(e,t){this._instanceConstructor=e,this.promise=new e(f),this._validateInput(t)?(this._input=t,this.length=t.length,this._remaining=t.length,this._init(),0===this.length?g(this.promise,this._result):(this.length=this.length||0,this._enumerate(),0===this._remaining&&g(this.promise,this._result))):b(this.promise,this._validationError())}R.prototype._validateInput=function(e){return r(e)},R.prototype._validationError=function(){return new e.Error("Array Methods must be provided an Array")},R.prototype._init=function(){this._result=new Array(this.length)};var j=R;R.prototype._enumerate=function(){for(var e=this.length,t=this.promise,n=this._input,r=0;void 0===t._state&&r<e;r++)this._eachEntry(n[r],r)},R.prototype._eachEntry=function(e,t){var n=this._instanceConstructor;!function(e){return"object"===typeof e&&null!==e}(e)?(this._remaining--,this._result[t]=e):e.constructor===n&&void 0!==e._state?(e._onerror=null,this._settledAt(e._state,t,e._result)):this._willSettleAt(n.resolve(e),t)},R.prototype._settledAt=function(e,t,n){var r=this.promise;void 0===r._state&&(this._remaining--,2===e?b(r,n):this._result[t]=n),0===this._remaining&&g(r,this._result)},R.prototype._willSettleAt=function(e,t){var n=this;_(e,void 0,(function(e){n._settledAt(1,t,e)}),(function(e){n._settledAt(2,t,e)}))};var A=function(e){return new j(this,e).promise};var C=function(e){var t=new this(f);if(!r(e))return b(t,new TypeError("You must pass an array to race.")),t;var n=e.length;function i(e){m(t,e)}function o(e){b(t,e)}for(var s=0;void 0===t._state&&s<n;s++)_(this.resolve(e[s]),void 0,i,o);return t};var S=function(e){if(e&&"object"===typeof e&&e.constructor===this)return e;var t=new this(f);return m(t,e),t};var E=function(e){var t=new this(f);return b(t,e),t},x=0;var N=T;function T(e){this._id=x++,this._state=void 0,this._result=void 0,this._subscribers=[],f!==e&&(t(e)||function(){throw new TypeError("You must pass a resolver function as the first argument to the promise constructor")}(),this instanceof T||function(){throw new TypeError("Failed to construct 'Promise': Please use the 'new' operator, this object constructor cannot be called as a function.")}(),function(e,t){try{t((function(t){m(e,t)}),(function(t){b(e,t)}))}catch(t){b(e,t)}}(this,e))}return T.all=A,T.race=C,T.resolve=S,T.reject=E,T._setScheduler=function(e){n=e},T._setAsap=function(e){o=e},T._asap=o,T.prototype={constructor:T,then:function(e,t){var n=this,r=n._state;if(1===r&&!e||2===r&&!t)return this;var i=new this.constructor(f),s=n._result;if(r){var a=arguments[r-1];o((function(){I(r,i,a,s)}))}else _(n,i,e,t);return i},catch:function(e){return this.then(null,e)}},N}.call(this)}}(e.PromiseImpl||(e.PromiseImpl={}))}(e._Internal||(e._Internal={})),function(e){e.OfficePromise=function(){return"undefined"===typeof window&&"function"===typeof Promise?Promise:"undefined"!==typeof window&&window.Promise?function(){var e=window.navigator.userAgent,t=e.indexOf("Edge/");return t>=0&&(e=e.substring(t+5,e.length))<"14.14393"}()?e.PromiseImpl.Init():window.Promise:e.PromiseImpl.Init()}()}(t=e._Internal||(e._Internal={}));var n=t.OfficePromise;e.Promise=n}(OfficeExtension||(OfficeExtension={})),function(e){var t=function(){function e(){}return e.prototype._resolveRequestUrlAndHeaderInfo=function(){return u._createPromiseFromResult(null)},e.prototype._createRequestExecutorOrNull=function(){return null},Object.defineProperty(e.prototype,"eventRegistration",{get:function(){return null},enumerable:!0,configurable:!0}),e}();e.SessionBase=t;var n=function(){function e(){}return e.setCustomSendRequestFunc=function(t){e.s_customSendRequestFunc=t},e.xhrSendRequestFunc=function(e){return u.createPromise((function(t,n){var i=new XMLHttpRequest;if(i.open(e.method,e.url),i.onload=function(){var e={statusCode:i.status,headers:u._parseHttpResponseHeaders(i.getAllResponseHeaders()),body:i.responseText};t(e)},i.onerror=function(){n(new r.RuntimeError({code:o.connectionFailure,httpStatusCode:i.status,message:u._getResourceString(s.connectionFailureWithStatus,i.statusText)}))},e.headers)for(var a in e.headers)i.setRequestHeader(a,e.headers[a]);i.send(u._getRequestBodyText(e))}))},e.fetchSendRequestFunc=function(e){var t=u._getRequestBodyText(e);return""===t&&(t=void 0),fetch(e.url,{method:e.method,headers:e.headers,body:t}).then((function(e){return e.text().then((function(t){var n=e.status,r={};return e.headers.forEach((function(e,t){r[t]=e})),{statusCode:n,headers:r,body:t}}))}))},e.sendRequest=function(t){e.validateAndNormalizeRequest(t);var n=e.s_customSendRequestFunc;return n||(n="undefined"!==typeof fetch?e.fetchSendRequestFunc:e.xhrSendRequestFunc),n(t)},e.setCustomSendLocalDocumentRequestFunc=function(t){e.s_customSendLocalDocumentRequestFunc=t},e.sendLocalDocumentRequest=function(t){return e.validateAndNormalizeRequest(t),(e.s_customSendLocalDocumentRequestFunc||e.officeJsSendLocalDocumentRequestFunc)(t)},e.officeJsSendLocalDocumentRequestFunc=function(e){e=u._validateLocalDocumentRequest(e);var t=u._buildRequestMessageSafeArray(e);return u.createPromise((function(e,n){OSF.DDA.RichApi.executeRichApiRequestAsync(t,(function(t){var n;n="succeeded"==t.status?{statusCode:c.getResponseStatusCode(t),headers:c.getResponseHeaders(t),body:c.getResponseBody(t)}:c.buildHttpResponseFromOfficeJsError(t.error.code,t.error.message),u.log("Response:"),u.log(JSON.stringify(n)),e(n)}))}))},e.validateAndNormalizeRequest=function(e){if(u.isNullOrUndefined(e))throw r.RuntimeError._createInvalidArgError({argumentName:"request"});u.isNullOrEmptyString(e.method)&&(e.method="GET"),e.method=e.method.toUpperCase()},e.logRequest=function(t){if(u._logEnabled){if(u.log("---HTTP Request---"),u.log(t.method+" "+t.url),t.headers)for(var n in t.headers)u.log(n+": "+t.headers[n]);e._logBodyEnabled&&u.log(u._getRequestBodyText(t))}},e.logResponse=function(t){if(u._logEnabled){if(u.log("---HTTP Response---"),u.log(""+t.statusCode),t.headers)for(var n in t.headers)u.log(n+": "+t.headers[n]);e._logBodyEnabled&&u.log(t.body)}},e._logBodyEnabled=!1,e}();e.HttpUtility=n;var r,i=function(){function e(e){var t=this;this.m_bridge=e,this.m_promiseResolver={},this.m_handlers=[],this.m_bridge.onMessageFromHost=function(e){var n=JSON.parse(e);if(3==n.type){var r=n.message;if(r&&r.entries)for(var i=0;i<r.entries.length;i++){var o=r.entries[i];if(Array.isArray(o)){var s={messageCategory:o[0],messageType:o[1],targetId:o[2],message:o[3],id:o[4]};r.entries[i]=s}}}t.dispatchMessage(n)}}return e.init=function(t){if("object"===typeof t&&t){var r=new e(t);e.s_instance=r,n.setCustomSendLocalDocumentRequestFunc((function(t){t=u._validateLocalDocumentRequest(t);var n=0;u.isReadonlyRestRequest(t.method)||(n=1);var i=t.url.indexOf("?");if(i>=0){var o=t.url.substr(i+1),s=u._parseRequestFlagsAndCustomDataFromQueryStringIfAny(o);s.flags>=0&&(n=s.flags)}var a={id:e.nextId(),type:1,flags:n,message:t};return r.sendMessageToHostAndExpectResponse(a).then((function(e){return e.message}))}));for(var i=0;i<e.s_onInitedHandlers.length;i++)e.s_onInitedHandlers[i](r)}},Object.defineProperty(e,"instance",{get:function(){return e.s_instance},enumerable:!0,configurable:!0}),e.prototype.sendMessageToHost=function(e){this.m_bridge.sendMessageToHost(JSON.stringify(e))},e.prototype.sendMessageToHostAndExpectResponse=function(e){var t=this,n=u.createPromise((function(n,r){t.m_promiseResolver[e.id]=n}));return this.m_bridge.sendMessageToHost(JSON.stringify(e)),n},e.prototype.addHostMessageHandler=function(e){this.m_handlers.push(e)},e.prototype.removeHostMessageHandler=function(e){var t=this.m_handlers.indexOf(e);t>=0&&this.m_handlers.splice(t,1)},e.onInited=function(t){e.s_onInitedHandlers.push(t),e.s_instance&&t(e.s_instance)},e.prototype.dispatchMessage=function(e){if("number"===typeof e.id){var t=this.m_promiseResolver[e.id];if(t)return t(e),void delete this.m_promiseResolver[e.id]}for(var n=0;n<this.m_handlers.length;n++)this.m_handlers[n](e)},e.nextId=function(){return e.s_nextId++},e.s_onInitedHandlers=[],e.s_nextId=1,e}();e.HostBridge=i,"object"===typeof _richApiNativeBridge&&_richApiNativeBridge&&i.init(_richApiNativeBridge),function(e){var t=function(t){function n(e){var r=t.call(this,"string"===typeof e?e:e.message)||this;if(Object.setPrototypeOf(r,n.prototype),r.name="RichApi.Error","string"===typeof e?r.message=e:(r.code=e.code,r.message=e.message,r.traceMessages=e.traceMessages||[],r.innerError=e.innerError||null,r.debugInfo=r._createDebugInfo(e.debugInfo||{}),r.httpStatusCode=e.httpStatusCode,r.data=e.data),u.isNullOrUndefined(r.httpStatusCode)||200===r.httpStatusCode){var i={};i[o.accessDenied]=401,i[o.connectionFailure]=500,i[o.generalException]=500,i[o.invalidArgument]=400,i[o.invalidObjectPath]=400,i[o.invalidOrTimedOutSession]=408,i[o.invalidRequestContext]=400,i[o.timeout]=408,i[o.valueNotLoaded]=400,r.httpStatusCode=i[r.code]}return u.isNullOrUndefined(r.httpStatusCode)&&(r.httpStatusCode=500),r}return __extends(n,t),n.prototype.toString=function(){return this.code+": "+this.message},n.prototype._createDebugInfo=function(t){var n={code:this.code,message:this.message,toString:function(){return JSON.stringify(this)}};for(var r in t)n[r]=t[r];return this.innerError&&(this.innerError instanceof e.RuntimeError?n.innerError=this.innerError.debugInfo:n.innerError=this.innerError),n},n._createInvalidArgError=function(t){return new e.RuntimeError({code:o.invalidArgument,httpStatusCode:400,message:u.isNullOrEmptyString(t.argumentName)?u._getResourceString(s.invalidArgumentGeneric):u._getResourceString(s.invalidArgument,t.argumentName),debugInfo:t.errorLocation?{errorLocation:t.errorLocation}:{},innerError:t.innerError})},n}(Error);e.RuntimeError=t}(r=e._Internal||(e._Internal={})),e.Error=r.RuntimeError;var o=function(){function e(){}return e.apiNotFound="ApiNotFound",e.accessDenied="AccessDenied",e.generalException="GeneralException",e.activityLimitReached="ActivityLimitReached",e.invalidArgument="InvalidArgument",e.connectionFailure="ConnectionFailure",e.timeout="Timeout",e.invalidOrTimedOutSession="InvalidOrTimedOutSession",e.invalidObjectPath="InvalidObjectPath",e.invalidRequestContext="InvalidRequestContext",e.valueNotLoaded="ValueNotLoaded",e}();e.CoreErrorCodes=o;var s=function(){function e(){}return e.apiNotFoundDetails="ApiNotFoundDetails",e.connectionFailureWithStatus="ConnectionFailureWithStatus",e.connectionFailureWithDetails="ConnectionFailureWithDetails",e.invalidArgument="InvalidArgument",e.invalidArgumentGeneric="InvalidArgumentGeneric",e.timeout="Timeout",e.invalidOrTimedOutSessionMessage="InvalidOrTimedOutSessionMessage",e.invalidObjectPath="InvalidObjectPath",e.invalidRequestContext="InvalidRequestContext",e.valueNotLoaded="ValueNotLoaded",e}();e.CoreResourceStrings=s;var a=function(){function e(){}return e.flags="flags",e.sourceLibHeader="SdkVersion",e.processQuery="ProcessQuery",e.localDocument="http://document.localhost/",e.localDocumentApiPrefix="http://document.localhost/_api/",e.customData="customdata",e}();e.CoreConstants=a;var c=function(){function e(){}return e.buildMessageArrayForIRequestExecutor=function(t,n,r,i){var o=JSON.stringify(r.Body);u.log("Request:"),u.log(o);var s={};return u._copyHeaders(r.Headers,s),s[a.sourceLibHeader]=i,e.buildRequestMessageSafeArray(t,n,"POST",a.processQuery,s,o)},e.buildResponseOnSuccess=function(e,t){var n={HttpStatusCode:200,ErrorCode:"",ErrorMessage:"",Headers:null,Body:null};return n.Body=JSON.parse(e),n.Headers=t,n},e.buildResponseOnError=function(t,n){var r={HttpStatusCode:500,ErrorCode:"",ErrorMessage:"",Headers:null,Body:null};return r.ErrorCode=o.generalException,r.ErrorMessage=n,t==e.OfficeJsErrorCode_ooeNoCapability?(r.ErrorCode=o.accessDenied,r.HttpStatusCode=401):t==e.OfficeJsErrorCode_ooeActivityLimitReached?(r.ErrorCode=o.activityLimitReached,r.HttpStatusCode=429):t==e.OfficeJsErrorCode_ooeInvalidOrTimedOutSession&&(r.ErrorCode=o.invalidOrTimedOutSession,r.HttpStatusCode=408,r.ErrorMessage=u._getResourceString(s.invalidOrTimedOutSessionMessage)),r},e.buildHttpResponseFromOfficeJsError=function(t,n){var r=500,i={error:{}};return i.error.code=o.generalException,i.error.message=n,t===e.OfficeJsErrorCode_ooeNoCapability?(r=403,i.error.code=o.accessDenied):t===e.OfficeJsErrorCode_ooeActivityLimitReached&&(r=429,i.error.code=o.activityLimitReached),{statusCode:r,headers:{},body:JSON.stringify(i)}},e.buildRequestMessageSafeArray=function(e,t,n,r,i,o){var s=[];if(i)for(var a in i)s.push(a),s.push(i[a]);return[e,n,r,s,o,0,t,"","",""]},e.getResponseBody=function(t){return e.getResponseBodyFromSafeArray(t.value.data)},e.getResponseHeaders=function(t){return e.getResponseHeadersFromSafeArray(t.value.data)},e.getResponseBodyFromSafeArray=function(e){var t=e[2];return"string"===typeof t?t:t.join("")},e.getResponseHeadersFromSafeArray=function(e){var t=e[1];if(!t)return null;for(var n={},r=0;r<t.length-1;r+=2)n[t[r]]=t[r+1];return n},e.getResponseStatusCode=function(t){return e.getResponseStatusCodeFromSafeArray(t.value.data)},e.getResponseStatusCodeFromSafeArray=function(e){return e[0]},e.OfficeJsErrorCode_ooeInvalidOrTimedOutSession=5012,e.OfficeJsErrorCode_ooeActivityLimitReached=5102,e.OfficeJsErrorCode_ooeNoCapability=7e3,e}();e.RichApiMessageUtility=c,function(e){e.getPromiseType=function(){if("undefined"!==typeof Promise)return Promise;if("undefined"!==typeof Office&&Office.Promise)return Office.Promise;if("undefined"!==typeof OfficeExtension&&OfficeExtension.Promise)return OfficeExtension.Promise;throw new e.Error("No Promise implementation found")}}(r=e._Internal||(e._Internal={}));var u=function(){function e(){}return e.log=function(t){e._logEnabled&&"undefined"!==typeof console&&console.log&&console.log(t)},e.checkArgumentNull=function(t,n){if(e.isNullOrUndefined(t))throw r.RuntimeError._createInvalidArgError({argumentName:n})},e.isNullOrUndefined=function(e){return null===e||"undefined"===typeof e},e.isUndefined=function(e){return"undefined"===typeof e},e.isNullOrEmptyString=function(e){return null===e||("undefined"===typeof e||0==e.length)},e.isPlainJsonObject=function(t){if(e.isNullOrUndefined(t))return!1;if("object"!==typeof t)return!1;if("[object Object]"!==Object.prototype.toString.apply(t))return!1;if(t.constructor&&!Object.prototype.hasOwnProperty.call(t,"constructor")&&!Object.prototype.hasOwnProperty.call(t.constructor.prototype,"hasOwnProperty"))return!1;for(var n in t)if(!Object.prototype.hasOwnProperty.call(t,n))return!1;return!0},e.trim=function(e){return e.replace(new RegExp("^\\s+|\\s+$","g"),"")},e.caseInsensitiveCompareString=function(t,n){return e.isNullOrUndefined(t)?e.isNullOrUndefined(n):!e.isNullOrUndefined(n)&&t.toUpperCase()==n.toUpperCase()},e.isReadonlyRestRequest=function(t){return e.caseInsensitiveCompareString(t,"GET")},e._getResourceString=function(t,n){var r;if("undefined"!==typeof window&&window.Strings&&window.Strings.OfficeOM){var i="L_"+t,o=window.Strings.OfficeOM[i];o&&(r=o)}if(r||(r=e.s_resourceStringValues[t]),r||(r=t),!e.isNullOrUndefined(n))if(Array.isArray(n)){var s=n;r=e._formatString(r,s)}else r=r.replace("{0}",n);return r},e._formatString=function(e,t){return e.replace(/\{\d\}/g,(function(e){var n=parseInt(e.substr(1,e.length-2));if(n<t.length)return t[n];throw r.RuntimeError._createInvalidArgError({argumentName:"format"})}))},Object.defineProperty(e,"Promise",{get:function(){return r.getPromiseType()},enumerable:!0,configurable:!0}),e.createPromise=function(t){return new e.Promise(t)},e._createPromiseFromResult=function(t){return e.createPromise((function(e,n){e(t)}))},e._createPromiseFromException=function(t){return e.createPromise((function(e,n){n(t)}))},e._createTimeoutPromise=function(t){return e.createPromise((function(e,n){setTimeout((function(){e(null)}),t)}))},e._createInvalidArgError=function(e){return r.RuntimeError._createInvalidArgError(e)},e._isLocalDocumentUrl=function(t){return e._getLocalDocumentUrlPrefixLength(t)>0},e._getLocalDocumentUrlPrefixLength=function(e){for(var t=["http://document.localhost","https://document.localhost","//document.localhost"],n=e.toLowerCase().trim(),r=0;r<t.length;r++){if(n===t[r])return t[r].length;if(n.substr(0,t[r].length+1)===t[r]+"/")return t[r].length+1}return 0},e._validateLocalDocumentRequest=function(t){var n=e._getLocalDocumentUrlPrefixLength(t.url);if(n<=0)throw r.RuntimeError._createInvalidArgError({argumentName:"request"});var i=t.url.substr(n),o=i.toLowerCase();return"_api"===o?i="":"_api/"===o.substr(0,"_api/".length)&&(i=i.substr("_api/".length)),{method:t.method,url:i,headers:t.headers,body:t.body}},e._parseRequestFlagsAndCustomDataFromQueryStringIfAny=function(e){for(var t={flags:-1,customData:""},n=e.split("&"),r=0;r<n.length;r++){var i=n[r].split("=");if(i[0].toLowerCase()===a.flags){var o=parseInt(i[1]);o&=4095,t.flags=o}else i[0].toLowerCase()===a.customData&&(t.customData=decodeURIComponent(i[1]))}return t},e._getRequestBodyText=function(e){var t="";return"string"===typeof e.body?t=e.body:e.body&&"object"===typeof e.body&&(t=JSON.stringify(e.body)),t},e._parseResponseBody=function(t){if("string"===typeof t.body){var n=e.trim(t.body);return JSON.parse(n)}return t.body},e._buildRequestMessageSafeArray=function(t){var n=0;e.isReadonlyRestRequest(t.method)||(n=1);var r="";if(t.url.substr(0,a.processQuery.length).toLowerCase()===a.processQuery.toLowerCase()){var i=t.url.indexOf("?");if(i>0){var o=t.url.substr(i+1),s=e._parseRequestFlagsAndCustomDataFromQueryStringIfAny(o);s.flags>=0&&(n=s.flags),r=s.customData}}return c.buildRequestMessageSafeArray(r,n,t.method,t.url,t.headers,e._getRequestBodyText(t))},e._parseHttpResponseHeaders=function(t){var n={};if(!e.isNullOrEmptyString(t))for(var r=new RegExp("\r?\n"),i=t.split(r),o=0;o<i.length;o++){var s=i[o];if(null!=s){var a=s.indexOf(":");if(a>0){var c=s.substr(0,a),u=s.substr(a+1);c=e.trim(c),u=e.trim(u),n[c.toUpperCase()]=u}}}return n},e._parseErrorResponse=function(t){var n=null;if(e.isPlainJsonObject(t.body))n=t.body;else if(!e.isNullOrEmptyString(t.body)){var r=e.trim(t.body);try{n=JSON.parse(r)}catch(t){e.log("Error when parse "+r)}}var i=t.statusCode.toString();if(e.isNullOrUndefined(n)||"object"!==typeof n||!n.error)return e._createDefaultErrorResponse(i);var o=n.error,s=o.innerError;return s&&s.code?e._createErrorResponse(s.code,i,s.message):o.code?e._createErrorResponse(o.code,i,o.message):e._createDefaultErrorResponse(i)},e._createDefaultErrorResponse=function(t){return{errorCode:o.connectionFailure,errorMessage:e._getResourceString(s.connectionFailureWithStatus,t)}},e._createErrorResponse=function(t,n,r){return{errorCode:t,errorMessage:e._getResourceString(s.connectionFailureWithDetails,[n,t,r])}},e._copyHeaders=function(e,t){if(e&&t)for(var n in e)t[n]=e[n]},e.addResourceStringValues=function(t){for(var n in t)e.s_resourceStringValues[n]=t[n]},e._logEnabled=!1,e.s_resourceStringValues={ApiNotFoundDetails:"The method or property {0} is part of the {1} requirement set, which is not available in your version of {2}.",ConnectionFailureWithStatus:"The request failed with status code of {0}.",ConnectionFailureWithDetails:"The request failed with status code of {0}, error code {1} and the following error message: {2}",InvalidArgument:"The argument '{0}' doesn't work for this situation, is missing, or isn't in the right format.",InvalidObjectPath:'The object path \'{0}\' isn\'t working for what you\'re trying to do. If you\'re using the object across multiple "context.sync" calls and outside the sequential execution of a ".run" batch, please use the "context.trackedObjects.add()" and "context.trackedObjects.remove()" methods to manage the object\'s lifetime.',InvalidRequestContext:"Cannot use the object across different request contexts.",Timeout:"The operation has timed out.",ValueNotLoaded:'The value of the result object has not been loaded yet. Before reading the value property, call "context.sync()" on the associated request context.'},e}();e.CoreUtility=u;var l=function(){function e(){}return e.setMock=function(t){e.s_isMock=t},e.isMock=function(){return e.s_isMock},e}();e.TestUtility=l,e._internalConfig={showDisposeInfoInDebugInfo:!1,showInternalApiInDebugInfo:!1,enableEarlyDispose:!0,alwaysPolyfillClientObjectUpdateMethod:!1,alwaysPolyfillClientObjectRetrieveMethod:!1,enableConcurrentFlag:!0,enableUndoableFlag:!0,appendTypeNameToObjectPathInfo:!1},e.config={extendedErrorLogging:!1};var p=function(){function e(){}return e.createSetPropertyAction=function(e,t,n,r,i){P.validateObjectPath(t);var o={Id:e._nextId(),ActionType:4,Name:n,ObjectPathId:t._objectPath.objectPathInfo.Id,ArgumentInfo:{}},s=[r],a=P.setMethodArguments(e,o.ArgumentInfo,s);P.validateReferencedObjectPaths(a);var c=new h(o,0,i);return c.referencedObjectPath=t._objectPath,c.referencedArgumentObjectPaths=a,t._addAction(c)},e.createQueryAction=function(e,t,n,r){P.validateObjectPath(t);var i={Id:e._nextId(),ActionType:2,Name:"",ObjectPathId:t._objectPath.objectPathInfo.Id,QueryInfo:n},o=new h(i,1,4);return o.referencedObjectPath=t._objectPath,t._addAction(o,r)},e.createQueryAsJsonAction=function(e,t,n,r){P.validateObjectPath(t);var i={Id:e._nextId(),ActionType:7,Name:"",ObjectPathId:t._objectPath.objectPathInfo.Id,QueryInfo:n},o=new h(i,1,4);return o.referencedObjectPath=t._objectPath,t._addAction(o,r)},e.createUpdateAction=function(e,t,n){P.validateObjectPath(t);var r={Id:e._nextId(),ActionType:9,Name:"",ObjectPathId:t._objectPath.objectPathInfo.Id,ObjectState:n},i=new h(r,0,0);return i.referencedObjectPath=t._objectPath,t._addAction(i)},e}();e.CommonActionFactory=p;var f=function(){function t(e,t){this.m_contextBase=e,this.m_objectPath=t}return Object.defineProperty(t.prototype,"_objectPath",{get:function(){return this.m_objectPath},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_context",{get:function(){return this.m_contextBase},enumerable:!0,configurable:!0}),t.prototype._addAction=function(e,t){var n=this;return void 0===t&&(t=null),u.createPromise((function(r,i){n._context._addServiceApiAction(e,t,r,i)}))},t.prototype._retrieve=function(t,n){var r=e._internalConfig.alwaysPolyfillClientObjectRetrieveMethod;r||(r=!P.isSetSupported("RichApiRuntime","1.1"));var i=m._parseQueryOption(t);return r?p.createQueryAction(this._context,this,i,n):p.createQueryAsJsonAction(this._context,this,i,n)},t.prototype._recursivelyUpdate=function(t){var n=e._internalConfig.alwaysPolyfillClientObjectUpdateMethod;n||(n=!P.isSetSupported("RichApiRuntime","1.2"));try{var i=this[O.scalarPropertyNames];i||(i=[]);var a=this[O.scalarPropertyUpdateable];if(!a){a=[];for(var c=0;c<i.length;c++)a.push(!1)}var l=this[O.navigationPropertyNames];l||(l=[]);var f={},h={},d=0;for(var m in t){var y=i.indexOf(m);if(y>=0){if(!a[y])throw new r.RuntimeError({code:o.invalidArgument,httpStatusCode:400,message:u._getResourceString(I.attemptingToSetReadOnlyProperty,m),debugInfo:{errorLocation:m}});f[m]=t[m],++d}else{if(!(l.indexOf(m)>=0))throw new r.RuntimeError({code:o.invalidArgument,httpStatusCode:400,message:u._getResourceString(I.propertyDoesNotExist,m),debugInfo:{errorLocation:m}});h[m]=t[m]}}if(d>0)if(n)for(c=0;c<i.length;c++){var g=f[m=i[c]];P.isUndefined(g)||p.createSetPropertyAction(this._context,this,m,g)}else p.createUpdateAction(this._context,this,f);for(var m in h){var b=this[m],_=h[m];b._recursivelyUpdate(_)}}catch(e){throw new r.RuntimeError({code:o.invalidArgument,httpStatusCode:400,message:u._getResourceString(s.invalidArgument,"properties"),debugInfo:{errorLocation:this._className+".update"},innerError:e})}},t}();e.ClientObjectBase=f;var h=function(){function e(e,t,n){this.m_actionInfo=e,this.m_operationType=t,this.m_flags=n}return Object.defineProperty(e.prototype,"actionInfo",{get:function(){return this.m_actionInfo},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"operationType",{get:function(){return this.m_operationType},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"flags",{get:function(){return this.m_flags},enumerable:!0,configurable:!0}),e}();e.Action=h;var d=function(){function t(e,t,n,r,i,o){this.m_objectPathInfo=e,this.m_parentObjectPath=t,this.m_isCollection=n,this.m_isInvalidAfterRequest=r,this.m_isValid=!0,this.m_operationType=i,this.m_flags=o}return Object.defineProperty(t.prototype,"id",{get:function(){var e=this.m_objectPathInfo.ArgumentInfo;if(e){var t=e.Arguments;if(t)return t[0]}},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"parent",{get:function(){var e=this.m_parentObjectPath;if(e)return e},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"parentId",{get:function(){return this.parent?this.parent.id:void 0},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"objectPathInfo",{get:function(){return this.m_objectPathInfo},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"operationType",{get:function(){return this.m_operationType},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"flags",{get:function(){return this.m_flags},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"isCollection",{get:function(){return this.m_isCollection},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"isInvalidAfterRequest",{get:function(){return this.m_isInvalidAfterRequest},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"parentObjectPath",{get:function(){return this.m_parentObjectPath},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"argumentObjectPaths",{get:function(){return this.m_argumentObjectPaths},set:function(e){this.m_argumentObjectPaths=e},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"isValid",{get:function(){return this.m_isValid},set:function(e){this.m_isValid=e,!e&&6===this.m_objectPathInfo.ObjectPathType&&this.m_savedObjectPathInfo&&(t.copyObjectPathInfo(this.m_savedObjectPathInfo.pathInfo,this.m_objectPathInfo),this.m_parentObjectPath=this.m_savedObjectPathInfo.parent,this.m_isValid=!0,this.m_savedObjectPathInfo=null)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"originalObjectPathInfo",{get:function(){return this.m_originalObjectPathInfo},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"getByIdMethodName",{get:function(){return this.m_getByIdMethodName},set:function(e){this.m_getByIdMethodName=e},enumerable:!0,configurable:!0}),t.prototype._updateAsNullObject=function(){this.resetForUpdateUsingObjectData(),this.m_objectPathInfo.ObjectPathType=7,this.m_objectPathInfo.Name="",this.m_parentObjectPath=null},t.prototype.saveOriginalObjectPathInfo=function(){e.config.extendedErrorLogging&&!this.m_originalObjectPathInfo&&(this.m_originalObjectPathInfo={},t.copyObjectPathInfo(this.m_objectPathInfo,this.m_originalObjectPathInfo))},t.prototype.updateUsingObjectData=function(e,n){var r=e[O.referenceId];if(!u.isNullOrEmptyString(r)){if(!this.m_savedObjectPathInfo&&!this.isInvalidAfterRequest&&t.isRestorableObjectPath(this.m_objectPathInfo.ObjectPathType)){var i={};t.copyObjectPathInfo(this.m_objectPathInfo,i),this.m_savedObjectPathInfo={pathInfo:i,parent:this.m_parentObjectPath}}return this.saveOriginalObjectPathInfo(),this.resetForUpdateUsingObjectData(),this.m_objectPathInfo.ObjectPathType=6,this.m_objectPathInfo.Name=r,delete this.m_objectPathInfo.ParentObjectPathId,void(this.m_parentObjectPath=null)}if(n){var o=n[O.collectionPropertyPath];if(!u.isNullOrEmptyString(o)&&n.context){var s=P.tryGetObjectIdFromLoadOrRetrieveResult(e);if(!u.isNullOrUndefined(s)){for(var a=o.split("."),c=n.context[a[0]],l=1;l<a.length;l++)c=c[a[l]];return this.saveOriginalObjectPathInfo(),this.resetForUpdateUsingObjectData(),this.m_parentObjectPath=c._objectPath,this.m_objectPathInfo.ParentObjectPathId=this.m_parentObjectPath.objectPathInfo.Id,this.m_objectPathInfo.ObjectPathType=5,this.m_objectPathInfo.Name="",void(this.m_objectPathInfo.ArgumentInfo.Arguments=[s])}}}var p=this.parentObjectPath&&this.parentObjectPath.isCollection,f=this.getByIdMethodName;if(p||!u.isNullOrEmptyString(f)){s=P.tryGetObjectIdFromLoadOrRetrieveResult(e);if(!u.isNullOrUndefined(s))return this.saveOriginalObjectPathInfo(),this.resetForUpdateUsingObjectData(),u.isNullOrEmptyString(f)?(this.m_objectPathInfo.ObjectPathType=5,this.m_objectPathInfo.Name=""):(this.m_objectPathInfo.ObjectPathType=3,this.m_objectPathInfo.Name=f),void(this.m_objectPathInfo.ArgumentInfo.Arguments=[s])}},t.prototype.resetForUpdateUsingObjectData=function(){this.m_isInvalidAfterRequest=!1,this.m_isValid=!0,this.m_operationType=1,this.m_flags=4,this.m_objectPathInfo.ArgumentInfo={},this.m_argumentObjectPaths=null,this.m_getByIdMethodName=null},t.isRestorableObjectPath=function(e){return 1===e||5===e||3===e||4===e},t.copyObjectPathInfo=function(e,t){t.Id=e.Id,t.ArgumentInfo=e.ArgumentInfo,t.Name=e.Name,t.ObjectPathType=e.ObjectPathType,t.ParentObjectPathId=e.ParentObjectPathId},t}();e.ObjectPath=d;var m=function(){function e(){this.m_nextId=0}return e.prototype._nextId=function(){return++this.m_nextId},e.prototype._addServiceApiAction=function(e,t,n,r){this.m_serviceApiQueue||(this.m_serviceApiQueue=new _(this)),this.m_serviceApiQueue.add(e,t,n,r)},e._parseQueryOption=function(t){var n={};if("string"===typeof t){var i=t;n.Select=P._parseSelectExpand(i)}else if(Array.isArray(t))n.Select=t;else if("object"===typeof t){var o=t;if(e.isLoadOption(o)){if("string"===typeof o.select)n.Select=P._parseSelectExpand(o.select);else if(Array.isArray(o.select))n.Select=o.select;else if(!P.isNullOrUndefined(o.select))throw r.RuntimeError._createInvalidArgError({argumentName:"option.select"});if("string"===typeof o.expand)n.Expand=P._parseSelectExpand(o.expand);else if(Array.isArray(o.expand))n.Expand=o.expand;else if(!P.isNullOrUndefined(o.expand))throw r.RuntimeError._createInvalidArgError({argumentName:"option.expand"});if("number"===typeof o.top)n.Top=o.top;else if(!P.isNullOrUndefined(o.top))throw r.RuntimeError._createInvalidArgError({argumentName:"option.top"});if("number"===typeof o.skip)n.Skip=o.skip;else if(!P.isNullOrUndefined(o.skip))throw r.RuntimeError._createInvalidArgError({argumentName:"option.skip"})}else n=e.parseStrictLoadOption(t)}else if(!P.isNullOrUndefined(t))throw r.RuntimeError._createInvalidArgError({argumentName:"option"});return n},e.isLoadOption=function(e){if(!P.isUndefined(e.select)&&("string"===typeof e.select||Array.isArray(e.select)))return!0;if(!P.isUndefined(e.expand)&&("string"===typeof e.expand||Array.isArray(e.expand)))return!0;if(!P.isUndefined(e.top)&&"number"===typeof e.top)return!0;if(!P.isUndefined(e.skip)&&"number"===typeof e.skip)return!0;for(var t in e)return!1;return!0},e.parseStrictLoadOption=function(t){var n={Select:[]};return e.parseStrictLoadOptionHelper(n,"","option",t),n},e.combineQueryPath=function(e,t,n){return 0===e.length?t:e+n+t},e.parseStrictLoadOptionHelper=function(t,n,i,o){for(var s in o){var a=o[s];if("$all"===s){if("boolean"!==typeof a)throw r.RuntimeError._createInvalidArgError({argumentName:e.combineQueryPath(i,s,".")});a&&t.Select.push(e.combineQueryPath(n,"*","/"))}else if("$top"===s){if("number"!==typeof a||n.length>0)throw r.RuntimeError._createInvalidArgError({argumentName:e.combineQueryPath(i,s,".")});t.Top=a}else if("$skip"===s){if("number"!==typeof a||n.length>0)throw r.RuntimeError._createInvalidArgError({argumentName:e.combineQueryPath(i,s,".")});t.Skip=a}else if("boolean"===typeof a)a&&t.Select.push(e.combineQueryPath(n,s,"/"));else{if("object"!==typeof a)throw r.RuntimeError._createInvalidArgError({argumentName:e.combineQueryPath(i,s,".")});e.parseStrictLoadOptionHelper(t,e.combineQueryPath(n,s,"/"),e.combineQueryPath(i,s,"."),a)}}},e}();e.ClientRequestContextBase=m;var y=function(){function e(e){this.m_objectPath=e}return e.prototype._handleResult=function(e){u.isNullOrUndefined(e)?this.m_objectPath._updateAsNullObject():this.m_objectPath.updateUsingObjectData(e,null)},e}(),g=function(){function t(e){this.m_contextBase=e,this.m_actions=[],this.m_actionResultHandler={},this.m_referencedObjectPaths={},this.m_instantiatedObjectPaths={},this.m_preSyncPromises=[]}return t.prototype.addAction=function(e){this.m_actions.push(e),1==e.actionInfo.ActionType&&(this.m_instantiatedObjectPaths[e.actionInfo.ObjectPathId]=e)},Object.defineProperty(t.prototype,"hasActions",{get:function(){return this.m_actions.length>0},enumerable:!0,configurable:!0}),t.prototype._getLastAction=function(){return this.m_actions[this.m_actions.length-1]},t.prototype.ensureInstantiateObjectPath=function(e){if(e){if(this.m_instantiatedObjectPaths[e.objectPathInfo.Id])return;if(this.ensureInstantiateObjectPath(e.parentObjectPath),this.ensureInstantiateObjectPaths(e.argumentObjectPaths),!this.m_instantiatedObjectPaths[e.objectPathInfo.Id]){var t={Id:this.m_contextBase._nextId(),ActionType:1,Name:"",ObjectPathId:e.objectPathInfo.Id},n=new h(t,1,4);n.referencedObjectPath=e,this.addReferencedObjectPath(e),this.addAction(n);var r=new y(e);this.addActionResultHandler(n,r)}}},t.prototype.ensureInstantiateObjectPaths=function(e){if(e)for(var t=0;t<e.length;t++)this.ensureInstantiateObjectPath(e[t])},t.prototype.addReferencedObjectPath=function(e){if(e&&!this.m_referencedObjectPaths[e.objectPathInfo.Id]){if(!e.isValid)throw new r.RuntimeError({code:o.invalidObjectPath,httpStatusCode:400,message:u._getResourceString(s.invalidObjectPath,P.getObjectPathExpression(e)),debugInfo:{errorLocation:P.getObjectPathExpression(e)}});for(;e;)this.m_referencedObjectPaths[e.objectPathInfo.Id]=e,3==e.objectPathInfo.ObjectPathType&&this.addReferencedObjectPaths(e.argumentObjectPaths),e=e.parentObjectPath}},t.prototype.addReferencedObjectPaths=function(e){if(e)for(var t=0;t<e.length;t++)this.addReferencedObjectPath(e[t])},t.prototype.addActionResultHandler=function(e,t){this.m_actionResultHandler[e.actionInfo.Id]=t},t.prototype.aggregrateRequestFlags=function(e,t,n){return 0===t&&(e|=1,0===(2&n)&&(e&=-17),0===(8&n)&&(e&=-257),e&=-5),1&n&&(e|=2),0===(4&n)&&(e&=-5),e},t.prototype.finallyNormalizeFlags=function(t){return 0===(1&t)&&(t&=-17,t&=-257),e._internalConfig.enableConcurrentFlag||(t&=-5),e._internalConfig.enableUndoableFlag||(t&=-17),P.isSetSupported("RichApiRuntimeFlag","1.1")||(t&=-5,t&=-17),P.isSetSupported("RichApiRuntimeFlag","1.2")||(t&=-257),"number"===typeof this.m_flagsForTesting&&(t=this.m_flagsForTesting),t},t.prototype.buildRequestMessageBodyAndRequestFlags=function(){e._internalConfig.enableEarlyDispose&&t._calculateLastUsedObjectPathIds(this.m_actions);var n=276,r={};for(var i in this.m_referencedObjectPaths)n=this.aggregrateRequestFlags(n,this.m_referencedObjectPaths[i].operationType,this.m_referencedObjectPaths[i].flags),r[i]=this.m_referencedObjectPaths[i].objectPathInfo;for(var o=[],s=!1,a=0;a<this.m_actions.length;a++){var c=this.m_actions[a];3===c.actionInfo.ActionType&&c.actionInfo.Name===O.keepReference&&(s=!0),n=this.aggregrateRequestFlags(n,c.operationType,c.flags),o.push(c.actionInfo)}return n=this.finallyNormalizeFlags(n),{body:{AutoKeepReference:this.m_contextBase._autoCleanup&&s,Actions:o,ObjectPaths:r},flags:n}},t.prototype.processResponse=function(e){if(e)for(var t=0;t<e.length;t++){var n=e[t],r=this.m_actionResultHandler[n.ActionId];r&&r._handleResult(n.Value)}},t.prototype.invalidatePendingInvalidObjectPaths=function(){for(var e in this.m_referencedObjectPaths)this.m_referencedObjectPaths[e].isInvalidAfterRequest&&(this.m_referencedObjectPaths[e].isValid=!1)},t.prototype._addPreSyncPromise=function(e){this.m_preSyncPromises.push(e)},Object.defineProperty(t.prototype,"_preSyncPromises",{get:function(){return this.m_preSyncPromises},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_actions",{get:function(){return this.m_actions},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_objectPaths",{get:function(){return this.m_referencedObjectPaths},enumerable:!0,configurable:!0}),t.prototype._removeKeepReferenceAction=function(e){for(var t=this.m_actions.length-1;t>=0;t--){var n=this.m_actions[t].actionInfo;if(n.ObjectPathId===e&&3===n.ActionType&&n.Name===O.keepReference){this.m_actions.splice(t,1);break}}},t._updateLastUsedActionIdOfObjectPathId=function(e,n,r){for(;n;){if(e[n.objectPathInfo.Id])return;e[n.objectPathInfo.Id]=r;var i=n.argumentObjectPaths;if(i)for(var o=i.length,s=0;s<o;s++)t._updateLastUsedActionIdOfObjectPathId(e,i[s],r);n=n.parentObjectPath}},t._calculateLastUsedObjectPathIds=function(e){for(var n={},r=e.length,i=r-1;i>=0;--i){var o=(f=e[i]).actionInfo.Id;f.referencedObjectPath&&t._updateLastUsedActionIdOfObjectPathId(n,f.referencedObjectPath,o);var s=f.referencedArgumentObjectPaths;if(s)for(var a=s.length,c=0;c<a;c++)t._updateLastUsedActionIdOfObjectPathId(n,s[c],o)}var u={};for(var l in n){var p=u[o=n[l]];p||(p=[],u[o]=p),p.push(parseInt(l))}for(i=0;i<r;i++){var f,h=u[(f=e[i]).actionInfo.Id];h&&h.length>0?f.actionInfo.L=h:f.actionInfo.L&&delete f.actionInfo.L}},t}();e.ClientRequestBase=g;var b=function(){function e(e){this.m_type=e}return Object.defineProperty(e.prototype,"value",{get:function(){if(!this.m_isLoaded)throw new r.RuntimeError({code:o.valueNotLoaded,httpStatusCode:400,message:u._getResourceString(s.valueNotLoaded),debugInfo:{errorLocation:"clientResult.value"}});return this.m_value},enumerable:!0,configurable:!0}),e.prototype._handleResult=function(e){this.m_isLoaded=!0,"object"===typeof e&&e&&e._IsNull||(1===this.m_type?this.m_value=P.adjustToDateTime(e):this.m_value=e)},e}();e.ClientResult=b;var _=function(){function e(e){this.m_context=e,this.m_actions=[]}return e.prototype.add=function(e,t,n,r){var i=this;this.m_actions.push({action:e,resultHandler:t,resolve:n,reject:r}),1===this.m_actions.length&&setTimeout((function(){return i.processActions()}),0)},e.prototype.processActions=function(){var e=this;if(0!==this.m_actions.length){var t=this.m_actions;this.m_actions=[];for(var n=new g(this.m_context),r=0;r<t.length;r++){var i=t[r];n.ensureInstantiateObjectPath(i.action.referencedObjectPath),n.ensureInstantiateObjectPaths(i.action.referencedArgumentObjectPaths),n.addAction(i.action),n.addReferencedObjectPath(i.action.referencedObjectPath),n.addReferencedObjectPaths(i.action.referencedArgumentObjectPaths)}var o=n.buildRequestMessageBodyAndRequestFlags(),s=o.body,c=o.flags,l={Url:a.localDocumentApiPrefix,Headers:null,Body:s};u.log("Request:"),u.log(JSON.stringify(s)),(new v).executeAsync(this.m_context._customData,c,l).then((function(r){e.processResponse(n,t,r)})).catch((function(e){for(var n=0;n<t.length;n++){t[n].reject(e)}}))}},e.prototype.processResponse=function(e,t,n){var r=this.getErrorFromResponse(n),i=null;n.Body.Results?i=n.Body.Results:n.Body.ProcessedResults&&n.Body.ProcessedResults.Results&&(i=n.Body.ProcessedResults.Results),i||(i=[]),this.processActionResults(e,t,i,r)},e.prototype.getErrorFromResponse=function(e){return u.isNullOrEmptyString(e.ErrorCode)?e.Body&&e.Body.Error?new r.RuntimeError({code:e.Body.Error.Code,httpStatusCode:e.Body.Error.HttpStatusCode,message:e.Body.Error.Message}):null:new r.RuntimeError({code:e.ErrorCode,httpStatusCode:e.HttpStatusCode,message:e.ErrorMessage})},e.prototype.processActionResults=function(e,t,n,r){e.processResponse(n);for(var i=0;i<t.length;i++){for(var o=t[i],s=o.action.actionInfo.Id,a=!1,c=0;c<n.length;c++)if(s==n[c].ActionId){var u=n[c].Value;o.resultHandler&&(o.resultHandler._handleResult(u),u=o.resultHandler.value),o.resolve&&o.resolve(u),a=!0;break}!a&&o.reject&&(r?o.reject(r):o.reject("No response for the action."))}},e}(),v=function(){function e(){}return e.prototype.getRequestUrl=function(e,t){return"/"!=e.charAt(e.length-1)&&(e+="/"),e=(e+=a.processQuery)+"?"+a.flags+"="+t.toString()},e.prototype.executeAsync=function(t,r,i){var o={method:"POST",url:this.getRequestUrl(i.Url,r),headers:{},body:i.Body};if(o.headers[a.sourceLibHeader]=e.SourceLibHeaderValue,o.headers["CONTENT-TYPE"]="application/json",i.Headers)for(var s in i.Headers)o.headers[s]=i.Headers[s];return(u._isLocalDocumentUrl(o.url)?n.sendLocalDocumentRequest:n.sendRequest)(o).then((function(e){var t;if(200===e.statusCode)t={HttpStatusCode:e.statusCode,ErrorCode:null,ErrorMessage:null,Headers:e.headers,Body:u._parseResponseBody(e)};else{u.log("Error Response:"+e.body);var n=u._parseErrorResponse(e);t={HttpStatusCode:e.statusCode,ErrorCode:n.errorCode,ErrorMessage:n.errorMessage,Headers:e.headers,Body:null}}return t}))},e.SourceLibHeaderValue="officejs-rest",e}();e.HttpRequestExecutor=v;var O=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return __extends(t,e),t.collectionPropertyPath="_collectionPropertyPath",t.id="Id",t.idLowerCase="id",t.idPrivate="_Id",t.keepReference="_KeepReference",t.objectPathIdPrivate="_ObjectPathId",t.referenceId="_ReferenceId",t.items="_Items",t.itemsLowerCase="items",t.scalarPropertyNames="_scalarPropertyNames",t.scalarPropertyOriginalNames="_scalarPropertyOriginalNames",t.navigationPropertyNames="_navigationPropertyNames",t.scalarPropertyUpdateable="_scalarPropertyUpdateable",t}(a);e.CommonConstants=O;var P=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return __extends(t,e),t.validateObjectPath=function(e){for(var n=e._objectPath;n;){if(!n.isValid)throw new r.RuntimeError({code:o.invalidObjectPath,httpStatusCode:400,message:u._getResourceString(s.invalidObjectPath,t.getObjectPathExpression(n)),debugInfo:{errorLocation:t.getObjectPathExpression(n)}});n=n.parentObjectPath}},t.validateReferencedObjectPaths=function(e){if(e)for(var n=0;n<e.length;n++)for(var i=e[n];i;){if(!i.isValid)throw new r.RuntimeError({code:o.invalidObjectPath,httpStatusCode:400,message:u._getResourceString(s.invalidObjectPath,t.getObjectPathExpression(i))});i=i.parentObjectPath}},t._toCamelLowerCase=function(e){if(u.isNullOrEmptyString(e))return e;for(var t=0;t<e.length&&e.charCodeAt(t)>=65&&e.charCodeAt(t)<=90;)t++;return t<e.length?e.substr(0,t).toLowerCase()+e.substr(t):e.toLowerCase()},t.adjustToDateTime=function(e){if(u.isNullOrUndefined(e))return null;if("string"===typeof e)return new Date(e);if(Array.isArray(e)){for(var n=e,r=0;r<n.length;r++)n[r]=t.adjustToDateTime(n[r]);return n}throw u._createInvalidArgError({argumentName:"date"})},t.tryGetObjectIdFromLoadOrRetrieveResult=function(e){var t=e[O.id];return u.isNullOrUndefined(t)&&(t=e[O.idLowerCase]),u.isNullOrUndefined(t)&&(t=e[O.idPrivate]),t},t.getObjectPathExpression=function(e){for(var n="";e;){switch(e.objectPathInfo.ObjectPathType){case 1:n=n;break;case 2:n="new()"+(n.length>0?".":"")+n;break;case 3:n=t.normalizeName(e.objectPathInfo.Name)+"()"+(n.length>0?".":"")+n;break;case 4:n=t.normalizeName(e.objectPathInfo.Name)+(n.length>0?".":"")+n;break;case 5:n="getItem()"+(n.length>0?".":"")+n;break;case 6:n="_reference()"+(n.length>0?".":"")+n}e=e.parentObjectPath}return n},t.setMethodArguments=function(e,n,r){if(u.isNullOrUndefined(r))return null;var i=new Array,o=new Array,s=t.collectObjectPathInfos(e,r,i,o);return n.Arguments=r,s&&(n.ReferencedObjectPathIds=o),i},t.validateContext=function(e,t){if(e&&t&&t._context!==e)throw new r.RuntimeError({code:o.invalidRequestContext,httpStatusCode:400,message:u._getResourceString(s.invalidRequestContext)})},t.isSetSupported=function(e,t){return!("undefined"!==typeof window&&window.Office&&window.Office.context&&window.Office.context.requirements)||window.Office.context.requirements.isSetSupported(e,t)},t.throwIfApiNotSupported=function(e,n,i,a){if(t._doApiNotSupportedCheck&&!t.isSetSupported(n,i)){var c=u._getResourceString(s.apiNotFoundDetails,[e,n+" "+i,a]);throw new r.RuntimeError({code:o.apiNotFound,httpStatusCode:404,message:c,debugInfo:{errorLocation:e}})}},t.calculateApiFlags=function(e,n,r){return t.isSetSupported(n,r)||(e&=-3),e},t._parseSelectExpand=function(e){var t=[];if(!u.isNullOrEmptyString(e))for(var n=e.split(","),r=0;r<n.length;r++){var i=n[r];(i=o(i.trim())).length>0&&t.push(i)}return t;function o(e){var t=e.toLowerCase();if("items"===t||"items/"===t)return"*";return("items/"===t.substr(0,6)||"items."===t.substr(0,6))&&(e=e.substr(6)),e.replace(new RegExp("[/.]items[/.]","gi"),"/")}},t.changePropertyNameToCamelLowerCase=function(e){if(Array.isArray(e)){for(var n=[],r=0;r<e.length;r++)n.push(this.changePropertyNameToCamelLowerCase(e[r]));return n}if("object"===typeof e&&null!==e){n={};for(var i in e){var o=e[i];if(i===O.items){(n={})[O.itemsLowerCase]=this.changePropertyNameToCamelLowerCase(o);break}n[t._toCamelLowerCase(i)]=this.changePropertyNameToCamelLowerCase(o)}return n}return e},t.purifyJson=function(e){if(Array.isArray(e)){for(var t=[],n=0;n<e.length;n++)t.push(this.purifyJson(e[n]));return t}if("object"===typeof e&&null!==e){t={};for(var r in e)if(95!==r.charCodeAt(0)){var i=e[r];"object"===typeof i&&null!==i&&Array.isArray(i.items)&&(i=i.items),t[r]=this.purifyJson(i)}return t}return e},t.collectObjectPathInfos=function(e,n,r,i){for(var o=!1,s=0;s<n.length;s++)if(n[s]instanceof f){var a=n[s];t.validateContext(e,a),n[s]=a._objectPath.objectPathInfo.Id,i.push(a._objectPath.objectPathInfo.Id),r.push(a._objectPath),o=!0}else if(Array.isArray(n[s])){var c=new Array;t.collectObjectPathInfos(e,n[s],r,c)?(i.push(c),o=!0):i.push(0)}else u.isPlainJsonObject(n[s])?(i.push(0),t.replaceClientObjectPropertiesWithObjectPathIds(n[s],r)):i.push(0);return o},t.replaceClientObjectPropertiesWithObjectPathIds=function(e,n){var r,i;for(var o in e){var s=e[o];if(s instanceof f)n.push(s._objectPath),e[o]=((r={})[O.objectPathIdPrivate]=s._objectPath.objectPathInfo.Id,r);else if(Array.isArray(s))for(var a=0;a<s.length;a++)if(s[a]instanceof f){var c=s[a];n.push(c._objectPath),s[a]=((i={})[O.objectPathIdPrivate]=c._objectPath.objectPathInfo.Id,i)}else u.isPlainJsonObject(s[a])&&t.replaceClientObjectPropertiesWithObjectPathIds(s[a],n);else u.isPlainJsonObject(s)&&t.replaceClientObjectPropertiesWithObjectPathIds(s,n)}},t.normalizeName=function(e){return e.substr(0,1).toLowerCase()+e.substr(1)},t._doApiNotSupportedCheck=!1,t}(u);e.CommonUtility=P;var I=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return __extends(t,e),t.propertyDoesNotExist="PropertyDoesNotExist",t.attemptingToSetReadOnlyProperty="AttemptingToSetReadOnlyProperty",t}(s);e.CommonResourceStrings=I;var R=function(e){function t(t){var n=e.call(this)||this;return n.m_shouldPolyfill=t,n}return __extends(t,e),t.prototype._handleResult=function(t){e.prototype._handleResult.call(this,t),this.m_shouldPolyfill&&(this.m_value=P.changePropertyNameToCamelLowerCase(this.m_value)),this.m_value=this.removeItemNodes(this.m_value)},t.prototype.removeItemNodes=function(e){return"object"===typeof e&&null!==e&&e[O.itemsLowerCase]&&(e=e[O.itemsLowerCase]),P.purifyJson(e)},t}(b);e.ClientRetrieveResult=R;var j=function(){function e(e){this.callback=e}return e.prototype._handleResult=function(e){this.callback&&this.callback()},e}(),A=function(e){function t(t){var n=e.call(this)||this;return n.callback=t,n}return __extends(t,e),t.prototype._handleResult=function(t){e.prototype._handleResult.call(this,t),this.callback()},t}(b);e.ClientResultCallback=A;var C=function(){function t(){}return t.invokeMethod=function(e,t,n,r,i,o){return void 0===n&&(n=0),void 0===r&&(r=[]),void 0===i&&(i=0),void 0===o&&(o=0),u.createPromise((function(o,s){var a=new b,c={Id:e._context._nextId(),ActionType:3,Name:t,ObjectPathId:e._objectPath.objectPathInfo.Id,ArgumentInfo:{}},u=P.setMethodArguments(e._context,c.ArgumentInfo,r),l=new h(c,n,i);l.referencedObjectPath=e._objectPath,l.referencedArgumentObjectPaths=u,e._context._addServiceApiAction(l,a,o,s)}))},t.invokeMethodWithClientResultCallback=function(e,t,n){var r=[];return u.createPromise((function(i,o){var s=new A(e),a={Id:t._context._nextId(),ActionType:3,Name:n,ObjectPathId:t._objectPath.objectPathInfo.Id,ArgumentInfo:{}},c=P.setMethodArguments(t._context,a.ArgumentInfo,r),u=new h(a,0,0);u.referencedObjectPath=t._objectPath,u.referencedArgumentObjectPaths=c,t._context._addServiceApiAction(u,s,i,o)}))},t.invokeRetrieve=function(n,r){var i,o=e._internalConfig.alwaysPolyfillClientObjectRetrieveMethod;if(o||(o=!P.isSetSupported("RichApiRuntime","1.1")),"object"===typeof r[0]&&r[0].hasOwnProperty("$all")){if(!r[0].$all)throw e.Error._createInvalidArgError({});i=r[0]}else i=t._parseSelectOption(r);return n._retrieve(i,new R(o))},t._parseSelectOption=function(n){if(!n||!n[0])throw e.Error._createInvalidArgError({});var r=n[0]&&"string"!==typeof n[0]?n[0]:n;return Array.isArray(r)?r:t.parseRecursiveSelect(r)},t.parseRecursiveSelect=function(e){var t=function(e){return Object.keys(e).reduce((function(n,r){var i=e[r];return"object"===typeof i?n.concat(t(i).map((function(e){return r+"/"+e}))):i?n.concat(r):n}),[])};return t(e)},t.invokeRecursiveUpdate=function(e,t){return u.createPromise((function(n,r){e._recursivelyUpdate(t);var i={Id:e._context._nextId(),ActionType:5,Name:"Trace",ObjectPathId:0},o=new h(i,1,4);e._context._addServiceApiAction(o,null,n,r)}))},t.createRootServiceObject=function(e,t){var n={Id:t._nextId(),ObjectPathType:1,Name:""};return new e(t,new d(n,null,!1,!1,1,4))},t.createTopLevelServiceObject=function(e,t,n,r,i){var o={Id:t._nextId(),ObjectPathType:2,Name:n};return new e(t,new d(o,null,r,!1,1,4|i))},t.createPropertyObject=function(e,t,n,r,i){var o={Id:t._context._nextId(),ObjectPathType:4,Name:n,ParentObjectPathId:t._objectPath.objectPathInfo.Id},s=new d(o,t._objectPath,r,!1,1,4|i);return new e(t._context,s)},t.createIndexerObject=function(e,t,n){var r={Id:t._context._nextId(),ObjectPathType:5,Name:"",ParentObjectPathId:t._objectPath.objectPathInfo.Id,ArgumentInfo:{}};r.ArgumentInfo.Arguments=n;var i=new d(r,t._objectPath,!1,!1,1,4);return new e(t._context,i)},t.createMethodObject=function(e,t,n,r,i,o,s,a,c){var u={Id:t._context._nextId(),ObjectPathType:3,Name:n,ParentObjectPathId:t._objectPath.objectPathInfo.Id,ArgumentInfo:{}},l=P.setMethodArguments(t._context,u.ArgumentInfo,i),p=new d(u,t._objectPath,o,s,r,c);return p.argumentObjectPaths=l,p.getByIdMethodName=a,new e(t._context,p)},t.createAndInstantiateMethodObject=function(e,t,n,r,i,o,s,a,c){return u.createPromise((function(u,l){var p={Id:t._context._nextId(),ObjectPathType:3,Name:n,ParentObjectPathId:t._objectPath.objectPathInfo.Id,ArgumentInfo:{}},f=P.setMethodArguments(t._context,p.ArgumentInfo,i),m=new d(p,t._objectPath,o,s,r,c);m.argumentObjectPaths=f,m.getByIdMethodName=a;var y=new b,g={Id:t._context._nextId(),ActionType:1,Name:"",ObjectPathId:m.objectPathInfo.Id,QueryInfo:{}},_=new h(g,1,4);_.referencedObjectPath=m,t._context._addServiceApiAction(_,y,(function(){return u(new e(t._context,m))}),l)}))},t.createTraceAction=function(e,t){return u.createPromise((function(n,r){var i={Id:e._nextId(),ActionType:5,Name:"Trace",ObjectPathId:0},o=new h(i,1,4),s=new j(t);e._addServiceApiAction(o,s,n,r)}))},t.localDocumentContext=new m,t}();e.OperationalApiHelper=C;var S=function(){function e(e,t,n){this.eventId=e,this.targetId=t,this.eventArgumentTransform=n,this.registeredCallbacks=[]}return e.prototype.add=function(e){this.hasZero()&&J.getGenericEventRegistration().register(this.eventId,this.targetId,this.registerCallback),this.registeredCallbacks.push(e)},e.prototype.remove=function(e){var t=this.registeredCallbacks.lastIndexOf(e);-1!==t&&this.registeredCallbacks.splice(t,1)},e.prototype.removeAll=function(){this.registeredCallbacks=[],J.getGenericEventRegistration().unregister(this.eventId,this.targetId,this.registerCallback)},e.prototype.hasZero=function(){return 0===this.registeredCallbacks.length},Object.defineProperty(e.prototype,"registerCallback",{get:function(){var e=this;return this.outsideCallback||(this.outsideCallback=function(t){e.call(t)}),this.outsideCallback},enumerable:!0,configurable:!0}),e.prototype.call=function(t){var n=this;this.eventArgumentTransform(t).then((function(t){var r=n.registeredCallbacks.map((function(n){return e.callCallback(n,t)}));u.Promise.all(r)}))},e.callCallback=function(t,n){return u._createPromiseFromResult(null).then(e.wrapCallbackInFunction(t,n)).catch((function(e){u.log("Error when invoke handler: "+JSON.stringify(e))}))},e.wrapCallbackInFunction=function(e,t){return function(){return e(t)}},e}();e.GenericEventRegistryOperational=S;var E=function(){function e(){this.eventToTargetToHandlerMap={}}return Object.defineProperty(e,"globalEventRegistry",{get:function(){return e.singleton||(e.singleton=new e),e.singleton},enumerable:!0,configurable:!0}),e.getGlobalEventRegistry=function(t,n,r){var i=e.globalEventRegistry.eventToTargetToHandlerMap;i.hasOwnProperty(t)||(i[t]={});var o=i[t];return o.hasOwnProperty(n)||(o[n]=new S(t,n,r)),o[n]},e.singleton=void 0,e}();e.GlobalEventRegistryOperational=E;var x=function(){function e(e){this.genericEventInfo=e}return e.prototype.add=function(e){var t=this,n=void 0,r=u.createPromise((function(e){n=e}));return this.register(),this.createTrace((function(){var r=t.genericEventInfo.eventType,i=t.genericEventInfo.getTargetIdFunc();E.getGlobalEventRegistry(r,i,t.genericEventInfo.eventArgsTransformFunc).add(e),n()})),r},e.prototype.remove=function(e){var t=this;this.register(),this.createTrace((function(){var n=t.genericEventInfo.eventType,r=t.genericEventInfo.getTargetIdFunc();E.getGlobalEventRegistry(n,r,t.genericEventInfo.eventArgsTransformFunc).remove(e)}))},e.prototype.removeAll=function(){var e=this;this.unregister(),this.createTrace((function(){var t=e.genericEventInfo.eventType,n=e.genericEventInfo.getTargetIdFunc();E.getGlobalEventRegistry(t,n,e.genericEventInfo.eventArgsTransformFunc).removeAll()}))},e.prototype.createTrace=function(e){C.createTraceAction(this.genericEventInfo.object._context,e)},e.prototype.register=function(){C.invokeMethod(this.genericEventInfo.object,this.genericEventInfo.register,0,[],0),J.getGenericEventRegistration().isReady||J.getGenericEventRegistration().ready()},e.prototype.unregister=function(){C.invokeMethod(this.genericEventInfo.object,this.genericEventInfo.unregister)},e}();e.GenericEventHandlerOperational=x;var N=function(){function e(){}return e.invokeOn=function(e,t,n){var r=void 0,i=u.createPromise((function(e,t){r=e}));return e.add(t).then((function(){r({})})),i},e.invokeOff=function(t,n,r,i){if(!r&&!i){var o=Object.keys(t).map((function(e){return t[e]}));return e.invokeAllOff(o)}return r?(i?n.remove(i):n.removeAll(),u.createPromise((function(e,t){return e()}))):u._createPromiseFromException(r+" must be supplied if handler is supplied.")},e.invokeAllOff=function(e){return e.forEach((function(e){e.removeAll()})),u.createPromise((function(e,t){return e()}))},e}();e.EventHelper=N;var T=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return __extends(t,e),t.propertyNotLoaded="PropertyNotLoaded",t.runMustReturnPromise="RunMustReturnPromise",t.cannotRegisterEvent="CannotRegisterEvent",t.invalidOrTimedOutSession="InvalidOrTimedOutSession",t.cannotUpdateReadOnlyProperty="CannotUpdateReadOnlyProperty",t}(o);e.ErrorCodes=T;var w=function(){function e(e){this.m_callback=e}return e.prototype._handleResult=function(e){this.m_callback&&this.m_callback()},e}(),U=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return __extends(t,e),t.createMethodAction=function(e,t,n,r,i,o){X.validateObjectPath(t);var s={Id:e._nextId(),ActionType:3,Name:n,ObjectPathId:t._objectPath.objectPathInfo.Id,ArgumentInfo:{}},a=X.setMethodArguments(e,s.ArgumentInfo,i);X.validateReferencedObjectPaths(a);var c=new h(s,r,X._fixupApiFlags(o));return c.referencedObjectPath=t._objectPath,c.referencedArgumentObjectPaths=a,t._addAction(c),c},t.createRecursiveQueryAction=function(e,t,n){X.validateObjectPath(t);var r={Id:e._nextId(),ActionType:6,Name:"",ObjectPathId:t._objectPath.objectPathInfo.Id,RecursiveQueryInfo:n},i=new h(r,1,4);return i.referencedObjectPath=t._objectPath,t._addAction(i),i},t.createEnsureUnchangedAction=function(e,t,n){X.validateObjectPath(t);var r={Id:e._nextId(),ActionType:8,Name:"",ObjectPathId:t._objectPath.objectPathInfo.Id,ObjectState:n},i=new h(r,1,4);return i.referencedObjectPath=t._objectPath,t._addAction(i),i},t.createInstantiateAction=function(e,t){X.validateObjectPath(t),e._pendingRequest.ensureInstantiateObjectPath(t._objectPath.parentObjectPath),e._pendingRequest.ensureInstantiateObjectPaths(t._objectPath.argumentObjectPaths);var n={Id:e._nextId(),ActionType:1,Name:"",ObjectPathId:t._objectPath.objectPathInfo.Id},r=new h(n,1,4);return r.referencedObjectPath=t._objectPath,t._addAction(r,new z(t),!0),r},t.createTraceAction=function(e,t,n){var r={Id:e._nextId(),ActionType:5,Name:"Trace",ObjectPathId:0},i=new h(r,1,4);return e._pendingRequest.addAction(i),n&&e._pendingRequest.addTrace(r.Id,t),i},t.createTraceMarkerForCallback=function(e,n){var r=t.createTraceAction(e,null,!1);e._pendingRequest.addActionResultHandler(r,new w(n))},t}(p);e.ActionFactory=U;var F=function(t){function n(n,r){var i=t.call(this,n,r)||this;return X.checkArgumentNull(n,"context"),i.m_context=n,i._objectPath&&(!n._processingResult&&n._pendingRequest&&(U.createInstantiateAction(n,i),n._autoCleanup&&i._KeepReference&&n.trackedObjects._autoAdd(i)),e._internalConfig.appendTypeNameToObjectPathInfo&&i._objectPath.objectPathInfo&&i._className&&(i._objectPath.objectPathInfo.T=i._className)),i}return __extends(n,t),Object.defineProperty(n.prototype,"context",{get:function(){return this.m_context},enumerable:!0,configurable:!0}),Object.defineProperty(n.prototype,"isNull",{get:function(){return("undefined"!==typeof this.m_isNull||!l.isMock())&&(X.throwIfNotLoaded("isNull",this._isNull,null,this._isNull),this._isNull)},enumerable:!0,configurable:!0}),Object.defineProperty(n.prototype,"isNullObject",{get:function(){return("undefined"!==typeof this.m_isNull||!l.isMock())&&(X.throwIfNotLoaded("isNullObject",this._isNull,null,this._isNull),this._isNull)},enumerable:!0,configurable:!0}),Object.defineProperty(n.prototype,"_isNull",{get:function(){return this.m_isNull},set:function(e){this.m_isNull=e,e&&this._objectPath&&this._objectPath._updateAsNullObject()},enumerable:!0,configurable:!0}),n.prototype._addAction=function(e,t,n){return void 0===t&&(t=null),n||(this.context._pendingRequest.ensureInstantiateObjectPath(this._objectPath),this.context._pendingRequest.ensureInstantiateObjectPaths(e.referencedArgumentObjectPaths)),this.context._pendingRequest.addAction(e),this.context._pendingRequest.addReferencedObjectPath(this._objectPath),this.context._pendingRequest.addReferencedObjectPaths(e.referencedArgumentObjectPaths),this.context._pendingRequest.addActionResultHandler(e,t),u._createPromiseFromResult(null)},n.prototype._handleResult=function(e){this._isNull=X.isNullOrUndefined(e),this.context.trackedObjects._autoTrackIfNecessaryWhenHandleObjectResultValue(this,e)},n.prototype._handleIdResult=function(e){this._isNull=X.isNullOrUndefined(e),X.fixObjectPathIfNecessary(this,e),this.context.trackedObjects._autoTrackIfNecessaryWhenHandleObjectResultValue(this,e)},n.prototype._handleRetrieveResult=function(e,t){this._handleIdResult(e)},n.prototype._recursivelySet=function(e,t,i,a,c){var l=e instanceof n,p=e;if(l){if(Object.getPrototypeOf(this)!==Object.getPrototypeOf(e))throw r.RuntimeError._createInvalidArgError({argumentName:"properties",errorLocation:this._className+".set"});e=JSON.parse(JSON.stringify(e))}try{for(var f,h=0;h<i.length;h++)f=i[h],e.hasOwnProperty(f)&&"undefined"!==typeof e[f]&&(this[f]=e[f]);for(h=0;h<a.length;h++)if(f=a[h],e.hasOwnProperty(f)&&"undefined"!==typeof e[f]){var d=l?p[f]:e[f];this[f].set(d,t)}var m=!l;t&&!X.isNullOrUndefined(m)&&(m=t.throwOnReadOnly);for(h=0;h<c.length;h++)if(f=c[h],e.hasOwnProperty(f)&&"undefined"!==typeof e[f]&&m)throw new r.RuntimeError({code:o.invalidArgument,httpStatusCode:400,message:u._getResourceString(Y.cannotApplyPropertyThroughSetMethod,f),debugInfo:{errorLocation:f}});for(f in e)if(i.indexOf(f)<0&&a.indexOf(f)<0){var y=Object.getOwnPropertyDescriptor(Object.getPrototypeOf(this),f);if(!y)throw new r.RuntimeError({code:o.invalidArgument,httpStatusCode:400,message:u._getResourceString(I.propertyDoesNotExist,f),debugInfo:{errorLocation:f}});if(m&&!y.set)throw new r.RuntimeError({code:o.invalidArgument,httpStatusCode:400,message:u._getResourceString(I.attemptingToSetReadOnlyProperty,f),debugInfo:{errorLocation:f}})}}catch(e){throw new r.RuntimeError({code:o.invalidArgument,httpStatusCode:400,message:u._getResourceString(s.invalidArgument,"properties"),debugInfo:{errorLocation:this._className+".set"},innerError:e})}},n}(f);e.ClientObject=F;var k=function(){function e(e){this.m_session=e}return e.prototype.executeAsync=function(e,t,n){var r={url:a.processQuery,method:"POST",headers:n.Headers,body:n.Body},o={id:i.nextId(),type:1,flags:t,message:r};return u.log(JSON.stringify(o)),this.m_session.sendMessageToHost(o).then((function(e){u.log("Received response: "+JSON.stringify(e));var t,n=e.message;if(200===n.statusCode)t={HttpStatusCode:n.statusCode,ErrorCode:null,ErrorMessage:null,Headers:n.headers,Body:u._parseResponseBody(n)};else{u.log("Error Response:"+n.body);var r=u._parseErrorResponse(n);t={HttpStatusCode:n.statusCode,ErrorCode:r.errorCode,ErrorMessage:r.errorMessage,Headers:n.headers,Body:null}}return t}))},e}(),D=function(e){function t(t){var n=e.call(this)||this;return n.m_bridge=t,n.m_bridge.addHostMessageHandler((function(e){3===e.type&&J.getGenericEventRegistration()._handleRichApiMessage(e.message)})),n}return __extends(t,e),t.getInstanceIfHostBridgeInited=function(){return i.instance?((u.isNullOrUndefined(t.s_instance)||t.s_instance.m_bridge!==i.instance)&&(t.s_instance=new t(i.instance)),t.s_instance):null},t.prototype._resolveRequestUrlAndHeaderInfo=function(){return u._createPromiseFromResult(null)},t.prototype._createRequestExecutorOrNull=function(){return u.log("NativeBridgeSession::CreateRequestExecutor"),new k(this)},Object.defineProperty(t.prototype,"eventRegistration",{get:function(){return J.getGenericEventRegistration()},enumerable:!0,configurable:!0}),t.prototype.sendMessageToHost=function(e){return this.m_bridge.sendMessageToHostAndExpectResponse(e)},t}(t);e.HostBridgeSession=D;var M=function(n){function i(e){var o=n.call(this)||this;if(o.m_customRequestHeaders={},o.m_batchMode=0,o._onRunFinishedNotifiers=[],t._overrideSession)o.m_requestUrlAndHeaderInfoResolver=t._overrideSession;else if((X.isNullOrUndefined(e)||"string"===typeof e&&0===e.length)&&((e=i.defaultRequestUrlAndHeaders)||(e={url:a.localDocument,headers:{}})),"string"===typeof e)o.m_requestUrlAndHeaderInfo={url:e,headers:{}};else if(i.isRequestUrlAndHeaderInfoResolver(e))o.m_requestUrlAndHeaderInfoResolver=e;else{if(!i.isRequestUrlAndHeaderInfo(e))throw r.RuntimeError._createInvalidArgError({argumentName:"url"});var s=e;o.m_requestUrlAndHeaderInfo={url:s.url,headers:{}},u._copyHeaders(s.headers,o.m_requestUrlAndHeaderInfo.headers)}return!o.m_requestUrlAndHeaderInfoResolver&&o.m_requestUrlAndHeaderInfo&&u._isLocalDocumentUrl(o.m_requestUrlAndHeaderInfo.url)&&D.getInstanceIfHostBridgeInited()&&(o.m_requestUrlAndHeaderInfo=null,o.m_requestUrlAndHeaderInfoResolver=D.getInstanceIfHostBridgeInited()),o.m_requestUrlAndHeaderInfoResolver instanceof t&&(o.m_session=o.m_requestUrlAndHeaderInfoResolver),o._processingResult=!1,o._customData=H.iterativeExecutor,o.sync=o.sync.bind(o),o}return __extends(i,n),Object.defineProperty(i.prototype,"session",{get:function(){return this.m_session},enumerable:!0,configurable:!0}),Object.defineProperty(i.prototype,"eventRegistration",{get:function(){return this.m_session?this.m_session.eventRegistration:r.officeJsEventRegistration},enumerable:!0,configurable:!0}),Object.defineProperty(i.prototype,"_url",{get:function(){return this.m_requestUrlAndHeaderInfo?this.m_requestUrlAndHeaderInfo.url:null},enumerable:!0,configurable:!0}),Object.defineProperty(i.prototype,"_pendingRequest",{get:function(){return null==this.m_pendingRequest&&(this.m_pendingRequest=new B(this)),this.m_pendingRequest},enumerable:!0,configurable:!0}),Object.defineProperty(i.prototype,"debugInfo",{get:function(){return{pendingStatements:new $(this._rootObjectPropertyName,this._pendingRequest._objectPaths,this._pendingRequest._actions,e._internalConfig.showDisposeInfoInDebugInfo).process()}},enumerable:!0,configurable:!0}),Object.defineProperty(i.prototype,"trackedObjects",{get:function(){return this.m_trackedObjects||(this.m_trackedObjects=new Z(this)),this.m_trackedObjects},enumerable:!0,configurable:!0}),Object.defineProperty(i.prototype,"requestHeaders",{get:function(){return this.m_customRequestHeaders},enumerable:!0,configurable:!0}),Object.defineProperty(i.prototype,"batchMode",{get:function(){return this.m_batchMode},enumerable:!0,configurable:!0}),i.prototype.ensureInProgressBatchIfBatchMode=function(){if(1===this.m_batchMode&&!this.m_explicitBatchInProgress)throw X.createRuntimeError(o.generalException,u._getResourceString(Y.notInsideBatch),null)},i.prototype.load=function(e,t){X.validateContext(this,e);var n=i._parseQueryOption(t);p.createQueryAction(this,e,n,e)},i.prototype.loadRecursive=function(e,t,n){if(!X.isPlainJsonObject(t))throw r.RuntimeError._createInvalidArgError({argumentName:"options"});var o={};for(var s in t)o[s]=i._parseQueryOption(t[s]);var a=U.createRecursiveQueryAction(this,e,{Queries:o,MaxDepth:n});this._pendingRequest.addActionResultHandler(a,e)},i.prototype.trace=function(e){U.createTraceAction(this,e,!0)},i.prototype._processOfficeJsErrorResponse=function(e,t){},i.prototype.ensureRequestUrlAndHeaderInfo=function(){var e=this;return X._createPromiseFromResult(null).then((function(){if(!e.m_requestUrlAndHeaderInfo)return e.m_requestUrlAndHeaderInfoResolver._resolveRequestUrlAndHeaderInfo().then((function(t){if(e.m_requestUrlAndHeaderInfo=t,e.m_requestUrlAndHeaderInfo||(e.m_requestUrlAndHeaderInfo={url:a.localDocument,headers:{}}),X.isNullOrEmptyString(e.m_requestUrlAndHeaderInfo.url)&&(e.m_requestUrlAndHeaderInfo.url=a.localDocument),e.m_requestUrlAndHeaderInfo.headers||(e.m_requestUrlAndHeaderInfo.headers={}),"function"===typeof e.m_requestUrlAndHeaderInfoResolver._createRequestExecutorOrNull){var n=e.m_requestUrlAndHeaderInfoResolver._createRequestExecutorOrNull();n&&(e._requestExecutor=n)}}))}))},i.prototype.syncPrivateMain=function(){var e=this;return this.ensureRequestUrlAndHeaderInfo().then((function(){var t=e._pendingRequest;return e.m_pendingRequest=null,e.processPreSyncPromises(t).then((function(){return e.syncPrivate(t)}))}))},i.prototype.syncPrivate=function(e){var t=this;if(l.isMock())return u._createPromiseFromResult(null);if(!e.hasActions)return this.processPendingEventHandlers(e);var n=e.buildRequestMessageBodyAndRequestFlags(),i=n.body,o=n.flags;this._requestFlagModifier&&(o|=this._requestFlagModifier),this._requestExecutor||(u._isLocalDocumentUrl(this.m_requestUrlAndHeaderInfo.url)?this._requestExecutor=new K(this):this._requestExecutor=new v);var s=this._requestExecutor,a={};u._copyHeaders(this.m_requestUrlAndHeaderInfo.headers,a),u._copyHeaders(this.m_customRequestHeaders,a),delete this.m_customRequestHeaders[H.officeScriptEventId];var c={Url:this.m_requestUrlAndHeaderInfo.url,Headers:a,Body:i};e.invalidatePendingInvalidObjectPaths();var p=null,f=null;return this._lastSyncStart="undefined"===typeof performance?0:performance.now(),this._lastRequestFlags=o,s.executeAsync(this._customData,o,c).then((function(n){return t._lastSyncEnd="undefined"===typeof performance?0:performance.now(),p=t.processRequestExecutorResponseMessage(e,n),t.processPendingEventHandlers(e).catch((function(e){u.log("Error in processPendingEventHandlers"),u.log(JSON.stringify(e)),f=e}))})).then((function(){if(p)throw u.log("Throw error from response: "+JSON.stringify(p)),p;if(f){u.log("Throw error from ProcessEventHandler: "+JSON.stringify(f));var t=null;if(f instanceof r.RuntimeError)(t=f).traceMessages=e._responseTraceMessages;else{var n=null;n="string"===typeof f?f:f.message,X.isNullOrEmptyString(n)&&(n=u._getResourceString(Y.cannotRegisterEvent)),t=new r.RuntimeError({code:T.cannotRegisterEvent,httpStatusCode:400,message:n,traceMessages:e._responseTraceMessages})}throw t}}))},i.prototype.processRequestExecutorResponseMessage=function(t,n){n.Body&&n.Body.TraceIds&&t._setResponseTraceIds(n.Body.TraceIds);var i=t._responseTraceMessages,o=null;if(n.Body){if(n.Body.Error&&n.Body.Error.ActionIndex>=0){var s=new $(this._rootObjectPropertyName,t._objectPaths,t._actions,!1,!0),a=s.processForDebugStatementInfo(n.Body.Error.ActionIndex);o={statement:a.statement,surroundingStatements:a.surroundingStatements,fullStatements:["Please enable config.extendedErrorLogging to see full statements."]},e.config.extendedErrorLogging&&(s=new $(this._rootObjectPropertyName,t._objectPaths,t._actions,!1,!1),o.fullStatements=s.process())}var c=null;if(n.Body.Results?c=n.Body.Results:n.Body.ProcessedResults&&n.Body.ProcessedResults.Results&&(c=n.Body.ProcessedResults.Results),c){this._processingResult=!0;try{t.processResponse(c)}finally{this._processingResult=!1}}}if(!X.isNullOrEmptyString(n.ErrorCode))return new r.RuntimeError({code:n.ErrorCode,httpStatusCode:n.HttpStatusCode,message:n.ErrorMessage,traceMessages:i});if(n.Body&&n.Body.Error){var u={errorLocation:n.Body.Error.Location};return o&&(u.statement=o.statement,u.surroundingStatements=o.surroundingStatements,u.fullStatements=o.fullStatements),new r.RuntimeError({code:n.Body.Error.Code,httpStatusCode:n.Body.Error.HttpStatusCode,message:n.Body.Error.Message,traceMessages:i,debugInfo:u})}return null},i.prototype.processPendingEventHandlers=function(e){for(var t=X._createPromiseFromResult(null),n=0;n<e._pendingProcessEventHandlers.length;n++){var r=e._pendingProcessEventHandlers[n];t=t.then(this.createProcessOneEventHandlersFunc(r,e))}return t},i.prototype.createProcessOneEventHandlersFunc=function(e,t){return function(){return e._processRegistration(t)}},i.prototype.processPreSyncPromises=function(e){for(var t=X._createPromiseFromResult(null),n=0;n<e._preSyncPromises.length;n++){var r=e._preSyncPromises[n];t=t.then(this.createProcessOneProSyncFunc(r))}return t},i.prototype.createProcessOneProSyncFunc=function(e){return function(){return e}},i.prototype.sync=function(e){return l.isMock()?u._createPromiseFromResult(e):this.syncPrivateMain().then((function(){return e}))},i.prototype.batch=function(e){var t=this;if(1!==this.m_batchMode)return u._createPromiseFromException(X.createRuntimeError(o.generalException,null,null));if(this.m_explicitBatchInProgress)return u._createPromiseFromException(X.createRuntimeError(o.generalException,u._getResourceString(Y.pendingBatchInProgress),null));if(X.isNullOrUndefined(e))return X._createPromiseFromResult(null);this.m_explicitBatchInProgress=!0;var n,r,i,s=this.m_pendingRequest;this.m_pendingRequest=new B(this);try{n=e(this._rootObject,this)}catch(e){return this.m_explicitBatchInProgress=!1,this.m_pendingRequest=s,u._createPromiseFromException(e)}return"object"===typeof n&&n&&"function"===typeof n.then?i=X._createPromiseFromResult(null).then((function(){return n})).then((function(e){return t.m_explicitBatchInProgress=!1,r=t.m_pendingRequest,t.m_pendingRequest=s,e})).catch((function(e){return t.m_explicitBatchInProgress=!1,r=t.m_pendingRequest,t.m_pendingRequest=s,u._createPromiseFromException(e)})):(this.m_explicitBatchInProgress=!1,r=this.m_pendingRequest,this.m_pendingRequest=s,i=X._createPromiseFromResult(n)),i.then((function(e){return t.ensureRequestUrlAndHeaderInfo().then((function(){return t.syncPrivate(r)})).then((function(){return e}))}))},i._run=function(e,t,n,r,o,s){return void 0===n&&(n=3),void 0===r&&(r=5e3),i._runCommon("run",null,e,0,t,n,r,null,o,s)},i.isValidRequestInfo=function(e){return"string"===typeof e||i.isRequestUrlAndHeaderInfo(e)||i.isRequestUrlAndHeaderInfoResolver(e)},i.isRequestUrlAndHeaderInfo=function(e){return"object"===typeof e&&null!==e&&Object.getPrototypeOf(e)===Object.getPrototypeOf({})&&!X.isNullOrUndefined(e.url)},i.isRequestUrlAndHeaderInfoResolver=function(e){return"object"===typeof e&&null!==e&&"function"===typeof e._resolveRequestUrlAndHeaderInfo},i._runBatch=function(e,t,n,r,o,s,a,c){return void 0===o&&(o=3),void 0===s&&(s=5e3),i._runBatchCommon(0,e,t,n,o,s,r,a,c)},i._runExplicitBatch=function(e,t,n,r,o,s,a,c){return void 0===o&&(o=3),void 0===s&&(s=5e3),i._runBatchCommon(1,e,t,n,o,s,r,a,c)},i._runBatchCommon=function(e,t,n,r,o,s,a,c,u){var l,p;void 0===o&&(o=3),void 0===s&&(s=5e3);var f=null,h=null,d=0,m=null;if(n.length>0)if(i.isValidRequestInfo(n[0]))f=n[0],d=1;else if(X.isPlainJsonObject(n[0])){if(null!=(f=(m=n[0]).session)&&!i.isValidRequestInfo(f))return i.createErrorPromise(t);h=m.previousObjects,d=1}if(n.length==d+1)p=n[d+0];else{if(null!=m||n.length!=d+2)return i.createErrorPromise(t);h=n[d+0],p=n[d+1]}if(null!=h)if(h instanceof F)l=function(){return h.context};else if(h instanceof i)l=function(){return h};else{if(!Array.isArray(h))return i.createErrorPromise(t);var y=h;if(0==y.length)return i.createErrorPromise(t);for(var g=0;g<y.length;g++){if(!(y[g]instanceof F))return i.createErrorPromise(t);if(y[g].context!=y[0].context)return i.createErrorPromise(t,Y.invalidRequestContext)}l=function(){return y[0].context}}else l=r;var b=null;return a&&(b=function(e){return a(m||{},e)}),i._runCommon(t,f,l,e,p,o,s,b,c,u)},i.createErrorPromise=function(e,t){return void 0===t&&(t=s.invalidArgument),u._createPromiseFromException(X.createRuntimeError(t,u._getResourceString(t),e))},i._runCommon=function(n,r,o,s,a,c,l,p,f,h){t._overrideSession&&(r=t._overrideSession);var d,m,y,g=u.createPromise((function(e,t){e()})),b=!1;return g.then((function(){if((d=o(r))._autoCleanup)return new e.Promise((function(e,t){d._onRunFinishedNotifiers.push((function(){d._autoCleanup=!0,e()}))}));d._autoCleanup=!0})).then((function(){return"function"!==typeof a?i.createErrorPromise(n):(y=d.m_batchMode,d.m_batchMode=s,p&&p(d),e=a(1==s?d.batch.bind(d):d),(X.isNullOrUndefined(e)||"function"!==typeof e.then)&&X.throwError(Y.runMustReturnPromise),e);var e})).then((function(e){return 1===s?e:d.sync(e)})).then((function(e){b=!0,m=e})).catch((function(e){m=e})).then((function(){var e=d.trackedObjects._retrieveAndClearAutoCleanupList();for(var t in d._autoCleanup=!1,d.m_batchMode=y,e)e[t]._objectPath.isValid=!1;var n=0;if(X._synchronousCleanup||i.isRequestUrlAndHeaderInfoResolver(r))return o();function o(){n++;var t=d.m_pendingRequest,r=d.m_batchMode,i=new B(d);d.m_pendingRequest=i,d.m_batchMode=0;try{for(var s in e)d.trackedObjects.remove(e[s])}finally{d.m_batchMode=r,d.m_pendingRequest=t}return d.syncPrivate(i).then((function(){f&&f(n)})).catch((function(){h&&h(n),n<c&&setTimeout((function(){o()}),l)}))}o()})).then((function(){d._onRunFinishedNotifiers&&d._onRunFinishedNotifiers.length>0&&d._onRunFinishedNotifiers.shift()();if(b)return m;throw m}))},i}(m);e.ClientRequestContext=M;var L=function(){function e(e,t){this.m_proxy=e,this.m_shouldPolyfill=t;var n=e[H.scalarPropertyNames],r=e[H.navigationPropertyNames],i=e[H.className],o=e[H.isCollection];if(n)for(var s=0;s<n.length;s++)X.definePropertyThrowUnloadedException(this,i,n[s]);if(r)for(s=0;s<r.length;s++)X.definePropertyThrowUnloadedException(this,i,r[s]);o&&X.definePropertyThrowUnloadedException(this,i,H.itemsLowerCase)}return Object.defineProperty(e.prototype,"$proxy",{get:function(){return this.m_proxy},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"$isNullObject",{get:function(){if(!this.m_isLoaded)throw new r.RuntimeError({code:T.valueNotLoaded,httpStatusCode:400,message:u._getResourceString(Y.valueNotLoaded),debugInfo:{errorLocation:"retrieveResult.$isNullObject"}});return this.m_isNullObject},enumerable:!0,configurable:!0}),e.prototype.toJSON=function(){if(this.m_isLoaded)return this.m_isNullObject?null:(X.isUndefined(this.m_json)&&(this.m_json=X.purifyJson(this.m_value)),this.m_json)},e.prototype.toString=function(){return JSON.stringify(this.toJSON())},e.prototype._handleResult=function(e){this.m_isLoaded=!0,null===e||"object"===typeof e&&e&&e._IsNull?(this.m_isNullObject=!0,e=null):this.m_isNullObject=!1,this.m_shouldPolyfill&&(e=X.changePropertyNameToCamelLowerCase(e)),this.m_value=e,this.m_proxy._handleRetrieveResult(e,this)},e}(),H=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return __extends(t,e),t.getItemAt="GetItemAt",t.index="_Index",t.iterativeExecutor="IterativeExecutor",t.isTracked="_IsTracked",t.eventMessageCategory=65536,t.eventWorkbookId="Workbook",t.eventSourceRemote="Remote",t.proxy="$proxy",t.className="_className",t.isCollection="_isCollection",t.collectionPropertyPath="_collectionPropertyPath",t.objectPathInfoDoNotKeepReferenceFieldName="D",t.officeScriptEventId="X-OfficeScriptEventId",t.officeScriptFireRecordingEvent="X-OfficeScriptFireRecordingEvent",t}(O);e.Constants=H;var B=function(e){function t(t){var n=e.call(this,t)||this;return n.m_context=t,n.m_pendingProcessEventHandlers=[],n.m_pendingEventHandlerActions={},n.m_traceInfos={},n.m_responseTraceIds={},n.m_responseTraceMessages=[],n}return __extends(t,e),Object.defineProperty(t.prototype,"traceInfos",{get:function(){return this.m_traceInfos},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_responseTraceMessages",{get:function(){return this.m_responseTraceMessages},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_responseTraceIds",{get:function(){return this.m_responseTraceIds},enumerable:!0,configurable:!0}),t.prototype._setResponseTraceIds=function(e){if(e)for(var t=0;t<e.length;t++){var n=e[t];this.m_responseTraceIds[n]=n;var r=this.m_traceInfos[n];u.isNullOrUndefined(r)||this.m_responseTraceMessages.push(r)}},t.prototype.addTrace=function(e,t){this.m_traceInfos[e]=t},t.prototype._addPendingEventHandlerAction=function(e,t){this.m_pendingEventHandlerActions[e._id]||(this.m_pendingEventHandlerActions[e._id]=[],this.m_pendingProcessEventHandlers.push(e)),this.m_pendingEventHandlerActions[e._id].push(t)},Object.defineProperty(t.prototype,"_pendingProcessEventHandlers",{get:function(){return this.m_pendingProcessEventHandlers},enumerable:!0,configurable:!0}),t.prototype._getPendingEventHandlerActions=function(e){return this.m_pendingEventHandlerActions[e._id]},t}(g);e.ClientRequest=B;var q=function(){function e(e,t,n,r){var i=this;this.m_id=e._nextId(),this.m_context=e,this.m_name=n,this.m_handlers=[],this.m_registered=!1,this.m_eventInfo=r,this.m_callback=function(e){i.m_eventInfo.eventArgsTransformFunc(e).then((function(e){return i.fireEvent(e)}))}}return Object.defineProperty(e.prototype,"_registered",{get:function(){return this.m_registered},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"_id",{get:function(){return this.m_id},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"_handlers",{get:function(){return this.m_handlers},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"_context",{get:function(){return this.m_context},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"_callback",{get:function(){return this.m_callback},enumerable:!0,configurable:!0}),e.prototype.add=function(e){var t=U.createTraceAction(this.m_context,null,!1);return this.m_context._pendingRequest._addPendingEventHandlerAction(this,{id:t.actionInfo.Id,handler:e,operation:0}),new V(this.m_context,this,e)},e.prototype.remove=function(e){var t=U.createTraceAction(this.m_context,null,!1);this.m_context._pendingRequest._addPendingEventHandlerAction(this,{id:t.actionInfo.Id,handler:e,operation:1})},e.prototype.removeAll=function(){var e=U.createTraceAction(this.m_context,null,!1);this.m_context._pendingRequest._addPendingEventHandlerAction(this,{id:e.actionInfo.Id,handler:null,operation:2})},e.prototype._processRegistration=function(e){var t=this,n=u._createPromiseFromResult(null),r=e._getPendingEventHandlerActions(this);if(!r)return n;for(var i=[],o=0;o<this.m_handlers.length;o++)i.push(this.m_handlers[o]);var s=!1;for(o=0;o<r.length;o++)if(e._responseTraceIds[r[o].id])switch(s=!0,r[o].operation){case 0:i.push(r[o].handler);break;case 1:for(var a=i.length-1;a>=0;a--)if(i[a]===r[o].handler){i.splice(a,1);break}break;case 2:i=[]}return s&&(!this.m_registered&&i.length>0?n=n.then((function(){return t.m_eventInfo.registerFunc(t.m_callback)})).then((function(){return t.m_registered=!0})):this.m_registered&&0==i.length&&(n=n.then((function(){return t.m_eventInfo.unregisterFunc(t.m_callback)})).catch((function(e){u.log("Error when unregister event: "+JSON.stringify(e))})).then((function(){return t.m_registered=!1}))),n=n.then((function(){return t.m_handlers=i}))),n},e.prototype.fireEvent=function(e){for(var t=[],n=0;n<this.m_handlers.length;n++){var r=this.m_handlers[n],i=u._createPromiseFromResult(null).then(this.createFireOneEventHandlerFunc(r,e)).catch((function(e){u.log("Error when invoke handler: "+JSON.stringify(e))}));t.push(i)}u.Promise.all(t)},e.prototype.createFireOneEventHandlerFunc=function(e,t){return function(){return e(t)}},e}();e.EventHandlers=q;var V=function(){function e(e,t,n){this.m_context=e,this.m_allHandlers=t,this.m_handler=n}return Object.defineProperty(e.prototype,"context",{get:function(){return this.m_context},enumerable:!0,configurable:!0}),e.prototype.remove=function(){this.m_allHandlers&&this.m_handler&&(this.m_allHandlers.remove(this.m_handler),this.m_allHandlers=null,this.m_handler=null)},e}();e.EventHandlerResult=V,function(e){var t=function(){function t(){}return t.prototype.register=function(t,n,r){switch(t){case 4:return X.promisify((function(e){return Office.context.document.bindings.getByIdAsync(n,e)})).then((function(e){return X.promisify((function(t){return e.addHandlerAsync(Office.EventType.BindingDataChanged,r,t)}))}));case 3:return X.promisify((function(e){return Office.context.document.bindings.getByIdAsync(n,e)})).then((function(e){return X.promisify((function(t){return e.addHandlerAsync(Office.EventType.BindingSelectionChanged,r,t)}))}));case 2:return X.promisify((function(e){return Office.context.document.addHandlerAsync(Office.EventType.DocumentSelectionChanged,r,e)}));case 1:return X.promisify((function(e){return Office.context.document.settings.addHandlerAsync(Office.EventType.SettingsChanged,r,e)}));case 5:return OSF.DDA.RichApi.richApiMessageManager.register(r);case 13:return X.promisify((function(e){return Office.context.document.addHandlerAsync(Office.EventType.ObjectDeleted,r,{id:n},e)}));case 14:return X.promisify((function(e){return Office.context.document.addHandlerAsync(Office.EventType.ObjectSelectionChanged,r,{id:n},e)}));case 15:return X.promisify((function(e){return Office.context.document.addHandlerAsync(Office.EventType.ObjectDataChanged,r,{id:n},e)}));case 16:return X.promisify((function(e){return Office.context.document.addHandlerAsync(Office.EventType.ContentControlAdded,r,{id:n},e)}));default:throw e.RuntimeError._createInvalidArgError({argumentName:"eventId"})}},t.prototype.unregister=function(t,n,r){switch(t){case 4:return X.promisify((function(e){return Office.context.document.bindings.getByIdAsync(n,e)})).then((function(e){return X.promisify((function(t){return e.removeHandlerAsync(Office.EventType.BindingDataChanged,{handler:r},t)}))}));case 3:return X.promisify((function(e){return Office.context.document.bindings.getByIdAsync(n,e)})).then((function(e){return X.promisify((function(t){return e.removeHandlerAsync(Office.EventType.BindingSelectionChanged,{handler:r},t)}))}));case 2:return X.promisify((function(e){return Office.context.document.removeHandlerAsync(Office.EventType.DocumentSelectionChanged,{handler:r},e)}));case 1:return X.promisify((function(e){return Office.context.document.settings.removeHandlerAsync(Office.EventType.SettingsChanged,{handler:r},e)}));case 5:return X.promisify((function(e){return OSF.DDA.RichApi.richApiMessageManager.removeHandlerAsync("richApiMessage",{handler:r},e)}));case 13:return X.promisify((function(e){return Office.context.document.removeHandlerAsync(Office.EventType.ObjectDeleted,{id:n,handler:r},e)}));case 14:return X.promisify((function(e){return Office.context.document.removeHandlerAsync(Office.EventType.ObjectSelectionChanged,{id:n,handler:r},e)}));case 15:return X.promisify((function(e){return Office.context.document.removeHandlerAsync(Office.EventType.ObjectDataChanged,{id:n,handler:r},e)}));case 16:return X.promisify((function(e){return Office.context.document.removeHandlerAsync(Office.EventType.ContentControlAdded,{id:n,handler:r},e)}));default:throw e.RuntimeError._createInvalidArgError({argumentName:"eventId"})}},t}();e.officeJsEventRegistration=new t}(r=e._Internal||(e._Internal={}));var G=function(){function e(e,t){this.m_handlersByEventByTarget={},this.m_registerEventImpl=e,this.m_unregisterEventImpl=t}return e.getTargetIdOrDefault=function(e){return X.isNullOrUndefined(e)?"":e},e.prototype.getHandlers=function(t,n){n=e.getTargetIdOrDefault(n);var r=this.m_handlersByEventByTarget[t];r||(r={},this.m_handlersByEventByTarget[t]=r);var i=r[n];return i||(i=[],r[n]=i),i},e.prototype.callHandlers=function(e,t,n){for(var r=this.getHandlers(e,t),i=0;i<r.length;i++)r[i](n)},e.prototype.hasHandlers=function(e,t){return this.getHandlers(e,t).length>0},e.prototype.register=function(e,t,n){if(!n)throw r.RuntimeError._createInvalidArgError({argumentName:"handler"});var i=this.getHandlers(e,t);return i.push(n),1===i.length?this.m_registerEventImpl(e,t):X._createPromiseFromResult(null)},e.prototype.unregister=function(e,t,n){if(!n)throw r.RuntimeError._createInvalidArgError({argumentName:"handler"});for(var i=this.getHandlers(e,t),o=i.length-1;o>=0;o--)if(i[o]===n){i.splice(o,1);break}return 0===i.length?this.m_unregisterEventImpl(e,t):X._createPromiseFromResult(null)},e}();e.EventRegistration=G;var J=function(){function e(){this.m_eventRegistration=new G(this._registerEventImpl.bind(this),this._unregisterEventImpl.bind(this)),this.m_richApiMessageHandler=this._handleRichApiMessage.bind(this)}return e.prototype.ready=function(){var t=this;return this.m_ready||(e._testReadyImpl?this.m_ready=e._testReadyImpl().then((function(){t.m_isReady=!0})):i.instance?this.m_ready=X._createPromiseFromResult(null).then((function(){t.m_isReady=!0})):this.m_ready=r.officeJsEventRegistration.register(5,"",this.m_richApiMessageHandler).then((function(){t.m_isReady=!0}))),this.m_ready},Object.defineProperty(e.prototype,"isReady",{get:function(){return this.m_isReady},enumerable:!0,configurable:!0}),e.prototype.register=function(e,t,n){var r=this;return this.ready().then((function(){return r.m_eventRegistration.register(e,t,n)}))},e.prototype.unregister=function(e,t,n){var r=this;return this.ready().then((function(){return r.m_eventRegistration.unregister(e,t,n)}))},e.prototype._registerEventImpl=function(e,t){return X._createPromiseFromResult(null)},e.prototype._unregisterEventImpl=function(e,t){return X._createPromiseFromResult(null)},e.prototype._handleRichApiMessage=function(e){if(e&&e.entries)for(var t=0;t<e.entries.length;t++){var n=e.entries[t];if(n.messageCategory==H.eventMessageCategory){u._logEnabled&&u.log(JSON.stringify(n));var r=n.messageType,i=n.targetId;if(this.m_eventRegistration.hasHandlers(r,i)){var o=JSON.parse(n.message);n.isRemoteOverride&&(o.source=H.eventSourceRemote),this.m_eventRegistration.callHandlers(r,i,o)}}}},e.getGenericEventRegistration=function(){return e.s_genericEventRegistration||(e.s_genericEventRegistration=new e),e.s_genericEventRegistration},e.richApiMessageEventCategory=65536,e}();e.GenericEventRegistration=J,e._testSetRichApiMessageReadyImpl=function(e){J._testReadyImpl=e},e._testTriggerRichApiMessageEvent=function(e){J.getGenericEventRegistration()._handleRichApiMessage(e)};var W=function(e){function t(t,n,r,i){var o=e.call(this,t,n,r,i)||this;return o.m_genericEventInfo=i,o}return __extends(t,e),t.prototype.add=function(e){var t=this;return 0==this._handlers.length&&this.m_genericEventInfo.registerFunc&&this.m_genericEventInfo.registerFunc(),J.getGenericEventRegistration().isReady||this._context._pendingRequest._addPreSyncPromise(J.getGenericEventRegistration().ready()),U.createTraceMarkerForCallback(this._context,(function(){t._handlers.push(e),1==t._handlers.length&&J.getGenericEventRegistration().register(t.m_genericEventInfo.eventType,t.m_genericEventInfo.getTargetIdFunc(),t._callback)})),new V(this._context,this,e)},t.prototype.remove=function(e){var t=this;1==this._handlers.length&&this.m_genericEventInfo.unregisterFunc&&this.m_genericEventInfo.unregisterFunc(),U.createTraceMarkerForCallback(this._context,(function(){for(var n=t._handlers,r=n.length-1;r>=0;r--)if(n[r]===e){n.splice(r,1);break}0==n.length&&J.getGenericEventRegistration().unregister(t.m_genericEventInfo.eventType,t.m_genericEventInfo.getTargetIdFunc(),t._callback)}))},t.prototype.removeAll=function(){},t}(q);e.GenericEventHandlers=W;var z=function(){function e(e){this.m_clientObject=e}return e.prototype._handleResult=function(e){this.m_clientObject._handleIdResult(e)},e}(),Q=function(){function e(){}return e.createGlobalObjectObjectPath=function(e){var t={Id:e._nextId(),ObjectPathType:1,Name:""};return new d(t,null,!1,!1,1,4)},e.createNewObjectObjectPath=function(e,t,n,r){var i={Id:e._nextId(),ObjectPathType:2,Name:t};return new d(i,null,n,!1,1,X._fixupApiFlags(r))},e.createPropertyObjectPath=function(e,t,n,r,i,o){var s={Id:e._nextId(),ObjectPathType:4,Name:n,ParentObjectPathId:t._objectPath.objectPathInfo.Id};return new d(s,t._objectPath,r,i,1,X._fixupApiFlags(o))},e.createIndexerObjectPath=function(e,t,n){var r={Id:e._nextId(),ObjectPathType:5,Name:"",ParentObjectPathId:t._objectPath.objectPathInfo.Id,ArgumentInfo:{}};return r.ArgumentInfo.Arguments=n,new d(r,t._objectPath,!1,!1,1,4)},e.createIndexerObjectPathUsingParentPath=function(e,t,n){var r={Id:e._nextId(),ObjectPathType:5,Name:"",ParentObjectPathId:t.objectPathInfo.Id,ArgumentInfo:{}};return r.ArgumentInfo.Arguments=n,new d(r,t,!1,!1,1,4)},e.createMethodObjectPath=function(e,t,n,r,i,o,s,a,c){var u={Id:e._nextId(),ObjectPathType:3,Name:n,ParentObjectPathId:t._objectPath.objectPathInfo.Id,ArgumentInfo:{}},l=X.setMethodArguments(e,u.ArgumentInfo,i),p=new d(u,t._objectPath,o,s,r,X._fixupApiFlags(c));return p.argumentObjectPaths=l,p.getByIdMethodName=a,p},e.createReferenceIdObjectPath=function(e,t){var n={Id:e._nextId(),ObjectPathType:6,Name:t,ArgumentInfo:{}};return new d(n,null,!1,!1,1,4)},e.createChildItemObjectPathUsingIndexerOrGetItemAt=function(t,n,r,i,o){var s=X.tryGetObjectIdFromLoadOrRetrieveResult(i);return t&&!X.isNullOrUndefined(s)?e.createChildItemObjectPathUsingIndexer(n,r,i):e.createChildItemObjectPathUsingGetItemAt(n,r,i,o)},e.createChildItemObjectPathUsingIndexer=function(e,t,n){var r=X.tryGetObjectIdFromLoadOrRetrieveResult(n),i=i={Id:e._nextId(),ObjectPathType:5,Name:"",ParentObjectPathId:t._objectPath.objectPathInfo.Id,ArgumentInfo:{}};return i.ArgumentInfo.Arguments=[r],new d(i,t._objectPath,!1,!1,1,4)},e.createChildItemObjectPathUsingGetItemAt=function(e,t,n,r){var i=n[H.index];i&&(r=i);var o={Id:e._nextId(),ObjectPathType:3,Name:H.getItemAt,ParentObjectPathId:t._objectPath.objectPathInfo.Id,ArgumentInfo:{}};return o.ArgumentInfo.Arguments=[r],new d(o,t._objectPath,!1,!1,1,4)},e}();e.ObjectPathFactory=Q;var K=function(){function t(e){this.m_context=e}return t.prototype.executeAsync=function(n,r,i){var o=this,s=c.buildMessageArrayForIRequestExecutor(n,r,i,t.SourceLibHeaderValue);return new e.Promise((function(e,t){OSF.DDA.RichApi.executeRichApiRequestAsync(s,(function(t){var n;u.log("Response:"),u.log(JSON.stringify(t)),"succeeded"==t.status?n=c.buildResponseOnSuccess(c.getResponseBody(t),c.getResponseHeaders(t)):(n=c.buildResponseOnError(t.error.code,t.error.message),o.m_context._processOfficeJsErrorResponse(t.error.code,n)),e(n)}))}))},t.SourceLibHeaderValue="officejs",t}(),Z=function(){function e(e){this._autoCleanupList={},this.m_context=e}return e.prototype.add=function(e){var t=this;Array.isArray(e)?e.forEach((function(e){return t._addCommon(e,!0)})):this._addCommon(e,!0)},e.prototype._autoAdd=function(e){this._addCommon(e,!1),this._autoCleanupList[e._objectPath.objectPathInfo.Id]=e},e.prototype._autoTrackIfNecessaryWhenHandleObjectResultValue=function(e,t){this.m_context._autoCleanup&&!e[H.isTracked]&&e!==this.m_context._rootObject&&t&&!X.isNullOrEmptyString(t[H.referenceId])&&(this._autoCleanupList[e._objectPath.objectPathInfo.Id]=e,e[H.isTracked]=!0)},e.prototype._addCommon=function(e,t){if(e[H.isTracked])t&&this.m_context._autoCleanup&&delete this._autoCleanupList[e._objectPath.objectPathInfo.Id];else{var n=e[H.referenceId];if(e._objectPath.objectPathInfo[H.objectPathInfoDoNotKeepReferenceFieldName])throw X.createRuntimeError(o.generalException,u._getResourceString(Y.objectIsUntracked),null);X.isNullOrEmptyString(n)&&e._KeepReference&&(e._KeepReference(),U.createInstantiateAction(this.m_context,e),t&&this.m_context._autoCleanup&&delete this._autoCleanupList[e._objectPath.objectPathInfo.Id],e[H.isTracked]=!0)}},e.prototype.remove=function(e){var t=this;Array.isArray(e)?e.forEach((function(e){return t._removeCommon(e)})):this._removeCommon(e)},e.prototype._removeCommon=function(e){e._objectPath.objectPathInfo[H.objectPathInfoDoNotKeepReferenceFieldName]=!0,e.context._pendingRequest._removeKeepReferenceAction(e._objectPath.objectPathInfo.Id);var t=e[H.referenceId];if(!X.isNullOrEmptyString(t)){var n=this.m_context._rootObject;n._RemoveReference&&n._RemoveReference(t)}delete e[H.isTracked]},e.prototype._retrieveAndClearAutoCleanupList=function(){var e=this._autoCleanupList;return this._autoCleanupList={},e},e}();e.TrackedObjects=Z;var $=function(){function t(e,t,n,r,i){e||(e="root"),this.m_globalObjName=e,this.m_referencedObjectPaths=t,this.m_actions=n,this.m_statements=[],this.m_variableNameForObjectPathMap={},this.m_variableNameToObjectPathMap={},this.m_declaredObjectPathMap={},this.m_showDispose=r,this.m_removePII=i}return t.prototype.process=function(){this.m_showDispose&&B._calculateLastUsedObjectPathIds(this.m_actions);for(var e=0;e<this.m_actions.length;e++)this.processOneAction(this.m_actions[e]);return this.m_statements},t.prototype.processForDebugStatementInfo=function(e){this.m_showDispose&&B._calculateLastUsedObjectPathIds(this.m_actions);this.m_statements=[];for(var t=-1,n=0;n<this.m_actions.length&&(this.processOneAction(this.m_actions[n]),e==n&&(t=this.m_statements.length-1),!(t>=0&&this.m_statements.length>t+5+1));n++);if(t<0)return null;var r=t-5;r<0&&(r=0);var i=t+1+5;i>this.m_statements.length&&(i=this.m_statements.length);var o=[];0!=r&&o.push("...");for(var s=r;s<t;s++)o.push(this.m_statements[s]);o.push("// >>>>>"),o.push(this.m_statements[t]),o.push("// <<<<<");for(var a=t+1;a<i;a++)o.push(this.m_statements[a]);return i<this.m_statements.length&&o.push("..."),{statement:this.m_statements[t],surroundingStatements:o}},t.prototype.processOneAction=function(e){switch(e.actionInfo.ActionType){case 1:this.processInstantiateAction(e);break;case 3:this.processMethodAction(e);break;case 2:this.processQueryAction(e);break;case 7:this.processQueryAsJsonAction(e);break;case 6:this.processRecursiveQueryAction(e);break;case 4:this.processSetPropertyAction(e);break;case 5:this.processTraceAction(e);break;case 8:this.processEnsureUnchangedAction(e);break;case 9:this.processUpdateAction(e)}},t.prototype.processInstantiateAction=function(e){var t=e.actionInfo.ObjectPathId,n=this.m_referencedObjectPaths[t],r=this.getObjVarName(t);if(this.m_declaredObjectPathMap[t]){i="// Instantiate {"+r+"}";i=this.appendDisposeCommentIfRelevant(i,e),this.m_statements.push(i)}else{var i="var "+r+" = "+this.buildObjectPathExpressionWithParent(n)+";";i=this.appendDisposeCommentIfRelevant(i,e),this.m_statements.push(i),this.m_declaredObjectPathMap[t]=r}},t.prototype.processMethodAction=function(t){var n=t.actionInfo.Name;if("_KeepReference"===n){if(!e._internalConfig.showInternalApiInDebugInfo)return;n="track"}var r=this.getObjVarName(t.actionInfo.ObjectPathId)+"."+X._toCamelLowerCase(n)+"("+this.buildArgumentsExpression(t.actionInfo.ArgumentInfo)+");";r=this.appendDisposeCommentIfRelevant(r,t),this.m_statements.push(r)},t.prototype.processQueryAction=function(e){var t=this.buildQueryExpression(e),n=this.getObjVarName(e.actionInfo.ObjectPathId)+".load("+t+");";n=this.appendDisposeCommentIfRelevant(n,e),this.m_statements.push(n)},t.prototype.processQueryAsJsonAction=function(e){var t=this.buildQueryExpression(e),n=this.getObjVarName(e.actionInfo.ObjectPathId)+".retrieve("+t+");";n=this.appendDisposeCommentIfRelevant(n,e),this.m_statements.push(n)},t.prototype.processRecursiveQueryAction=function(e){var t="";e.actionInfo.RecursiveQueryInfo&&(t=JSON.stringify(e.actionInfo.RecursiveQueryInfo));var n=this.getObjVarName(e.actionInfo.ObjectPathId)+".loadRecursive("+t+");";n=this.appendDisposeCommentIfRelevant(n,e),this.m_statements.push(n)},t.prototype.processSetPropertyAction=function(e){var t=this.getObjVarName(e.actionInfo.ObjectPathId)+"."+X._toCamelLowerCase(e.actionInfo.Name)+" = "+this.buildArgumentsExpression(e.actionInfo.ArgumentInfo)+";";t=this.appendDisposeCommentIfRelevant(t,e),this.m_statements.push(t)},t.prototype.processTraceAction=function(e){var t="context.trace();";t=this.appendDisposeCommentIfRelevant(t,e),this.m_statements.push(t)},t.prototype.processEnsureUnchangedAction=function(e){var t=this.getObjVarName(e.actionInfo.ObjectPathId)+".ensureUnchanged("+JSON.stringify(e.actionInfo.ObjectState)+");";t=this.appendDisposeCommentIfRelevant(t,e),this.m_statements.push(t)},t.prototype.processUpdateAction=function(e){var t=this.getObjVarName(e.actionInfo.ObjectPathId)+".update("+JSON.stringify(e.actionInfo.ObjectState)+");";t=this.appendDisposeCommentIfRelevant(t,e),this.m_statements.push(t)},t.prototype.appendDisposeCommentIfRelevant=function(e,t){var n=this;if(this.m_showDispose){var r=t.actionInfo.L;if(r&&r.length>0)return e+" // And then dispose {"+r.map((function(e){return n.getObjVarName(e)})).join(", ")+"}"}return e},t.prototype.buildQueryExpression=function(e){if(e.actionInfo.QueryInfo){var t={};return t.select=e.actionInfo.QueryInfo.Select,t.expand=e.actionInfo.QueryInfo.Expand,t.skip=e.actionInfo.QueryInfo.Skip,t.top=e.actionInfo.QueryInfo.Top,"undefined"===typeof t.top&&"undefined"===typeof t.skip&&"undefined"===typeof t.expand?"undefined"===typeof t.select?"":JSON.stringify(t.select):JSON.stringify(t)}return""},t.prototype.buildObjectPathExpressionWithParent=function(e){return(5==e.objectPathInfo.ObjectPathType||3==e.objectPathInfo.ObjectPathType||4==e.objectPathInfo.ObjectPathType)&&e.objectPathInfo.ParentObjectPathId?this.getObjVarName(e.objectPathInfo.ParentObjectPathId)+"."+this.buildObjectPathExpression(e):this.buildObjectPathExpression(e)},t.prototype.buildObjectPathExpression=function(e){var t=this.buildObjectPathInfoExpression(e.objectPathInfo),n=e.originalObjectPathInfo;return n&&(t=t+" /* originally "+this.buildObjectPathInfoExpression(n)+" */"),t},t.prototype.buildObjectPathInfoExpression=function(e){switch(e.ObjectPathType){case 1:return"context."+this.m_globalObjName;case 5:return"getItem("+this.buildArgumentsExpression(e.ArgumentInfo)+")";case 3:return X._toCamelLowerCase(e.Name)+"("+this.buildArgumentsExpression(e.ArgumentInfo)+")";case 2:return e.Name+".newObject()";case 7:return"null";case 4:return X._toCamelLowerCase(e.Name);case 6:return"context."+this.m_globalObjName+"._getObjectByReferenceId("+JSON.stringify(e.Name)+")"}},t.prototype.buildArgumentsExpression=function(e){var t="";if(!e.Arguments||0===e.Arguments.length)return t;if(this.m_removePII)return"undefined"===typeof e.Arguments[0]?t:"...";for(var n=0;n<e.Arguments.length;n++)n>0&&(t+=", "),t+=this.buildArgumentLiteral(e.Arguments[n],e.ReferencedObjectPathIds?e.ReferencedObjectPathIds[n]:null);return"undefined"===t&&(t=""),t},t.prototype.buildArgumentLiteral=function(e,t){return"number"==typeof e&&e===t?this.getObjVarName(t):JSON.stringify(e)},t.prototype.getObjVarNameBase=function(e){var t="v",n=this.m_referencedObjectPaths[e];if(n)switch(n.objectPathInfo.ObjectPathType){case 1:t=this.m_globalObjName;break;case 4:t=X._toCamelLowerCase(n.objectPathInfo.Name);break;case 3:var r=n.objectPathInfo.Name;r.length>3&&"Get"===r.substr(0,3)&&(r=r.substr(3)),t=X._toCamelLowerCase(r);break;case 5:var i=this.getObjVarNameBase(n.objectPathInfo.ParentObjectPathId);t="s"===i.charAt(i.length-1)?i.substr(0,i.length-1):i+"Item"}return t},t.prototype.getObjVarName=function(e){if(this.m_variableNameForObjectPathMap[e])return this.m_variableNameForObjectPathMap[e];var t=this.getObjVarNameBase(e);if(!this.m_variableNameToObjectPathMap[t])return this.m_variableNameForObjectPathMap[e]=t,this.m_variableNameToObjectPathMap[t]=e,t;for(var n=1;this.m_variableNameToObjectPathMap[t+n.toString()];)n++;return t+=n.toString(),this.m_variableNameForObjectPathMap[e]=t,this.m_variableNameToObjectPathMap[t]=e,t},t}(),Y=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return __extends(t,e),t.cannotRegisterEvent="CannotRegisterEvent",t.connectionFailureWithStatus="ConnectionFailureWithStatus",t.connectionFailureWithDetails="ConnectionFailureWithDetails",t.propertyNotLoaded="PropertyNotLoaded",t.runMustReturnPromise="RunMustReturnPromise",t.moreInfoInnerError="MoreInfoInnerError",t.cannotApplyPropertyThroughSetMethod="CannotApplyPropertyThroughSetMethod",t.invalidOperationInCellEditMode="InvalidOperationInCellEditMode",t.objectIsUntracked="ObjectIsUntracked",t.customFunctionDefintionMissing="CustomFunctionDefintionMissing",t.customFunctionImplementationMissing="CustomFunctionImplementationMissing",t.customFunctionNameContainsBadChars="CustomFunctionNameContainsBadChars",t.customFunctionNameCannotSplit="CustomFunctionNameCannotSplit",t.customFunctionUnexpectedNumberOfEntriesInResultBatch="CustomFunctionUnexpectedNumberOfEntriesInResultBatch",t.customFunctionCancellationHandlerMissing="CustomFunctionCancellationHandlerMissing",t.customFunctionInvalidFunction="CustomFunctionInvalidFunction",t.customFunctionInvalidFunctionMapping="CustomFunctionInvalidFunctionMapping",t.customFunctionWindowMissing="CustomFunctionWindowMissing",t.customFunctionDefintionMissingOnWindow="CustomFunctionDefintionMissingOnWindow",t.pendingBatchInProgress="PendingBatchInProgress",t.notInsideBatch="NotInsideBatch",t.cannotUpdateReadOnlyProperty="CannotUpdateReadOnlyProperty",t}(I);e.ResourceStrings=Y,u.addResourceStringValues({CannotRegisterEvent:"The event handler cannot be registered.",PropertyNotLoaded:"The property '{0}' is not available. Before reading the property's value, call the load method on the containing object and call \"context.sync()\" on the associated request context.",RunMustReturnPromise:'The batch function passed to the ".run" method didn\'t return a promise. The function must return a promise, so that any automatically-tracked objects can be released at the completion of the batch operation. Typically, you return a promise by returning the response from "context.sync()".',InvalidOrTimedOutSessionMessage:"Your Office Online session has expired or is invalid. To continue, refresh the page.",InvalidOperationInCellEditMode:"Excel is in cell-editing mode. Please exit the edit mode by pressing ENTER or TAB or selecting another cell, and then try again.",CustomFunctionDefintionMissing:"A property with the name '{0}' that represents the function's definition must exist on Excel.Script.CustomFunctions.",CustomFunctionDefintionMissingOnWindow:"A property with the name '{0}' that represents the function's definition must exist on the window object.",CustomFunctionImplementationMissing:"The property with the name '{0}' on Excel.Script.CustomFunctions that represents the function's definition must contain a 'call' property that implements the function.",CustomFunctionNameContainsBadChars:"The function name may only contain letters, digits, underscores, and periods.",CustomFunctionNameCannotSplit:"The function name must contain a non-empty namespace and a non-empty short name.",CustomFunctionUnexpectedNumberOfEntriesInResultBatch:"The batching function returned a number of results that doesn't match the number of parameter value sets that were passed into it.",CustomFunctionCancellationHandlerMissing:"The cancellation handler onCanceled is missing in the function. The handler must be present as the function is defined as cancelable.",CustomFunctionInvalidFunction:"The property with the name '{0}' that represents the function's definition is not a valid function.",CustomFunctionInvalidFunctionMapping:"The property with the name '{0}' on CustomFunctionMappings that represents the function's definition is not a valid function.",CustomFunctionWindowMissing:"The window object was not found.",PendingBatchInProgress:"There is a pending batch in progress. The batch method may not be called inside another batch, or simultaneously with another batch.",NotInsideBatch:"Operations may not be invoked outside of a batch method.",CannotUpdateReadOnlyProperty:"The property '{0}' is read-only and it cannot be updated.",ObjectIsUntracked:"The object is untracked."});var X=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return __extends(n,t),n.fixObjectPathIfNecessary=function(e,t){e&&e._objectPath&&t&&e._objectPath.updateUsingObjectData(t,e)},n.load=function(e,t){return e.context.load(e,t),e},n.loadAndSync=function(e,t){return e.context.load(e,t),e.context.sync().then((function(){return e}))},n.retrieve=function(t,r){var i=e._internalConfig.alwaysPolyfillClientObjectRetrieveMethod;i||(i=!n.isSetSupported("RichApiRuntime","1.1"));var o=new L(t,i);return t._retrieve(r,o),o},n.retrieveAndSync=function(e,t){var r=n.retrieve(e,t);return e.context.sync().then((function(){return r}))},n.toJson=function(e,t,r,i){var o={};for(var s in t){"undefined"!==typeof(a=t[s])&&(o[s]=a)}for(var s in r){var a;"undefined"!==typeof(a=r[s])&&(a[n.fieldName_isCollection]&&"undefined"!==typeof a[n.fieldName_m__items]?o[s]=a.toJSON().items:o[s]=a.toJSON())}return i&&(o.items=i.map((function(e){return e.toJSON()}))),o},n.throwError=function(e,t,n){throw new r.RuntimeError({code:e,httpStatusCode:400,message:u._getResourceString(e,t),debugInfo:n?{errorLocation:n}:void 0})},n.createRuntimeError=function(e,t,n,i,o){return new r.RuntimeError({code:e,httpStatusCode:i,message:t,debugInfo:{errorLocation:n},data:o})},n.throwIfNotLoaded=function(e,t,r,i){if(!i&&u.isUndefined(t)&&e.charCodeAt(0)!=n.s_underscoreCharCode)throw n.createPropertyNotLoadedException(r,e)},n.createPropertyNotLoadedException=function(e,t){return new r.RuntimeError({code:T.propertyNotLoaded,httpStatusCode:400,message:u._getResourceString(Y.propertyNotLoaded,t),debugInfo:e?{errorLocation:e+"."+t}:void 0})},n.createCannotUpdateReadOnlyPropertyException=function(e,t){return new r.RuntimeError({code:T.cannotUpdateReadOnlyProperty,httpStatusCode:400,message:u._getResourceString(Y.cannotUpdateReadOnlyProperty,t),debugInfo:e?{errorLocation:e+"."+t}:void 0})},n.promisify=function(t){return new e.Promise((function(e,n){t((function(t){"failed"==t.status?n(t.error):e(t.value)}))}))},n._addActionResultHandler=function(e,t,n){e.context._pendingRequest.addActionResultHandler(t,n)},n._handleNavigationPropertyResults=function(e,t,n){for(var r=0;r<n.length-1;r+=2)u.isUndefined(t[n[r+1]])||e[n[r]]._handleResult(t[n[r+1]])},n._fixupApiFlags=function(e){return"boolean"===typeof e&&(e=e?1:0),e},n.definePropertyThrowUnloadedException=function(e,t,r){Object.defineProperty(e,r,{configurable:!0,enumerable:!0,get:function(){throw n.createPropertyNotLoadedException(t,r)},set:function(){throw n.createCannotUpdateReadOnlyPropertyException(t,r)}})},n.defineReadOnlyPropertyWithValue=function(e,t,r){Object.defineProperty(e,t,{configurable:!0,enumerable:!0,get:function(){return r},set:function(){throw n.createCannotUpdateReadOnlyPropertyException(null,t)}})},n.processRetrieveResult=function(e,t,r,i){if(!u.isNullOrUndefined(t))if(i){var o=t[H.itemsLowerCase];if(Array.isArray(o)){for(var s=[],a=0;a<o.length;a++){var c=i(o[a],a),l={};l[H.proxy]=c,c._handleRetrieveResult(o[a],l),s.push(l)}n.defineReadOnlyPropertyWithValue(r,H.itemsLowerCase,s)}}else{var p=e[H.scalarPropertyNames],f=e[H.navigationPropertyNames],h=e[H.className];if(p)for(a=0;a<p.length;a++){var d=t[m=p[a]];u.isUndefined(d)?n.definePropertyThrowUnloadedException(r,h,m):n.defineReadOnlyPropertyWithValue(r,m,d)}if(f)for(a=0;a<f.length;a++){var m;d=t[m=f[a]];if(u.isUndefined(d))n.definePropertyThrowUnloadedException(r,h,m);else{var y=e[m],g={};y._handleRetrieveResult(d,g),g[H.proxy]=y,Array.isArray(g[H.itemsLowerCase])&&(g=g[H.itemsLowerCase]),n.defineReadOnlyPropertyWithValue(r,m,g)}}}},n.setMockData=function(e,t,r,i){if(u.isNullOrUndefined(t))e._handleResult(t);else{if(e[H.scalarPropertyOriginalNames]){for(var o={},s=e[H.scalarPropertyOriginalNames],a=e[H.scalarPropertyNames],c=0;c<a.length;c++)"undefined"!==typeof t[a[c]]&&(o[s[c]]=t[a[c]]);e._handleResult(o)}if(e[H.navigationPropertyNames]){var l=e[H.navigationPropertyNames];for(c=0;c<l.length;c++)if("undefined"!==typeof t[l[c]]){var p=e[l[c]];p.setMockData&&p.setMockData(t[l[c]])}}if(e[H.isCollection]&&r){var f=Array.isArray(t)?t:t[H.itemsLowerCase];if(Array.isArray(f)){var h=[];for(c=0;c<f.length;c++){var d=r(f,c);n.setMockData(d,f[c]),h.push(d)}i(h)}}}},n.applyMixin=function(e,t){Object.getOwnPropertyNames(t.prototype).forEach((function(n){"constructor"!==n&&Object.defineProperty(e.prototype,n,Object.getOwnPropertyDescriptor(t.prototype,n))}))},n.fieldName_m__items="m__items",n.fieldName_isCollection="_isCollection",n._synchronousCleanup=!1,n.s_underscoreCharCode="_".charCodeAt(0),n}(P);e.Utility=X;var ee=function(){function e(){}return e.invokeMethod=function(e,t,n,r,i,o){var s=U.createMethodAction(e.context,e,t,n,r,i),a=new b(o);return X._addActionResultHandler(e,s,a),a},e.invokeEnsureUnchanged=function(e,t){U.createEnsureUnchangedAction(e.context,e,t)},e.invokeSetProperty=function(e,t,n,r){U.createSetPropertyAction(e.context,e,t,n,r)},e.createRootServiceObject=function(e,t){return new e(t,Q.createGlobalObjectObjectPath(t))},e.createObjectFromReferenceId=function(e,t,n){return new e(t,Q.createReferenceIdObjectPath(t,n))},e.createTopLevelServiceObject=function(e,t,n,r,i){return new e(t,Q.createNewObjectObjectPath(t,n,r,i))},e.createPropertyObject=function(e,t,n,r,i){var o=Q.createPropertyObjectPath(t.context,t,n,r,!1,i);return new e(t.context,o)},e.createIndexerObject=function(e,t,n){var r=Q.createIndexerObjectPath(t.context,t,n);return new e(t.context,r)},e.createMethodObject=function(e,t,n,r,i,o,s,a,c){var u=Q.createMethodObjectPath(t.context,t,n,r,i,o,s,a,c);return new e(t.context,u)},e.createChildItemObject=function(e,t,n,r,i){var o=Q.createChildItemObjectPathUsingIndexerOrGetItemAt(t,n.context,n,r,i);return new e(n.context,o)},e}();e.BatchApiHelper=ee;var te=function(){function t(e){if(this.m_namespaceMap={},this.m_namespace=e.metadata.name,this.m_targetNamespaceObject=e.targetNamespaceObject,this.m_namespaceMap[this.m_namespace]=e.targetNamespaceObject,e.namespaceMap)for(var t in e.namespaceMap)this.m_namespaceMap[t]=e.namespaceMap[t];this.m_defaultApiSetName=e.metadata.defaultApiSetName,this.m_hostName=e.metadata.hostName;var n=e.metadata;if(n.enumTypes)for(var r=0;r<n.enumTypes.length;r++)this.buildEnumType(n.enumTypes[r]);if(n.apiSets){for(r=0;r<n.apiSets.length;r++){var i=n.apiSets[r];Array.isArray(i)&&(n.apiSets[r]={version:i[0],name:i[1]||this.m_defaultApiSetName})}this.m_apiSets=n.apiSets}if(this.m_strings=n.strings,n.clientObjectTypes)for(r=0;r<n.clientObjectTypes.length;r++){i=n.clientObjectTypes[r];Array.isArray(i)&&(this.ensureArraySize(i,11),n.clientObjectTypes[r]={name:this.getString(i[0]),behaviorFlags:i[1],collectionPropertyPath:this.getString(i[6]),newObjectServerTypeFullName:this.getString(i[9]),newObjectApiFlags:i[10],childItemTypeFullName:this.getString(i[7]),scalarProperties:i[2],navigationProperties:i[3],scalarMethods:i[4],navigationMethods:i[5],events:i[8]}),this.buildClientObjectType(n.clientObjectTypes[r],e.fullyInitialize)}}return t.prototype.ensureArraySize=function(e,t){for(var n=t-e.length;n>0;)e.push(0),n--},t.prototype.getString=function(e){return"number"===typeof e?e>0?this.m_strings[e-1]:null:e},t.prototype.buildEnumType=function(e){var t;if(Array.isArray(e)){(t={name:e[0],fields:e[2]}).fields||(t.fields={});var n=e[1];if(Array.isArray(n))for(var r=0;r<n.length;r++)t.fields[n[r]]=this.toSimpleCamelUpperCase(n[r])}else t=e;this.m_targetNamespaceObject[t.name]=t.fields},t.prototype.buildClientObjectType=function(n,r){var i=this,o=function(t,r){F.apply(this,arguments),i.m_targetNamespaceObject[n.name]._typeInited||(i.buildPrototype(i.m_targetNamespaceObject[n.name],n),i.m_targetNamespaceObject[n.name]._typeInited=!0),e._internalConfig.appendTypeNameToObjectPathInfo&&this._objectPath&&this._objectPath.objectPathInfo&&this._className&&(this._objectPath.objectPathInfo.T=this._className)};(this.m_targetNamespaceObject[n.name]=o,this.extendsType(o,F),this.buildNewObject(o,n),0!==(2&n.behaviorFlags)&&(o.prototype._KeepReference=function(){ee.invokeMethod(this,"_KeepReference",1,[],0,0)}),0!==(32&n.behaviorFlags))&&this.getFunction(t.CustomizationCodeNamespace+"."+n.name+"_StaticCustomize").call(null,o);r&&(this.buildPrototype(o,n),o._typeInited=!0)},t.prototype.extendsType=function(e,t){function n(){this.constructor=e}e.prototype=null===t?Object.create(t):(n.prototype=t.prototype,new n)},t.prototype.findObjectUnderPath=function(t,n,r){for(var i=t,o=r;o<n.length;o++){if("object"!==typeof i)throw new e.Error("Cannot find "+n.join("."));i=i[n[o]]}return i},t.prototype.getFunction=function(t){var n=this.resolveObjectByFullName(t);if("function"!==typeof n)throw new e.Error("Cannot find function or type: "+t);return n},t.prototype.resolveObjectByFullName=function(e){var t=e.split(".");if(1===t.length)return this.m_targetNamespaceObject[t[0]];var n=t[0];return n===this.m_namespace?this.findObjectUnderPath(this.m_targetNamespaceObject,t,1):this.m_namespaceMap[n]?this.findObjectUnderPath(this.m_namespaceMap[n],t,1):this.findObjectUnderPath(this.m_targetNamespaceObject,t,0)},t.prototype.evaluateSimpleExpression=function(t,n){if(X.isNullOrUndefined(t))return null;var r=t.split(".");if(3===r.length&&"OfficeExtension"===r[0]&&"Constants"===r[1])return H[r[2]];if("this"===r[0]){for(var i=n,o=1;o<r.length;o++)i="toString()"==r[o]?i.toString():"()"===r[o].substr(r[o].length-2)?i[r[o].substr(0,r[o].length-2)]():i[r[o]];return i}throw new e.Error("Cannot evaluate: "+t)},t.prototype.evaluateEventTargetId=function(e,t){return X.isNullOrEmptyString(e)?"":this.evaluateSimpleExpression(e,t)},t.prototype.isAllDigits=function(e){for(var t="0".charCodeAt(0),n="9".charCodeAt(0),r=0;r<e.length;r++)if(e.charCodeAt(r)<t||e.charCodeAt(r)>n)return!1;return!0},t.prototype.evaluateEventType=function(t){if(X.isNullOrEmptyString(t))return 0;if(this.isAllDigits(t))return parseInt(t);var n=this.resolveObjectByFullName(t);if("number"!==typeof n)throw new e.Error("Invalid event type: "+t);return n},t.prototype.buildPrototype=function(e,t){this.buildScalarProperties(e,t),this.buildNavigationProperties(e,t),this.buildScalarMethods(e,t),this.buildNavigationMethods(e,t),this.buildEvents(e,t),this.buildHandleResult(e,t),this.buildHandleIdResult(e,t),this.buildHandleRetrieveResult(e,t),this.buildLoad(e,t),this.buildRetrieve(e,t),this.buildSetMockData(e,t),this.buildEnsureUnchanged(e,t),this.buildUpdate(e,t),this.buildSet(e,t),this.buildToJSON(e,t),this.buildItems(e,t),this.buildTypeMetadataInfo(e,t),this.buildTrackUntrack(e,t),this.buildMixin(e,t)},t.prototype.toSimpleCamelUpperCase=function(e){return e.substr(0,1).toUpperCase()+e.substr(1)},t.prototype.ensureOriginalName=function(e){null===e.originalName&&(e.originalName=this.toSimpleCamelUpperCase(e.name))},t.prototype.getFieldName=function(e){return"m_"+e.name},t.prototype.throwIfApiNotSupported=function(e,t){if(this.m_apiSets&&t.apiSetInfoOrdinal>0){var n=this.m_apiSets[t.apiSetInfoOrdinal-1];n&&X.throwIfApiNotSupported(e.name+"."+t.name,n.name,n.version,this.m_hostName)}},t.prototype.buildScalarProperties=function(e,t){if(Array.isArray(t.scalarProperties))for(var n=0;n<t.scalarProperties.length;n++){var r=t.scalarProperties[n];Array.isArray(r)&&(this.ensureArraySize(r,6),t.scalarProperties[n]={name:this.getString(r[0]),behaviorFlags:r[1],apiSetInfoOrdinal:r[2],originalName:this.getString(r[3]),setMethodApiFlags:r[4],undoableApiSetInfoOrdinal:r[5]}),this.buildScalarProperty(e,t,t.scalarProperties[n])}},t.prototype.calculateApiFlags=function(e,t){if(t>0){var n=this.m_apiSets[t-1];n&&(e=P.calculateApiFlags(e,n.name,n.version))}return e},t.prototype.buildScalarProperty=function(e,n,r){this.ensureOriginalName(r);var i=this,o=this.getFieldName(r),s={get:function(){return X.throwIfNotLoaded(r.name,this[o],n.name,this._isNull),i.throwIfApiNotSupported(n,r),this[o]},enumerable:!0,configurable:!0};0===(2&r.behaviorFlags)&&(s.set=function(e){if(4&r.behaviorFlags&&i.getFunction(t.CustomizationCodeNamespace+"."+n.name+"_"+r.originalName+"_Set").call(this,this,e).handled)return;this[o]=e;var s=i.calculateApiFlags(r.setMethodApiFlags,r.undoableApiSetInfoOrdinal);ee.invokeSetProperty(this,r.originalName,e,s)}),Object.defineProperty(e.prototype,r.name,s)},t.prototype.buildNavigationProperties=function(e,t){if(Array.isArray(t.navigationProperties))for(var n=0;n<t.navigationProperties.length;n++){var r=t.navigationProperties[n];Array.isArray(r)&&(this.ensureArraySize(r,8),t.navigationProperties[n]={name:this.getString(r[0]),behaviorFlags:r[2],apiSetInfoOrdinal:r[3],originalName:this.getString(r[4]),getMethodApiFlags:r[5],setMethodApiFlags:r[6],propertyTypeFullName:this.getString(r[1]),undoableApiSetInfoOrdinal:r[7]}),this.buildNavigationProperty(e,t,t.navigationProperties[n])}},t.prototype.buildNavigationProperty=function(e,n,r){this.ensureOriginalName(r);var i=this,o=this.getFieldName(r),s={get:function(){(this[i.getFieldName(r)]||(i.throwIfApiNotSupported(n,r),this[o]=ee.createPropertyObject(i.getFunction(r.propertyTypeFullName),this,r.originalName,0!==(16&r.behaviorFlags),r.getMethodApiFlags)),64&r.behaviorFlags)&&i.getFunction(t.CustomizationCodeNamespace+"."+n.name+"_"+r.originalName+"_Get").call(this,this,this[o]);return this[o]},enumerable:!0,configurable:!0};0===(2&r.behaviorFlags)&&(s.set=function(e){if(4&r.behaviorFlags&&i.getFunction(t.CustomizationCodeNamespace+"."+n.name+"_"+r.originalName+"_Set").call(this,this,e).handled)return;this[o]=e;var s=i.calculateApiFlags(r.setMethodApiFlags,r.undoableApiSetInfoOrdinal);ee.invokeSetProperty(this,r.originalName,e,s)}),Object.defineProperty(e.prototype,r.name,s)},t.prototype.buildScalarMethods=function(e,t){if(Array.isArray(t.scalarMethods))for(var n=0;n<t.scalarMethods.length;n++){var r=t.scalarMethods[n];Array.isArray(r)&&(this.ensureArraySize(r,7),t.scalarMethods[n]={name:this.getString(r[0]),behaviorFlags:r[2],apiSetInfoOrdinal:r[3],originalName:this.getString(r[5]),apiFlags:r[4],parameterCount:r[1],undoableApiSetInfoOrdinal:r[6]}),this.buildScalarMethod(e,t,t.scalarMethods[n])}},t.prototype.buildScalarMethod=function(e,n,r){this.ensureOriginalName(r);var i=this;e.prototype[r.name]=function(){var e=[];if(64&r.behaviorFlags&&r.parameterCount>0){for(var o=0;o<r.parameterCount-1;o++)e.push(arguments[o]);var s=[];for(o=r.parameterCount-1;o<arguments.length;o++)s.push(arguments[o]);e.push(s)}else for(o=0;o<arguments.length;o++)e.push(arguments[o]);if(1&r.behaviorFlags){var a=i.getFunction(t.CustomizationCodeNamespace+"."+n.name+"_"+r.originalName),c=[this];for(o=0;o<e.length;o++)c.push(e[o]);var u=a.apply(this,c),l=u.handled,p=u.result;if(l)return p}i.throwIfApiNotSupported(n,r);var f=0;32&r.behaviorFlags&&(f=1);var h=0;2&r.behaviorFlags&&(h=1);var d=i.calculateApiFlags(r.apiFlags,r.undoableApiSetInfoOrdinal);return ee.invokeMethod(this,r.originalName,h,e,d,f)}},t.prototype.buildNavigationMethods=function(e,t){if(Array.isArray(t.navigationMethods))for(var n=0;n<t.navigationMethods.length;n++){var r=t.navigationMethods[n];Array.isArray(r)&&(this.ensureArraySize(r,9),t.navigationMethods[n]={name:this.getString(r[0]),behaviorFlags:r[3],apiSetInfoOrdinal:r[4],originalName:this.getString(r[6]),apiFlags:r[5],parameterCount:r[2],returnTypeFullName:this.getString(r[1]),returnObjectGetByIdMethodName:this.getString(r[7]),undoableApiSetInfoOrdinal:r[8]}),this.buildNavigationMethod(e,t,t.navigationMethods[n])}},t.prototype.buildNavigationMethod=function(e,n,r){this.ensureOriginalName(r);var i=this;e.prototype[r.name]=function(){var e=[];if(64&r.behaviorFlags&&r.parameterCount>0){for(var o=0;o<r.parameterCount-1;o++)e.push(arguments[o]);var s=[];for(o=r.parameterCount-1;o<arguments.length;o++)s.push(arguments[o]);e.push(s)}else for(o=0;o<arguments.length;o++)e.push(arguments[o]);if(1&r.behaviorFlags){var a=i.getFunction(t.CustomizationCodeNamespace+"."+n.name+"_"+r.originalName),c=[this];for(o=0;o<e.length;o++)c.push(e[o]);var u=a.apply(this,c),l=u.handled,p=u.result;if(l)return p}if(i.throwIfApiNotSupported(n,r),0!==(16&r.behaviorFlags))return ee.createIndexerObject(i.getFunction(r.returnTypeFullName),this,e);var f=0;2&r.behaviorFlags&&(f=1);var h=i.calculateApiFlags(r.apiFlags,r.undoableApiSetInfoOrdinal);return ee.createMethodObject(i.getFunction(r.returnTypeFullName),this,r.originalName,f,e,0!==(4&r.behaviorFlags),0!==(8&r.behaviorFlags),r.returnObjectGetByIdMethodName,h)}},t.prototype.buildHandleResult=function(e,n){var r=this;e.prototype._handleResult=function(e){if(F.prototype._handleResult.call(this,e),!X.isNullOrUndefined(e)){if(X.fixObjectPathIfNecessary(this,e),8&n.behaviorFlags)r.getFunction(t.CustomizationCodeNamespace+"."+n.name+"_HandleResult").call(this,this,e);if(n.scalarProperties)for(var i=0;i<n.scalarProperties.length;i++)X.isUndefined(e[n.scalarProperties[i].originalName])||(0!==(8&n.scalarProperties[i].behaviorFlags)?this[r.getFieldName(n.scalarProperties[i])]=X.adjustToDateTime(e[n.scalarProperties[i].originalName]):this[r.getFieldName(n.scalarProperties[i])]=e[n.scalarProperties[i].originalName]);if(n.navigationProperties){for(var o=[],s=0;s<n.navigationProperties.length;s++)o.push(n.navigationProperties[s].name),o.push(n.navigationProperties[s].originalName);X._handleNavigationPropertyResults(this,e,o)}if(0!==(1&n.behaviorFlags)){var a=r.hasIndexMethod(n);if(!X.isNullOrUndefined(e[H.items])){this.m__items=[];for(var c=e[H.items],u=r.getFunction(n.childItemTypeFullName),l=0;l<c.length;l++){var p=ee.createChildItemObject(u,a,this,c[l],l);p._handleResult(c[l]),this.m__items.push(p)}}}}}},t.prototype.buildHandleRetrieveResult=function(e,t){var n=this;e.prototype._handleRetrieveResult=function(e,r){if(F.prototype._handleRetrieveResult.call(this,e,r),!X.isNullOrUndefined(e)){if(t.scalarProperties)for(var i=0;i<t.scalarProperties.length;i++)8&t.scalarProperties[i].behaviorFlags&&(X.isNullOrUndefined(e[t.scalarProperties[i].name])||(e[t.scalarProperties[i].name]=X.adjustToDateTime(e[t.scalarProperties[i].name])));if(1&t.behaviorFlags){var o=n.hasIndexMethod(t),s=n.getFunction(t.childItemTypeFullName),a=this;X.processRetrieveResult(a,e,r,(function(e,t){return ee.createChildItemObject(s,o,a,e,t)}))}else X.processRetrieveResult(this,e,r)}}},t.prototype.buildHandleIdResult=function(e,n){var r=this;e.prototype._handleIdResult=function(e){if(F.prototype._handleIdResult.call(this,e),!X.isNullOrUndefined(e)){if(16&n.behaviorFlags)r.getFunction(t.CustomizationCodeNamespace+"."+n.name+"_HandleIdResult").call(this,this,e);if(n.scalarProperties)for(var i=0;i<n.scalarProperties.length;i++){var o=n.scalarProperties[i].originalName;"Id"!==o&&"_Id"!==o&&"_ReferenceId"!==o||X.isNullOrUndefined(e[n.scalarProperties[i].originalName])||(this[r.getFieldName(n.scalarProperties[i])]=e[n.scalarProperties[i].originalName])}}}},t.prototype.buildLoad=function(e,t){e.prototype.load=function(e){return X.load(this,e)}},t.prototype.buildRetrieve=function(e,t){e.prototype.retrieve=function(e){return X.retrieve(this,e)}},t.prototype.buildNewObject=function(e,t){X.isNullOrEmptyString(t.newObjectServerTypeFullName)||(e.newObject=function(n){return ee.createTopLevelServiceObject(e,n,t.newObjectServerTypeFullName,0!==(1&t.behaviorFlags),t.newObjectApiFlags)})},t.prototype.buildSetMockData=function(e,t){var n=this;if(1&t.behaviorFlags){var r=n.hasIndexMethod(t);e.prototype.setMockData=function(e){var i=this;X.setMockData(i,e,(function(e,o){return ee.createChildItemObject(n.getFunction(t.childItemTypeFullName),r,i,e,o)}),(function(e){i.m__items=e}))}}else e.prototype.setMockData=function(e){X.setMockData(this,e)}},t.prototype.buildEnsureUnchanged=function(e,t){e.prototype.ensureUnchanged=function(e){ee.invokeEnsureUnchanged(this,e)}},t.prototype.buildUpdate=function(e,t){e.prototype.update=function(e){this._recursivelyUpdate(e)}},t.prototype.buildSet=function(e,t){if(0===(1&t.behaviorFlags)){var n=[],r=[];if(t.scalarProperties)for(var i=0;i<t.scalarProperties.length;i++)0===(2&t.scalarProperties[i].behaviorFlags)&&0!==(1&t.scalarProperties[i].behaviorFlags)?r.push(t.scalarProperties[i].name):n.push(t.scalarProperties[i].name);var o=[];if(t.navigationProperties)for(i=0;i<t.navigationProperties.length;i++)0!==(16&t.navigationProperties[i].behaviorFlags)||0===(1&t.navigationProperties[i].behaviorFlags)||0===(32&t.navigationProperties[i].behaviorFlags)?n.push(t.navigationProperties[i].name):o.push(t.navigationProperties[i].name);0===o.length&&0===r.length||(e.prototype.set=function(e,t){this._recursivelySet(e,t,r,o,n)})}},t.prototype.buildItems=function(e,t){0!==(1&t.behaviorFlags)&&Object.defineProperty(e.prototype,"items",{get:function(){return X.throwIfNotLoaded("items",this.m__items,t.name,this._isNull),this.m__items},enumerable:!0,configurable:!0})},t.prototype.buildToJSON=function(e,t){var n=this;0===(1&t.behaviorFlags)?e.prototype.toJSON=function(){var e={};if(t.scalarProperties)for(var r=0;r<t.scalarProperties.length;r++)0!==(1&t.scalarProperties[r].behaviorFlags)&&(e[t.scalarProperties[r].name]=this[n.getFieldName(t.scalarProperties[r])]);var i={};if(t.navigationProperties)for(r=0;r<t.navigationProperties.length;r++)0!==(1&t.navigationProperties[r].behaviorFlags)&&(i[t.navigationProperties[r].name]=this[n.getFieldName(t.navigationProperties[r])]);return X.toJson(this,e,i)}:e.prototype.toJSON=function(){return X.toJson(this,{},{},this.m__items)}},t.prototype.buildTypeMetadataInfo=function(e,t){Object.defineProperty(e.prototype,"_className",{get:function(){return t.name},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"_isCollection",{get:function(){return 0!==(1&t.behaviorFlags)},enumerable:!0,configurable:!0}),X.isNullOrEmptyString(t.collectionPropertyPath)||Object.defineProperty(e.prototype,"_collectionPropertyPath",{get:function(){return t.collectionPropertyPath},enumerable:!0,configurable:!0}),t.scalarProperties&&t.scalarProperties.length>0&&(Object.defineProperty(e.prototype,"_scalarPropertyNames",{get:function(){return this.m__scalarPropertyNames||(this.m__scalarPropertyNames=t.scalarProperties.map((function(e){return e.name}))),this.m__scalarPropertyNames},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"_scalarPropertyOriginalNames",{get:function(){return this.m__scalarPropertyOriginalNames||(this.m__scalarPropertyOriginalNames=t.scalarProperties.map((function(e){return e.originalName}))),this.m__scalarPropertyOriginalNames},enumerable:!0,configurable:!0}),Object.defineProperty(e.prototype,"_scalarPropertyUpdateable",{get:function(){return this.m__scalarPropertyUpdateable||(this.m__scalarPropertyUpdateable=t.scalarProperties.map((function(e){return 0===(2&e.behaviorFlags)}))),this.m__scalarPropertyUpdateable},enumerable:!0,configurable:!0})),t.navigationProperties&&t.navigationProperties.length>0&&Object.defineProperty(e.prototype,"_navigationPropertyNames",{get:function(){return this.m__navigationPropertyNames||(this.m__navigationPropertyNames=t.navigationProperties.map((function(e){return e.name}))),this.m__navigationPropertyNames},enumerable:!0,configurable:!0})},t.prototype.buildTrackUntrack=function(e,t){2&t.behaviorFlags&&(e.prototype.track=function(){return this.context.trackedObjects.add(this),this},e.prototype.untrack=function(){return this.context.trackedObjects.remove(this),this})},t.prototype.buildMixin=function(e,t){if(4&t.behaviorFlags){var n=this.getFunction(t.name+"Custom");X.applyMixin(e,n)}},t.prototype.getOnEventName=function(e){return"_"===e[0]?"_on"+e.substr(1):"on"+e},t.prototype.buildEvents=function(e,t){if(t.events)for(var n=0;n<t.events.length;n++){var r=t.events[n];Array.isArray(r)&&(this.ensureArraySize(r,7),t.events[n]={name:this.getString(r[0]),behaviorFlags:r[1],apiSetInfoOrdinal:r[2],typeExpression:this.getString(r[3]),targetIdExpression:this.getString(r[4]),register:this.getString(r[5]),unregister:this.getString(r[6])}),this.buildEvent(e,t,t.events[n])}},t.prototype.buildEvent=function(e,t,n){1&n.behaviorFlags?this.buildV0Event(e,t,n):this.buildV2Event(e,t,n)},t.prototype.buildV2Event=function(e,n,r){var i=this,o=this.getOnEventName(r.name),s=this.getFieldName(r);Object.defineProperty(e.prototype,o,{get:function(){if(!this[s]){i.throwIfApiNotSupported(n,r);var e=this,o=null;"null"!==r.register&&(o=this[r.register].bind(this));var a=null;"null"!==r.unregister&&(a=this[r.unregister].bind(this));var c=null;2&r.behaviorFlags&&(c=i.getFunction(t.CustomizationCodeNamespace+"."+n.name+"_"+r.name+"_EventArgsTransform"));var u=i.evaluateEventType(r.typeExpression);this[s]=new W(this.context,this,r.name,{eventType:u,getTargetIdFunc:function(){return i.evaluateEventTargetId(r.targetIdExpression,e)},registerFunc:o,unregisterFunc:a,eventArgsTransformFunc:function(t){return c&&(t=c.call(e,e,t)),X._createPromiseFromResult(t)}})}return this[s]},enumerable:!0,configurable:!0})},t.prototype.buildV0Event=function(e,n,r){var i=this,o=this.getOnEventName(r.name),s=this.getFieldName(r);Object.defineProperty(e.prototype,o,{get:function(){if(!this[s]){i.throwIfApiNotSupported(n,r);var e=this,o=null;if(X.isNullOrEmptyString(r.register)){var a=i.evaluateEventType(r.typeExpression);o=function(t){var n=i.evaluateEventTargetId(r.targetIdExpression,e);return e.context.eventRegistration.register(a,n,t)}}else if("null"!==r.register){var c=i.getFunction(r.register);o=function(t){return c.call(e,e,t)}}var u=null;if(X.isNullOrEmptyString(r.unregister)){var l=i.evaluateEventType(r.typeExpression);u=function(t){var n=i.evaluateEventTargetId(r.targetIdExpression,e);return e.context.eventRegistration.unregister(l,n,t)}}else if("null"!==r.unregister){var p=i.getFunction(r.unregister);u=function(t){return p.call(e,e,t)}}var f=null;2&r.behaviorFlags&&(f=i.getFunction(t.CustomizationCodeNamespace+"."+n.name+"_"+r.name+"_EventArgsTransform"));this[s]=new q(this.context,this,r.name,{registerFunc:o,unregisterFunc:u,eventArgsTransformFunc:function(t){return f&&(t=f.call(e,e,t)),X._createPromiseFromResult(t)}})}return this[s]},enumerable:!0,configurable:!0})},t.prototype.hasIndexMethod=function(e){var t=!1;if(e.navigationMethods)for(var n=0;n<e.navigationMethods.length;n++)if(0!==(16&e.navigationMethods[n].behaviorFlags)){t=!0;break}return t},t.CustomizationCodeNamespace="_CC",t}();e.LibraryBuilder=te;var ne,re=1,ie=function(e){return e.DdaMethod.Version=re,e},oe=function(e){return re=e.Version,e.Error&&(e.error={},e.error.Code=e.Error),e};!function(e){e.SendingId="sId",e.RespondingId="rId",e.CommandKey="command",e.SessionInfoKey="sessionInfo",e.ParamsKey="params",e.ApiReadyCommand="apiready",e.ExecuteMethodCommand="executeMethod",e.GetAppContextCommand="getAppContext",e.RegisterEventCommand="registerEvent",e.UnregisterEventCommand="unregisterEvent",e.FireEventCommand="fireEvent"}(ne||(ne={}));var se=function(){function e(){}return e.sessionContext="sc",e.embeddingPageOrigin="EmbeddingPageOrigin",e.embeddingPageSessionInfo="EmbeddingPageSessionInfo",e}();e.EmbeddedConstants=se;var ae=function(e){function t(t,n){var r=e.call(this)||this;return r.m_chosenWindow=null,r.m_chosenOrigin=null,r.m_enabled=!0,r.m_onMessageHandler=r._onMessage.bind(r),r.m_callbackList={},r.m_id=0,r.m_timeoutId=-1,r.m_appContext=null,r.m_url=t,r.m_options=n,r.m_options||(r.m_options={sessionKey:Math.random().toString()}),r.m_options.sessionKey||(r.m_options.sessionKey=Math.random().toString()),r.m_options.container||(r.m_options.container=document.body),r.m_options.timeoutInMilliseconds||(r.m_options.timeoutInMilliseconds=6e4),r.m_options.height||(r.m_options.height="400px"),r.m_options.width||(r.m_options.width="100%"),r.m_options.webApplication&&r.m_options.webApplication.accessToken&&r.m_options.webApplication.accessTokenTtl||(r.m_options.webApplication=null),r}return __extends(t,e),t.prototype._getIFrameSrc=function(){var e=window.location.protocol+"//"+window.location.host,t=se.embeddingPageOrigin+"="+encodeURIComponent(e)+"&"+se.embeddingPageSessionInfo+"="+encodeURIComponent(this.m_options.sessionKey),n=!1;(this.m_url.toLowerCase().indexOf("/_layouts/preauth.aspx")>0||this.m_url.toLowerCase().indexOf("/_layouts/15/preauth.aspx")>0)&&(n=!0);var r=document.createElement("a");if(r.href=this.m_url,this.m_options.webApplication){var i=se.embeddingPageOrigin+"="+e+"&"+se.embeddingPageSessionInfo+"="+this.m_options.sessionKey;0===r.search.length||"?"===r.search?r.search="?"+se.sessionContext+"="+encodeURIComponent(i):r.search=r.search+"&"+se.sessionContext+"="+encodeURIComponent(i)}else n?0===r.hash.length||"#"===r.hash?r.hash="#"+t:r.hash=r.hash+"&"+t:0===r.search.length||"?"===r.search?r.search="?"+t:r.search=r.search+"&"+t;return r.href},t.prototype.init=function(){var e=this;window.addEventListener("message",this.m_onMessageHandler);var t=this._getIFrameSrc();return u.createPromise((function(n,r){var i=document.createElement("iframe");if(e.m_options.id&&(i.id=e.m_options.id,i.name=e.m_options.id),i.style.height=e.m_options.height,i.style.width=e.m_options.width,e.m_options.webApplication){var a=document.createElement("form");a.setAttribute("action",t),a.setAttribute("method","post"),a.setAttribute("target",i.name),e.m_options.container.appendChild(a);var c=document.createElement("input");c.setAttribute("type","hidden"),c.setAttribute("name","access_token"),c.setAttribute("value",e.m_options.webApplication.accessToken),a.appendChild(c);var l=document.createElement("input");l.setAttribute("type","hidden"),l.setAttribute("name","access_token_ttl"),l.setAttribute("value",e.m_options.webApplication.accessTokenTtl),a.appendChild(l),e.m_options.container.appendChild(i),a.submit()}else i.src=t,e.m_options.container.appendChild(i);e.m_timeoutId=window.setTimeout((function(){e.close();var t=X.createRuntimeError(o.timeout,u._getResourceString(s.timeout),"EmbeddedSession.init");r(t)}),e.m_options.timeoutInMilliseconds),e.m_promiseResolver=n}))},t.prototype._invoke=function(e,t,n){this.m_enabled?(ie&&(n=ie(n)),this._sendMessageWithCallback(this.m_id++,e,n,(function(e){oe&&(e=oe(e));var n=e.Error;delete e.Error,t(n||0,e)}))):t(5001,null)},t.prototype.close=function(){window.removeEventListener("message",this.m_onMessageHandler),window.clearTimeout(this.m_timeoutId),this.m_enabled=!1},Object.defineProperty(t.prototype,"eventRegistration",{get:function(){return this.m_sessionEventManager||(this.m_sessionEventManager=new G(this._registerEventImpl.bind(this),this._unregisterEventImpl.bind(this))),this.m_sessionEventManager},enumerable:!0,configurable:!0}),t.prototype._createRequestExecutorOrNull=function(){return new ce(this)},t.prototype._resolveRequestUrlAndHeaderInfo=function(){return u._createPromiseFromResult(null)},t.prototype._registerEventImpl=function(e,t){var n=this;return u.createPromise((function(r,i){n._sendMessageWithCallback(n.m_id++,ne.RegisterEventCommand,{EventId:e,TargetId:t},(function(){r(null)}))}))},t.prototype._unregisterEventImpl=function(e,t){var n=this;return u.createPromise((function(r,i){n._sendMessageWithCallback(n.m_id++,ne.UnregisterEventCommand,{EventId:e,TargetId:t},(function(){r()}))}))},t.prototype._onMessage=function(e){var t=this;if(this.m_enabled&&(!this.m_chosenWindow||this.m_chosenWindow===e.source&&this.m_chosenOrigin===e.origin)){var n=e.data;if(n&&n[ne.CommandKey]===ne.ApiReadyCommand)!this.m_chosenWindow&&this._isValidDescendant(e.source)&&n[ne.SessionInfoKey]===this.m_options.sessionKey&&(this.m_chosenWindow=e.source,this.m_chosenOrigin=e.origin,this._sendMessageWithCallback(this.m_id++,ne.GetAppContextCommand,null,(function(e){t._setupContext(e),window.clearTimeout(t.m_timeoutId),t.m_promiseResolver()})));else if(n&&n[ne.CommandKey]===ne.FireEventCommand){var r=n[ne.ParamsKey],i=r.EventId,o=r.TargetId,s=r.Data;if(this.m_sessionEventManager)for(var a=this.m_sessionEventManager.getHandlers(i,o),c=0;c<a.length;c++)a[c](s)}else if(n&&n.hasOwnProperty(ne.RespondingId)){var u=n[ne.RespondingId],l=this.m_callbackList[u];"function"===typeof l&&l(n[ne.ParamsKey]),delete this.m_callbackList[u]}}},t.prototype._sendMessageWithCallback=function(e,t,n,r){this.m_callbackList[e]=r;var i={};i[ne.SendingId]=e,i[ne.CommandKey]=t,i[ne.ParamsKey]=n,this.m_chosenWindow.postMessage(JSON.stringify(i),this.m_chosenOrigin)},t.prototype._isValidDescendant=function(e){function t(n){if(n===e)return!0;for(var r=0,i=n.frames.length;r<i;r++)if(t(n.frames[r]))return!0;return!1}for(var n=(this.m_options.container||document.body).getElementsByTagName("iframe"),r=0,i=n.length;r<i;r++)if(t(n[r].contentWindow))return!0;return!1},t.prototype._setupContext=function(e){this.m_appContext=e},t}(t);e.EmbeddedSession=ae;var ce=function(){function e(e){this.m_session=e}return e.prototype.executeAsync=function(t,n,r){var i=this,o=c.buildMessageArrayForIRequestExecutor(t,n,r,e.SourceLibHeaderValue);return u.createPromise((function(t,n){i.m_session._invoke(ne.ExecuteMethodCommand,(function(e,n){var r;u.log("Response:"),u.log(JSON.stringify(n)),r=0==e?c.buildResponseOnSuccess(c.getResponseBodyFromSafeArray(n.Data),c.getResponseHeadersFromSafeArray(n.Data)):c.buildResponseOnError(n.error.Code,n.error.Message),t(r)}),e._transformMessageArrayIntoParams(o))}))},e._transformMessageArrayIntoParams=function(t){return{ArrayData:t,DdaMethod:{DispatchId:e.DispidExecuteRichApiRequestMethod}}},e.DispidExecuteRichApiRequestMethod=93,e.SourceLibHeaderValue="Embedded",e}()}(OfficeExtension||(OfficeExtension={}));__extends=this&&this.__extends||function(){var e=function(t,n){return(e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])})(t,n)};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();var OfficeFirstPartyAuth,OfficeCore,Office,__awaiter=this&&this.__awaiter||function(e,t,n,r){return new(n||(n=Promise))((function(i,o){function s(e){try{c(r.next(e))}catch(e){o(e)}}function a(e){try{c(r.throw(e))}catch(e){o(e)}}function c(e){e.done?i(e.value):new n((function(t){t(e.value)})).then(s,a)}c((r=r.apply(e,t||[])).next())}))},__generator=this&&this.__generator||function(e,t){var n,r,i,o,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"===typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(o){return function(a){return function(o){if(n)throw new TypeError("Generator is already executing.");for(;s;)try{if(n=1,r&&(i=2&o[0]?r.return:o[0]?r.throw||((i=r.return)&&i.call(r),0):r.next)&&!(i=i.call(r,o[1])).done)return i;switch(r=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,r=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(!(i=(i=s.trys).length>0&&i[i.length-1])&&(6===o[0]||2===o[0])){s=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){s.label=o[1];break}if(6===o[0]&&s.label<i[1]){s.label=i[1],i=o;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(o);break}i[2]&&s.ops.pop(),s.trys.pop();continue}o=t.call(e,s)}catch(e){o=[6,e],r=0}finally{n=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,a])}}};!function(e){OfficeExtension.BatchApiHelper.createPropertyObject,OfficeExtension.BatchApiHelper.createMethodObject,OfficeExtension.BatchApiHelper.createIndexerObject,OfficeExtension.BatchApiHelper.createRootServiceObject;var t=OfficeExtension.BatchApiHelper.createTopLevelServiceObject,n=(OfficeExtension.BatchApiHelper.createChildItemObject,OfficeExtension.BatchApiHelper.invokeMethod),r=(OfficeExtension.BatchApiHelper.invokeEnsureUnchanged,OfficeExtension.BatchApiHelper.invokeSetProperty,OfficeExtension.Utility.isNullOrUndefined),i=(OfficeExtension.Utility.isUndefined,OfficeExtension.Utility.throwIfNotLoaded,OfficeExtension.Utility.throwIfApiNotSupported,OfficeExtension.Utility.load,OfficeExtension.Utility.retrieve,OfficeExtension.Utility.toJson),o=OfficeExtension.Utility.fixObjectPathIfNecessary,s=(OfficeExtension.Utility._handleNavigationPropertyResults,OfficeExtension.Utility.adjustToDateTime,OfficeExtension.Utility.processRetrieveResult),a=function(a){function c(){return null!==a&&a.apply(this,arguments)||this}return __extends(c,a),Object.defineProperty(c.prototype,"_className",{get:function(){return"BiShim"},enumerable:!0,configurable:!0}),c.prototype.initialize=function(e){n(this,"Initialize",0,[e],0,0)},c.prototype.getData=function(){return n(this,"getData",1,[],4,0)},c.prototype.setVisualObjects=function(e){n(this,"setVisualObjects",0,[e],2,0)},c.prototype.setVisualObjectsToPersist=function(e){n(this,"setVisualObjectsToPersist",0,[e],2,0)},c.prototype._handleResult=function(e){(a.prototype._handleResult.call(this,e),r(e))||o(this,e)},c.prototype._handleRetrieveResult=function(e,t){a.prototype._handleRetrieveResult.call(this,e,t),s(this,e,t)},c.newObject=function(n){return t(e.BiShim,n,"Microsoft.AgaveVisual.BiShim",!1,4)},c.prototype.toJSON=function(){return i(this,{},{})},c}(OfficeExtension.ClientObject);e.BiShim=a,function(e){e.generalException1="GeneralException"}(e.AgaveVisualErrorCodes||(e.AgaveVisualErrorCodes={}))}(OfficeCore||(OfficeCore={})),function(e){OfficeExtension.BatchApiHelper.createPropertyObject;var t=OfficeExtension.BatchApiHelper.createMethodObject,n=(OfficeExtension.BatchApiHelper.createIndexerObject,OfficeExtension.BatchApiHelper.createRootServiceObject,OfficeExtension.BatchApiHelper.createTopLevelServiceObject),r=(OfficeExtension.BatchApiHelper.createChildItemObject,OfficeExtension.BatchApiHelper.invokeMethod),i=OfficeExtension.BatchApiHelper.invokeEnsureUnchanged,o=(OfficeExtension.BatchApiHelper.invokeSetProperty,OfficeExtension.Utility.isNullOrUndefined),s=OfficeExtension.Utility.isUndefined,a=OfficeExtension.Utility.throwIfNotLoaded,c=(OfficeExtension.Utility.throwIfApiNotSupported,OfficeExtension.Utility.load),u=OfficeExtension.Utility.retrieve,l=OfficeExtension.Utility.toJson,p=OfficeExtension.Utility.fixObjectPathIfNecessary,f=(OfficeExtension.Utility._handleNavigationPropertyResults,OfficeExtension.Utility.adjustToDateTime,OfficeExtension.Utility.processRetrieveResult),h=function(i){function s(){return null!==i&&i.apply(this,arguments)||this}return __extends(s,i),Object.defineProperty(s.prototype,"_className",{get:function(){return"FlightingService"},enumerable:!0,configurable:!0}),s.prototype.getClientSessionId=function(){return r(this,"GetClientSessionId",1,[],4,0)},s.prototype.getDeferredFlights=function(){return r(this,"GetDeferredFlights",1,[],4,0)},s.prototype.getFeature=function(n,r,i,o){return t(e.ABType,this,"GetFeature",1,[n,r,i,o],!1,!1,null,4)},s.prototype.getFeatureGate=function(n,r){return t(e.ABType,this,"GetFeatureGate",1,[n,r],!1,!1,null,4)},s.prototype.resetOverride=function(e){r(this,"ResetOverride",0,[e],0,0)},s.prototype.setOverride=function(e,t,n){r(this,"SetOverride",0,[e,t,n],0,0)},s.prototype._handleResult=function(e){(i.prototype._handleResult.call(this,e),o(e))||p(this,e)},s.prototype._handleRetrieveResult=function(e,t){i.prototype._handleRetrieveResult.call(this,e,t),f(this,e,t)},s.newObject=function(t){return n(e.FlightingService,t,"Microsoft.Experiment.FlightingService",!1,4)},s.prototype.toJSON=function(){return l(this,{},{})},s}(OfficeExtension.ClientObject);e.FlightingService=h;var d=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return __extends(t,e),Object.defineProperty(t.prototype,"_className",{get:function(){return"ABType"},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_scalarPropertyNames",{get:function(){return["value"]},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"value",{get:function(){return a("value",this._V,"ABType",this._isNull),this._V},enumerable:!0,configurable:!0}),t.prototype._handleResult=function(t){if(e.prototype._handleResult.call(this,t),!o(t)){var n=t;p(this,n),s(n.Value)||(this._V=n.Value)}},t.prototype.load=function(e){return c(this,e)},t.prototype.retrieve=function(e){return u(this,e)},t.prototype._handleRetrieveResult=function(t,n){e.prototype._handleRetrieveResult.call(this,t,n),f(this,t,n)},t.prototype.toJSON=function(){return l(this,{value:this._V},{})},t.prototype.ensureUnchanged=function(e){i(this,e)},t}(OfficeExtension.ClientObject);e.ABType=d,function(e){e.boolean="Boolean",e.integer="Integer",e.string="String"}(e.FeatureType||(e.FeatureType={})),function(e){e.generalException="GeneralException"}(e.ExperimentErrorCodes||(e.ExperimentErrorCodes={}))}(OfficeCore||(OfficeCore={})),function(e){e.OfficeOnlineDomainList=["*.dod.online.office365.us","*.gov.online.office365.us","*.officeapps-df.live.com","*.officeapps.live.com","*.online.office.de","*.partner.officewebapps.cn"],e.isHostOriginTrusted=function(){if("undefined"===typeof window.external||"undefined"===typeof window.external.GetContext){var t=OSF.getClientEndPoint()._targetUrl,n=function(e){return e.split("/")[2].split(":")[0].split("?")[0]}(t);return 0!=t.indexOf("https:")||e.OfficeOnlineDomainList.forEach((function(e){if(0==e.indexOf("*.")&&(e=e.substring(2)),n.indexOf(e)==n.length-e.length)return!0})),!1}return!0}}(OfficeCore||(OfficeCore={})),function(e){var t=function(){function t(e){this.context=e}return Object.defineProperty(t.prototype,"roamingSettings",{get:function(){return this.m_roamingSettings||(this.m_roamingSettings=e.AuthenticationService.newObject(this.context).roamingSettings),this.m_roamingSettings},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"tap",{get:function(){return this.m_tap||(this.m_tap=e.Tap.newObject(this.context)),this.m_tap},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"skill",{get:function(){return this.m_skill||(this.m_skill=e.Skill.newObject(this.context)),this.m_skill},enumerable:!0,configurable:!0}),t}();e.FirstPartyApis=t;var n=function(n){function r(e){return n.call(this,e)||this}return __extends(r,n),Object.defineProperty(r.prototype,"firstParty",{get:function(){return this.m_firstPartyApis||(this.m_firstPartyApis=new t(this)),this.m_firstPartyApis},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"flighting",{get:function(){return this.flightingService},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"telemetry",{get:function(){return this.m_telemetry||(this.m_telemetry=e.TelemetryService.newObject(this)),this.m_telemetry},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"ribbon",{get:function(){return this.m_ribbon||(this.m_ribbon=e.DynamicRibbon.newObject(this)),this.m_ribbon},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"bi",{get:function(){return this.m_biShim||(this.m_biShim=e.BiShim.newObject(this)),this.m_biShim},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"flightingService",{get:function(){return this.m_flightingService||(this.m_flightingService=e.FlightingService.newObject(this)),this.m_flightingService},enumerable:!0,configurable:!0}),r}(OfficeExtension.ClientRequestContext);e.RequestContext=n,e.run=function(t,n){return OfficeExtension.ClientRequestContext._runBatch("OfficeCore.run",arguments,(function(t){return new e.RequestContext(t)}))}}(OfficeCore||(OfficeCore={})),function(e){!function(e){function t(){var e=new OfficeCore.RequestContext;return"web"==OSF._OfficeAppFactory.getHostInfo().hostPlatform&&(e._customData="WacPartition"),e}e.isFeatureEnabled=function(e,n){return __awaiter(this,void 0,void 0,(function(){var r,i,o;return __generator(this,(function(s){switch(s.label){case 0:return r=t(),i=OfficeCore.License.newObject(r),o=i.isFeatureEnabled(e,n),[4,r.sync()];case 1:return s.sent(),[2,o.value]}}))}))},e.getFeatureTier=function(e,n){return __awaiter(this,void 0,void 0,(function(){var r,i,o;return __generator(this,(function(s){switch(s.label){case 0:return r=t(),i=OfficeCore.License.newObject(r),o=i.getFeatureTier(e,n),[4,r.sync()];case 1:return s.sent(),[2,o.value]}}))}))},e.isFreemiumUpsellEnabled=function(){return __awaiter(this,void 0,void 0,(function(){var e,n,r;return __generator(this,(function(i){switch(i.label){case 0:return e=t(),n=OfficeCore.License.newObject(e),r=n.isFreemiumUpsellEnabled(),[4,e.sync()];case 1:return i.sent(),[2,r.value]}}))}))},e.launchUpsellExperience=function(e){return __awaiter(this,void 0,void 0,(function(){var n;return __generator(this,(function(r){switch(r.label){case 0:return n=t(),OfficeCore.License.newObject(n).launchUpsellExperience(e),[4,n.sync()];case 1:return r.sent(),[2]}}))}))},e.onFeatureStateChanged=function(e,n){return __awaiter(this,void 0,void 0,(function(){var r,i,o,s;return __generator(this,(function(a){switch(a.label){case 0:return r=t(),i=OfficeCore.License.newObject(r),(o=i.getLicenseFeature(e)).onStateChanged.add(n),s=function(){return o.onStateChanged.remove(n),null},[4,r.sync()];case 1:return a.sent(),[2,s]}}))}))}}(e.license||(e.license={}))}(Office||(Office={})),function(e){OfficeExtension.BatchApiHelper.createPropertyObject,OfficeExtension.BatchApiHelper.createMethodObject,OfficeExtension.BatchApiHelper.createIndexerObject,OfficeExtension.BatchApiHelper.createRootServiceObject;var t,n=OfficeExtension.BatchApiHelper.createTopLevelServiceObject,r=(OfficeExtension.BatchApiHelper.createChildItemObject,OfficeExtension.BatchApiHelper.invokeMethod),i=(OfficeExtension.BatchApiHelper.invokeEnsureUnchanged,OfficeExtension.BatchApiHelper.invokeSetProperty,OfficeExtension.Utility.isNullOrUndefined),o=(OfficeExtension.Utility.isUndefined,OfficeExtension.Utility.throwIfNotLoaded,OfficeExtension.Utility.throwIfApiNotSupported,OfficeExtension.Utility.load,OfficeExtension.Utility.retrieve,OfficeExtension.Utility.toJson),s=OfficeExtension.Utility.fixObjectPathIfNecessary,a=(OfficeExtension.Utility._handleNavigationPropertyResults,OfficeExtension.Utility.adjustToDateTime,OfficeExtension.Utility.processRetrieveResult),c=(OfficeExtension.Utility.setMockData,OfficeExtension.CommonUtility.calculateApiFlags,function(c){function u(){return null!==c&&c.apply(this,arguments)||this}return __extends(u,c),Object.defineProperty(u.prototype,"_className",{get:function(){return"Skill"},enumerable:!0,configurable:!0}),u.prototype.executeAction=function(e,t,n){return r(this,"ExecuteAction",1,[e,t,n],5,0)},u.prototype.notifyPaneEvent=function(e,t){r(this,"NotifyPaneEvent",1,[e,t],5,0)},u.prototype.registerHostSkillEvent=function(){r(this,"RegisterHostSkillEvent",0,[],1,0)},u.prototype.testFireEvent=function(){r(this,"TestFireEvent",0,[],1,0)},u.prototype.unregisterHostSkillEvent=function(){r(this,"UnregisterHostSkillEvent",0,[],1,0)},u.prototype._handleResult=function(e){(c.prototype._handleResult.call(this,e),i(e))||s(this,e)},u.prototype._handleRetrieveResult=function(e,t){c.prototype._handleRetrieveResult.call(this,e,t),a(this,e,t)},u.newObject=function(t){return n(e.Skill,t,"Microsoft.SkillApi.Skill",!1,4)},Object.defineProperty(u.prototype,"onHostSkillEvent",{get:function(){var e=this;return this.m_hostSkillEvent||(this.m_hostSkillEvent=new OfficeExtension.GenericEventHandlers(this.context,this,"HostSkillEvent",{eventType:65538,registerFunc:function(){return e.registerHostSkillEvent()},unregisterFunc:function(){return e.unregisterHostSkillEvent()},getTargetIdFunc:function(){return""},eventArgsTransformFunc:function(n){var r=t.Skill_HostSkillEvent_EventArgsTransform(e,n);return OfficeExtension.Utility._createPromiseFromResult(r)}})),this.m_hostSkillEvent},enumerable:!0,configurable:!0}),u.prototype.toJSON=function(){return o(this,{},{})},u}(OfficeExtension.ClientObject));e.Skill=c,function(e){e.Skill_HostSkillEvent_EventArgsTransform=function(e,t){return{type:t.type,data:t.data}}}(t=e._CC||(e._CC={})),function(e){e.generalException="GeneralException"}(e.SkillErrorCodes||(e.SkillErrorCodes={}))}(OfficeCore||(OfficeCore={})),function(e){OfficeExtension.BatchApiHelper.createPropertyObject,OfficeExtension.BatchApiHelper.createMethodObject,OfficeExtension.BatchApiHelper.createIndexerObject,OfficeExtension.BatchApiHelper.createRootServiceObject;var t=OfficeExtension.BatchApiHelper.createTopLevelServiceObject,n=(OfficeExtension.BatchApiHelper.createChildItemObject,OfficeExtension.BatchApiHelper.invokeMethod),r=(OfficeExtension.BatchApiHelper.invokeEnsureUnchanged,OfficeExtension.BatchApiHelper.invokeSetProperty,OfficeExtension.Utility.isNullOrUndefined),i=(OfficeExtension.Utility.isUndefined,OfficeExtension.Utility.throwIfNotLoaded,OfficeExtension.Utility.throwIfApiNotSupported,OfficeExtension.Utility.load,OfficeExtension.Utility.retrieve,OfficeExtension.Utility.toJson),o=OfficeExtension.Utility.fixObjectPathIfNecessary,s=(OfficeExtension.Utility._handleNavigationPropertyResults,OfficeExtension.Utility.adjustToDateTime,OfficeExtension.Utility.processRetrieveResult),a=function(a){function c(){return null!==a&&a.apply(this,arguments)||this}return __extends(c,a),Object.defineProperty(c.prototype,"_className",{get:function(){return"TelemetryService"},enumerable:!0,configurable:!0}),c.prototype.sendTelemetryEvent=function(e,t,r,i,o){n(this,"SendTelemetryEvent",1,[e,t,r,i,o],4,0)},c.prototype._handleResult=function(e){(a.prototype._handleResult.call(this,e),r(e))||o(this,e)},c.prototype._handleRetrieveResult=function(e,t){a.prototype._handleRetrieveResult.call(this,e,t),s(this,e,t)},c.newObject=function(n){return t(e.TelemetryService,n,"Microsoft.Telemetry.TelemetryService",!1,4)},c.prototype.toJSON=function(){return i(this,{},{})},c}(OfficeExtension.ClientObject);e.TelemetryService=a,function(e){e.unset="Unset",e.string="String",e.boolean="Boolean",e.int64="Int64",e.double="Double"}(e.DataFieldType||(e.DataFieldType={})),function(e){e.generalException="GeneralException"}(e.TelemetryErrorCodes||(e.TelemetryErrorCodes={}))}(OfficeCore||(OfficeCore={})),function(e){var t,n=function(){function e(){}return e.GetAuthContextAsyncMissing="GetAuthContextAsyncMissing",e.CannotGetAuthContext="CannotGetAuthContext",e.PackageNotLoaded="PackageNotLoaded",e.FailedToLoad="FailedToLoad",e}();function r(r){return new OfficeExtension.CoreUtility.Promise((function(i,o){if(OSF.WebAuth&&"web"==OSF._OfficeAppFactory.getHostInfo().hostPlatform)try{Office&&Office.context&&Office.context.webAuth||o({code:n.GetAuthContextAsyncMissing,message:"undefined"!==typeof Strings&&Strings.OfficeOM.L_ImplicitGetAuthContextMissing?Strings.OfficeOM.L_ImplicitGetAuthContextMissing:""}),Office.context.webAuth.getAuthContextAsync((function(s){if("succeeded"===s.status){!0;var a=s.value;if(!a||a.isAnonymous)return void o({code:n.CannotGetAuthContext,message:"undefined"!==typeof Strings&&Strings.OfficeOM.L_ImplicitGetAuthContextMissing?Strings.OfficeOM.L_ImplicitGetAuthContextMissing:""});var c="msa"===a.authorityType.toLowerCase();OSF.WebAuth.config={idp:a.authorityType.toLowerCase(),appIds:[c&&a.msaAppId?a.msaAppId:a.appId],authority:e.authorityOverride?e.authorityOverride:a.authority,redirectUri:r||null,upn:a.upn,enableConsoleLogging:e.debugging,telemetryInstance:"otel",telemetry:{HashedUserId:a.userId}};var u=!1;!function(t,n){e.debugging&&console.log("Logging Implicit load event");"undefined"!==typeof OTel&&OTel.OTelLogger.onTelemetryLoaded((function(){var e=[oteljs.makeStringDataField("IdentityProvider",OSF.WebAuth.config.idp),oteljs.makeStringDataField("AppId",OSF.WebAuth.config.appIds[0]),oteljs.makeBooleanDataField("Js","undefined"!==typeof Implicit),oteljs.makeBooleanDataField("Result",n)];if(OSF.WebAuth.config.telemetry)for(var r in OSF.WebAuth.config.telemetry)e.push(oteljs.makeStringDataField(r,OSF.WebAuth.config.telemetry[r]));if(t&&t.Telemetry)for(var r in t.Telemetry)if(t.Telemetry[r])switch(r){case"succeeded":e.push(oteljs.makeBooleanDataField(r,t.Telemetry[r]));break;case"loadedApplicationCount":case"timeToLoad":e.push(oteljs.makeInt64DataField(r,t.Telemetry[r]));break;default:e.push(oteljs.makeStringDataField(r,t.Telemetry[r]))}OTel.OTelLogger.sendTelemetryEvent({eventName:"Office.Extensibility.OfficeJs.OfficeFirstPartyAuth.Load",dataFields:e,eventFlags:{dataCategories:oteljs.DataCategories.ProductServiceUsage}})}))}(OSF.WebAuth.load((function(e){e&&(u=!0,i()),o({code:n.PackageNotLoaded,message:"undefined"!==typeof Strings&&Strings.OfficeOM.L_ImplicitNotLoaded?Strings.OfficeOM.L_ImplicitNotLoaded:""})})),u);var l=r||window.location.href.split("?")[0],p=sessionStorage.getItem("officeWebAuthReplyUrls");p||""===p?p=l:p+=", "+l,sessionStorage.setItem("officeWebAuthReplyUrls",p)}else!1,OSF.WebAuth.config=null,t=JSON.stringify(s),o({code:n.FailedToLoad,message:t})}))}catch(e){!1,OSF.WebAuth.config=null,t=e,OSF.WebAuth.load((function(e){e&&i(),o({code:n.FailedToLoad,message:t})}))}else i()}))}function i(t,n,r,i,o){e.debugging&&console.log("Logging Implicit acquire event"),"undefined"!==typeof OTel&&OTel.OTelLogger.onTelemetryLoaded((function(){var e=[oteljs.makeStringDataField("IdentityProvider",OSF.WebAuth.config.idp),oteljs.makeStringDataField("AppId",OSF.WebAuth.config.appIds[0]),oteljs.makeStringDataField("Target",r),oteljs.makeBooleanDataField("Popup","boolean"===typeof i&&i),oteljs.makeBooleanDataField("Result",n),oteljs.makeStringDataField("Error",o)];if(OSF.WebAuth.config.telemetry)for(var s in OSF.WebAuth.config.telemetry)e.push(oteljs.makeStringDataField(s,OSF.WebAuth.config.telemetry[s]));if(t&&t.Telemetry)for(var s in t.Telemetry)if(t.Telemetry[s])switch(s){case"succeeded":e.push(oteljs.makeBooleanDataField(s,t.Telemetry[s]));break;case"timeToGetToken":e.push(oteljs.makeInt64DataField(s,t.Telemetry[s]));break;default:e.push(oteljs.makeStringDataField(s,t.Telemetry[s]))}OTel.OTelLogger.sendTelemetryEvent({eventName:"Office.Extensibility.OfficeJs.OfficeFirstPartyAuth.GetAccessToken",dataFields:e,eventFlags:{dataCategories:oteljs.DataCategories.ProductServiceUsage}})}))}e.debugging=!1,e.load=r,e.getAccessToken=function(t,n){return new OfficeExtension.CoreUtility.Promise((function(r,o){if("web"==OSF._OfficeAppFactory.getHostInfo().hostPlatform)Office.context.webAuth.getAuthContextAsync((function(s){var a=!1;"succeeded"===s.status&&(!0,s.value.supportsAuthToken&&(a=!0));if(a){var c=new OfficeCore.RequestContext,u=OfficeCore.AuthenticationService.newObject(c);c._customData="WacPartition";var l=u.getAccessToken(t,null);c.sync().then((function(){r(l.value)}))}else if(OSF.WebAuth&&OSF.WebAuth.loaded){n&&n.forceRefresh&&OSF.WebAuth.clearCache();var p="msa"==OSF.WebAuth.config.idp.toLowerCase()?OfficeCore.IdentityType.microsoftAccount:OfficeCore.IdentityType.organizationAccount;OSF.WebAuth.config.appIds[0]&&OSF.WebAuth.getToken(t.resource,OSF.WebAuth.config.appIds[0],OSF._OfficeAppFactory.getHostInfo().osfControlAppCorrelationId,n&&n.popup?n.popup:null).then((function(e){i(e,!0,t.resource,n&&n.popup?n.popup:null),r({accessToken:e.Token,tokenIdenityType:p})})).catch((function(e){i(e,!1,t.resource,n&&n.popup?n.popup:null,e.ErrorCode),o({code:e.ErrorCode,message:e.ErrorMessage})}))}else!function(t,n){e.debugging&&console.log("Logging Implicit unexpected acquire event");"undefined"!==typeof OTel&&OTel.OTelLogger.onTelemetryLoaded((function(){var e=[oteljs.makeBooleanDataField("Loaded",t),oteljs.makeInt64DataField("LoadAttempts",n)];OTel.OTelLogger.sendTelemetryEvent({eventName:"Office.Extensibility.OfficeJs.OfficeFirstPartyAuth.UnexpectedAcquire",dataFields:e,eventFlags:{dataCategories:oteljs.DataCategories.ProductServiceUsage}})}))}(OSF.WebAuth.loaded,OSF.WebAuth.loadAttempts)}));else{var s=new OfficeCore.RequestContext,a=OfficeCore.AuthenticationService.newObject(s),c=a.onTokenReceived.add((function(e){if(!OfficeExtension.CoreUtility.isNullOrUndefined(e))if(c.remove(),s.sync().catch((function(){})),0==e.code)r(e.tokenValue);else if(OfficeExtension.CoreUtility.isNullOrUndefined(e.errorInfo))o({code:e.code});else try{o(JSON.parse(e.errorInfo))}catch(t){o({code:e.code,message:e.errorInfo})}return null}));s.sync().then((function(){var e=a.getAccessToken(t,a._targetId);return s.sync().then((function(){if(OfficeExtension.CoreUtility.isNullOrUndefined(e.value))return null;var t=e.value.accessToken;OfficeExtension.CoreUtility.isNullOrUndefined(t)||r(e.value)}))})).catch((function(e){o(e)}))}}))},e.getPrimaryIdentityInfo=function(){var e=new OfficeCore.RequestContext,t=OfficeCore.AuthenticationService.newObject(e);e._customData="WacPartition";var n=t.getPrimaryIdentityInfo();return e.sync().then((function(){return n.value}))},e.getIdentities=function(){var e=new OfficeCore.RequestContext,t=OfficeCore.AuthenticationService.newObject(e).getIdentities();return e.sync().then((function(){return t.value}))},"undefined"!==typeof window&&window.OSF&&function(){try{if("undefined"===typeof window||!window.sessionStorage)return;var e=sessionStorage.getItem("officeWebAuthReplyUrls");null!==e&&-1!==e.indexOf(window.location.origin+window.location.pathname)&&r()}catch(e){console.error(e)}}()}(OfficeFirstPartyAuth||(OfficeFirstPartyAuth={})),function(e){var t=OfficeExtension.BatchApiHelper.createPropertyObject,n=OfficeExtension.BatchApiHelper.createMethodObject,r=OfficeExtension.BatchApiHelper.createIndexerObject,i=(OfficeExtension.BatchApiHelper.createRootServiceObject,OfficeExtension.BatchApiHelper.createTopLevelServiceObject),o=OfficeExtension.BatchApiHelper.createChildItemObject,s=OfficeExtension.BatchApiHelper.invokeMethod,a=OfficeExtension.BatchApiHelper.invokeEnsureUnchanged,c=OfficeExtension.BatchApiHelper.invokeSetProperty,u=OfficeExtension.Utility.isNullOrUndefined,l=OfficeExtension.Utility.isUndefined,p=OfficeExtension.Utility.throwIfNotLoaded,f=OfficeExtension.Utility.throwIfApiNotSupported,h=OfficeExtension.Utility.load,d=OfficeExtension.Utility.retrieve,m=OfficeExtension.Utility.toJson,y=OfficeExtension.Utility.fixObjectPathIfNecessary,g=OfficeExtension.Utility._handleNavigationPropertyResults,b=OfficeExtension.Utility.adjustToDateTime,_=OfficeExtension.Utility.processRetrieveResult,v=OfficeExtension.Utility.setMockData;OfficeExtension.CommonUtility.calculateApiFlags;!function(e){e.organizationAccount="OrganizationAccount",e.microsoftAccount="MicrosoftAccount",e.unsupported="Unsupported"}(e.IdentityType||(e.IdentityType={}));var O=function(n){function r(){return null!==n&&n.apply(this,arguments)||this}return __extends(r,n),Object.defineProperty(r.prototype,"_className",{get:function(){return"AuthenticationService"},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"_navigationPropertyNames",{get:function(){return["roamingSettings"]},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"roamingSettings",{get:function(){return this._R||(this._R=t(e.RoamingSettingCollection,this,"RoamingSettings",!1,4)),this._R},enumerable:!0,configurable:!0}),r.prototype.getAccessToken=function(e,t){return s(this,"GetAccessToken",1,[e,t],5,0)},r.prototype.getIdentities=function(){return f("AuthenticationService.getIdentities","FirstPartyAuthentication","1.3","Office"),s(this,"GetIdentities",1,[],5,0)},r.prototype.getPrimaryIdentityInfo=function(){return f("AuthenticationService.getPrimaryIdentityInfo","FirstPartyAuthentication","1.2","Office"),s(this,"GetPrimaryIdentityInfo",1,[],5,0)},r.prototype._handleResult=function(e){if(n.prototype._handleResult.call(this,e),!u(e)){var t=e;y(this,t),g(this,t,["roamingSettings","RoamingSettings"])}},r.prototype.load=function(e){return h(this,e)},r.prototype.retrieve=function(e){return d(this,e)},r.prototype._handleRetrieveResult=function(e,t){n.prototype._handleRetrieveResult.call(this,e,t),_(this,e,t)},r.newObject=function(t){return i(e.AuthenticationService,t,"Microsoft.Authentication.AuthenticationService",!1,4)},Object.defineProperty(r.prototype,"onTokenReceived",{get:function(){var e=this;return f("AuthenticationService.onTokenReceived","FirstPartyAuthentication","1.2","Office"),this.m_tokenReceived||(this.m_tokenReceived=new OfficeExtension.GenericEventHandlers(this.context,this,"TokenReceived",{eventType:3001,registerFunc:function(){return OfficeExtension.Utility._createPromiseFromResult(null)},unregisterFunc:function(){return OfficeExtension.Utility._createPromiseFromResult(null)},getTargetIdFunc:function(){return e._targetId},eventArgsTransformFunc:function(t){var n=P.AuthenticationService_TokenReceived_EventArgsTransform(e,t);return OfficeExtension.Utility._createPromiseFromResult(n)}})),this.m_tokenReceived},enumerable:!0,configurable:!0}),r.prototype.toJSON=function(){return m(this,{},{})},r}(OfficeExtension.ClientObject);e.AuthenticationService=O;var P,I=function(){function e(){}return Object.defineProperty(e.prototype,"_targetId",{get:function(){return void 0==this.m_targetId&&("undefined"!==typeof OSF&&OSF.OUtil?this.m_targetId=OSF.OUtil.Guid.generateNewGuid():this.m_targetId=""+this.context._nextId()),this.m_targetId},enumerable:!0,configurable:!0}),e}();e.AuthenticationServiceCustom=I,OfficeExtension.Utility.applyMixin(O,I),function(e){e.AuthenticationService_TokenReceived_EventArgsTransform=function(e,t){var n=t;return{tokenValue:n.tokenValue,code:n.code,errorInfo:n.errorInfo}}}(P=e._CC||(e._CC={}));var R=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return __extends(t,e),Object.defineProperty(t.prototype,"_className",{get:function(){return"RoamingSetting"},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_scalarPropertyNames",{get:function(){return["id","value"]},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_scalarPropertyOriginalNames",{get:function(){return["Id","Value"]},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_scalarPropertyUpdateable",{get:function(){return[!1,!0]},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"id",{get:function(){return p("id",this._I,"RoamingSetting",this._isNull),this._I},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"value",{get:function(){return p("value",this._V,"RoamingSetting",this._isNull),this._V},set:function(e){this._V=e,c(this,"Value",e,0)},enumerable:!0,configurable:!0}),t.prototype.set=function(e,t){this._recursivelySet(e,t,["value"],[],[])},t.prototype.update=function(e){this._recursivelyUpdate(e)},t.prototype._handleResult=function(t){if(e.prototype._handleResult.call(this,t),!u(t)){var n=t;y(this,n),l(n.Id)||(this._I=n.Id),l(n.Value)||(this._V=n.Value)}},t.prototype.load=function(e){return h(this,e)},t.prototype.retrieve=function(e){return d(this,e)},t.prototype._handleIdResult=function(t){e.prototype._handleIdResult.call(this,t),u(t)||l(t.Id)||(this._I=t.Id)},t.prototype._handleRetrieveResult=function(t,n){e.prototype._handleRetrieveResult.call(this,t,n),_(this,t,n)},t.prototype.toJSON=function(){return m(this,{id:this._I,value:this._V},{})},t.prototype.setMockData=function(e){v(this,e)},t.prototype.ensureUnchanged=function(e){a(this,e)},t}(OfficeExtension.ClientObject);e.RoamingSetting=R;var j=function(t){function r(){return null!==t&&t.apply(this,arguments)||this}return __extends(r,t),Object.defineProperty(r.prototype,"_className",{get:function(){return"RoamingSettingCollection"},enumerable:!0,configurable:!0}),r.prototype.getItem=function(t){return n(e.RoamingSetting,this,"GetItem",1,[t],!1,!1,null,4)},r.prototype.getItemOrNullObject=function(t){return n(e.RoamingSetting,this,"GetItemOrNullObject",1,[t],!1,!1,null,4)},r.prototype._handleResult=function(e){(t.prototype._handleResult.call(this,e),u(e))||y(this,e)},r.prototype._handleRetrieveResult=function(e,n){t.prototype._handleRetrieveResult.call(this,e,n),_(this,e,n)},r.prototype.toJSON=function(){return m(this,{},{})},r}(OfficeExtension.ClientObject);e.RoamingSettingCollection=j,function(e){e.ariaBrowserPipeUrl="AriaBrowserPipeUrl",e.ariaUploadUrl="AriaUploadUrl",e.ariaVNextUploadUrl="AriaVNextUploadUrl",e.lokiAutoDiscoverUrl="LokiAutoDiscoverUrl"}(e.ServiceProvider||(e.ServiceProvider={}));var A=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return __extends(n,t),Object.defineProperty(n.prototype,"_className",{get:function(){return"ServiceUrlProvider"},enumerable:!0,configurable:!0}),n.prototype.getServiceUrl=function(e,t){return s(this,"GetServiceUrl",1,[e,t],4,0)},n.prototype._handleResult=function(e){(t.prototype._handleResult.call(this,e),u(e))||y(this,e)},n.prototype._handleRetrieveResult=function(e,n){t.prototype._handleRetrieveResult.call(this,e,n),_(this,e,n)},n.newObject=function(t){return i(e.ServiceUrlProvider,t,"Microsoft.DesktopCompliance.ServiceUrlProvider",!1,4)},n.prototype.toJSON=function(){return m(this,{},{})},n}(OfficeExtension.ClientObject);e.ServiceUrlProvider=A;var C=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return __extends(n,t),Object.defineProperty(n.prototype,"_className",{get:function(){return"LinkedIn"},enumerable:!0,configurable:!0}),n.prototype.isEnabledForOffice=function(){return s(this,"IsEnabledForOffice",1,[],4,0)},n.prototype.recordLinkedInSettingsCompliance=function(e,t){s(this,"RecordLinkedInSettingsCompliance",0,[e,t],0,0)},n.prototype._handleResult=function(e){(t.prototype._handleResult.call(this,e),u(e))||y(this,e)},n.prototype._handleRetrieveResult=function(e,n){t.prototype._handleRetrieveResult.call(this,e,n),_(this,e,n)},n.newObject=function(t){return i(e.LinkedIn,t,"Microsoft.DesktopCompliance.LinkedIn",!1,4)},n.prototype.toJSON=function(){return m(this,{},{})},n}(OfficeExtension.ClientObject);e.LinkedIn=C;var S=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return __extends(n,t),Object.defineProperty(n.prototype,"_className",{get:function(){return"NetworkUsage"},enumerable:!0,configurable:!0}),n.prototype.isInDisconnectedMode=function(){return s(this,"IsInDisconnectedMode",1,[],4,0)},n.prototype.isInOnlineMode=function(){return s(this,"IsInOnlineMode",1,[],4,0)},n.prototype._handleResult=function(e){(t.prototype._handleResult.call(this,e),u(e))||y(this,e)},n.prototype._handleRetrieveResult=function(e,n){t.prototype._handleRetrieveResult.call(this,e,n),_(this,e,n)},n.newObject=function(t){return i(e.NetworkUsage,t,"Microsoft.DesktopCompliance.NetworkUsage",!1,4)},n.prototype.toJSON=function(){return m(this,{},{})},n}(OfficeExtension.ClientObject);e.NetworkUsage=S;var E=function(r){function o(){return null!==r&&r.apply(this,arguments)||this}return __extends(o,r),Object.defineProperty(o.prototype,"_className",{get:function(){return"DynamicRibbon"},enumerable:!0,configurable:!0}),Object.defineProperty(o.prototype,"_navigationPropertyNames",{get:function(){return["buttons"]},enumerable:!0,configurable:!0}),Object.defineProperty(o.prototype,"buttons",{get:function(){return this._B||(this._B=t(e.RibbonButtonCollection,this,"Buttons",!0,4)),this._B},enumerable:!0,configurable:!0}),o.prototype.executeRequestCreate=function(e){f("DynamicRibbon.executeRequestCreate","DynamicRibbon","1.2","Office"),s(this,"ExecuteRequestCreate",1,[e],4,0)},o.prototype.executeRequestUpdate=function(e){s(this,"ExecuteRequestUpdate",1,[e],4,0)},o.prototype.getButton=function(t){return n(e.RibbonButton,this,"GetButton",1,[t],!1,!1,null,4)},o.prototype.getTab=function(t){return n(e.RibbonTab,this,"GetTab",1,[t],!1,!1,null,4)},o.prototype._handleResult=function(e){if(r.prototype._handleResult.call(this,e),!u(e)){var t=e;y(this,t),g(this,t,["buttons","Buttons"])}},o.prototype.load=function(e){return h(this,e)},o.prototype.retrieve=function(e){return d(this,e)},o.prototype._handleRetrieveResult=function(e,t){r.prototype._handleRetrieveResult.call(this,e,t),_(this,e,t)},o.newObject=function(t){return i(e.DynamicRibbon,t,"Microsoft.DynamicRibbon.DynamicRibbon",!1,4)},o.prototype.toJSON=function(){return m(this,{},{buttons:this._B})},o}(OfficeExtension.ClientObject);e.DynamicRibbon=E;var x=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return __extends(t,e),Object.defineProperty(t.prototype,"_className",{get:function(){return"RibbonTab"},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_scalarPropertyNames",{get:function(){return["id"]},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_scalarPropertyOriginalNames",{get:function(){return["Id"]},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"id",{get:function(){return p("id",this._I,"RibbonTab",this._isNull),this._I},enumerable:!0,configurable:!0}),t.prototype.setVisibility=function(e){s(this,"SetVisibility",0,[e],0,0)},t.prototype._handleResult=function(t){if(e.prototype._handleResult.call(this,t),!u(t)){var n=t;y(this,n),l(n.Id)||(this._I=n.Id)}},t.prototype.load=function(e){return h(this,e)},t.prototype.retrieve=function(e){return d(this,e)},t.prototype._handleIdResult=function(t){e.prototype._handleIdResult.call(this,t),u(t)||l(t.Id)||(this._I=t.Id)},t.prototype._handleRetrieveResult=function(t,n){e.prototype._handleRetrieveResult.call(this,t,n),_(this,t,n)},t.prototype.toJSON=function(){return m(this,{id:this._I},{})},t.prototype.setMockData=function(e){v(this,e)},t.prototype.ensureUnchanged=function(e){a(this,e)},t}(OfficeExtension.ClientObject);e.RibbonTab=x;var N=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return __extends(t,e),Object.defineProperty(t.prototype,"_className",{get:function(){return"RibbonButton"},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_scalarPropertyNames",{get:function(){return["id","enabled","label"]},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_scalarPropertyOriginalNames",{get:function(){return["Id","Enabled","Label"]},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_scalarPropertyUpdateable",{get:function(){return[!1,!0,!1]},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"enabled",{get:function(){return p("enabled",this._E,"RibbonButton",this._isNull),this._E},set:function(e){this._E=e,c(this,"Enabled",e,0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"id",{get:function(){return p("id",this._I,"RibbonButton",this._isNull),this._I},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"label",{get:function(){return p("label",this._L,"RibbonButton",this._isNull),this._L},enumerable:!0,configurable:!0}),t.prototype.set=function(e,t){this._recursivelySet(e,t,["enabled"],[],[])},t.prototype.update=function(e){this._recursivelyUpdate(e)},t.prototype.setEnabled=function(e){s(this,"SetEnabled",0,[e],0,0)},t.prototype._handleResult=function(t){if(e.prototype._handleResult.call(this,t),!u(t)){var n=t;y(this,n),l(n.Enabled)||(this._E=n.Enabled),l(n.Id)||(this._I=n.Id),l(n.Label)||(this._L=n.Label)}},t.prototype.load=function(e){return h(this,e)},t.prototype.retrieve=function(e){return d(this,e)},t.prototype._handleIdResult=function(t){e.prototype._handleIdResult.call(this,t),u(t)||l(t.Id)||(this._I=t.Id)},t.prototype._handleRetrieveResult=function(t,n){e.prototype._handleRetrieveResult.call(this,t,n),_(this,t,n)},t.prototype.toJSON=function(){return m(this,{enabled:this._E,id:this._I,label:this._L},{})},t.prototype.setMockData=function(e){v(this,e)},t.prototype.ensureUnchanged=function(e){a(this,e)},t}(OfficeExtension.ClientObject);e.RibbonButton=N;var T=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return __extends(n,t),Object.defineProperty(n.prototype,"_className",{get:function(){return"RibbonButtonCollection"},enumerable:!0,configurable:!0}),Object.defineProperty(n.prototype,"_isCollection",{get:function(){return!0},enumerable:!0,configurable:!0}),Object.defineProperty(n.prototype,"items",{get:function(){return p("items",this.m__items,"RibbonButtonCollection",this._isNull),this.m__items},enumerable:!0,configurable:!0}),n.prototype.getCount=function(){return s(this,"GetCount",1,[],4,0)},n.prototype.getItem=function(t){return r(e.RibbonButton,this,[t])},n.prototype._handleResult=function(n){if(t.prototype._handleResult.call(this,n),!u(n)){var r=n;if(y(this,r),!u(r[OfficeExtension.Constants.items])){this.m__items=[];for(var i=r[OfficeExtension.Constants.items],s=0;s<i.length;s++){var a=o(e.RibbonButton,!0,this,i[s],s);a._handleResult(i[s]),this.m__items.push(a)}}}},n.prototype.load=function(e){return h(this,e)},n.prototype.retrieve=function(e){return d(this,e)},n.prototype._handleRetrieveResult=function(n,r){var i=this;t.prototype._handleRetrieveResult.call(this,n,r),_(this,n,r,(function(t,n){return o(e.RibbonButton,!0,i,t,n)}))},n.prototype.toJSON=function(){return m(this,{},{},this.m__items)},n.prototype.setMockData=function(t){var n=this;v(this,t,(function(t,r){return o(e.RibbonButton,!0,n,t,r)}),(function(e){return n.m__items=e}))},n}(OfficeExtension.ClientObject);e.RibbonButtonCollection=T,function(e){e.shortTime="ShortTime",e.longTime="LongTime",e.shortDate="ShortDate",e.longDate="LongDate"}(e.TimeStringFormat||(e.TimeStringFormat={}));var w=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return __extends(n,t),Object.defineProperty(n.prototype,"_className",{get:function(){return"LocaleApi"},enumerable:!0,configurable:!0}),n.prototype.formatDateTimeString=function(e,t,n){return s(this,"FormatDateTimeString",1,[e,t,n],4,0)},n.prototype.getLocaleDateTimeFormattingInfo=function(e){return s(this,"GetLocaleDateTimeFormattingInfo",1,[e],4,0)},n.prototype._handleResult=function(e){(t.prototype._handleResult.call(this,e),u(e))||y(this,e)},n.prototype._handleRetrieveResult=function(e,n){t.prototype._handleRetrieveResult.call(this,e,n),_(this,e,n)},n.newObject=function(t){return i(e.LocaleApi,t,"Microsoft.LocaleApi.LocaleApi",!1,4)},n.prototype.toJSON=function(){return m(this,{},{})},n}(OfficeExtension.ClientObject);e.LocaleApi=w;var U=function(r){function i(){return null!==r&&r.apply(this,arguments)||this}return __extends(i,r),Object.defineProperty(i.prototype,"_className",{get:function(){return"Comment"},enumerable:!0,configurable:!0}),Object.defineProperty(i.prototype,"_scalarPropertyNames",{get:function(){return["id","text","created","level","resolved","author","mentions"]},enumerable:!0,configurable:!0}),Object.defineProperty(i.prototype,"_scalarPropertyOriginalNames",{get:function(){return["Id","Text","Created","Level","Resolved","Author","Mentions"]},enumerable:!0,configurable:!0}),Object.defineProperty(i.prototype,"_scalarPropertyUpdateable",{get:function(){return[!1,!0,!1,!1,!0,!1,!1]},enumerable:!0,configurable:!0}),Object.defineProperty(i.prototype,"_navigationPropertyNames",{get:function(){return["parent","parentOrNullObject","replies"]},enumerable:!0,configurable:!0}),Object.defineProperty(i.prototype,"parent",{get:function(){return this._P||(this._P=t(e.Comment,this,"Parent",!1,4)),this._P},enumerable:!0,configurable:!0}),Object.defineProperty(i.prototype,"parentOrNullObject",{get:function(){return this._Pa||(this._Pa=t(e.Comment,this,"ParentOrNullObject",!1,4)),this._Pa},enumerable:!0,configurable:!0}),Object.defineProperty(i.prototype,"replies",{get:function(){return this._R||(this._R=t(e.CommentCollection,this,"Replies",!0,4)),this._R},enumerable:!0,configurable:!0}),Object.defineProperty(i.prototype,"author",{get:function(){return p("author",this._A,"Comment",this._isNull),this._A},enumerable:!0,configurable:!0}),Object.defineProperty(i.prototype,"created",{get:function(){return p("created",this._C,"Comment",this._isNull),this._C},enumerable:!0,configurable:!0}),Object.defineProperty(i.prototype,"id",{get:function(){return p("id",this._I,"Comment",this._isNull),this._I},enumerable:!0,configurable:!0}),Object.defineProperty(i.prototype,"level",{get:function(){return p("level",this._L,"Comment",this._isNull),this._L},enumerable:!0,configurable:!0}),Object.defineProperty(i.prototype,"mentions",{get:function(){return p("mentions",this._M,"Comment",this._isNull),this._M},enumerable:!0,configurable:!0}),Object.defineProperty(i.prototype,"resolved",{get:function(){return p("resolved",this._Re,"Comment",this._isNull),this._Re},set:function(e){this._Re=e,c(this,"Resolved",e,0)},enumerable:!0,configurable:!0}),Object.defineProperty(i.prototype,"text",{get:function(){return p("text",this._T,"Comment",this._isNull),this._T},set:function(e){this._T=e,c(this,"Text",e,0)},enumerable:!0,configurable:!0}),i.prototype.set=function(e,t){this._recursivelySet(e,t,["text","resolved"],[],["parent","parentOrNullObject","replies"])},i.prototype.update=function(e){this._recursivelyUpdate(e)},i.prototype.delete=function(){s(this,"Delete",0,[],0,0)},i.prototype.getParentOrSelf=function(){return n(e.Comment,this,"GetParentOrSelf",1,[],!1,!1,null,4)},i.prototype.getRichText=function(e){return s(this,"GetRichText",1,[e],4,0)},i.prototype.reply=function(t,r){return n(e.Comment,this,"Reply",0,[t,r],!1,!1,null,0)},i.prototype.setRichText=function(e,t){return s(this,"SetRichText",0,[e,t],0,0)},i.prototype._handleResult=function(e){if(r.prototype._handleResult.call(this,e),!u(e)){var t=e;y(this,t),l(t.Author)||(this._A=t.Author),l(t.Created)||(this._C=b(t.Created)),l(t.Id)||(this._I=t.Id),l(t.Level)||(this._L=t.Level),l(t.Mentions)||(this._M=t.Mentions),l(t.Resolved)||(this._Re=t.Resolved),l(t.Text)||(this._T=t.Text),g(this,t,["parent","Parent","parentOrNullObject","ParentOrNullObject","replies","Replies"])}},i.prototype.load=function(e){return h(this,e)},i.prototype.retrieve=function(e){return d(this,e)},i.prototype._handleIdResult=function(e){r.prototype._handleIdResult.call(this,e),u(e)||l(e.Id)||(this._I=e.Id)},i.prototype._handleRetrieveResult=function(e,t){if(r.prototype._handleRetrieveResult.call(this,e,t),!u(e)){var n=e;l(n.Created)||(n.created=b(n.created)),_(this,e,t)}},i.prototype.toJSON=function(){return m(this,{author:this._A,created:this._C,id:this._I,level:this._L,mentions:this._M,resolved:this._Re,text:this._T},{replies:this._R})},i.prototype.setMockData=function(e){v(this,e)},i.prototype.ensureUnchanged=function(e){a(this,e)},i}(OfficeExtension.ClientObject);e.Comment=U;var F=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return __extends(n,t),Object.defineProperty(n.prototype,"_className",{get:function(){return"CommentCollection"},enumerable:!0,configurable:!0}),Object.defineProperty(n.prototype,"_isCollection",{get:function(){return!0},enumerable:!0,configurable:!0}),Object.defineProperty(n.prototype,"items",{get:function(){return p("items",this.m__items,"CommentCollection",this._isNull),this.m__items},enumerable:!0,configurable:!0}),n.prototype.getCount=function(){return s(this,"GetCount",1,[],4,0)},n.prototype.getItem=function(t){return r(e.Comment,this,[t])},n.prototype._handleResult=function(n){if(t.prototype._handleResult.call(this,n),!u(n)){var r=n;if(y(this,r),!u(r[OfficeExtension.Constants.items])){this.m__items=[];for(var i=r[OfficeExtension.Constants.items],s=0;s<i.length;s++){var a=o(e.Comment,!0,this,i[s],s);a._handleResult(i[s]),this.m__items.push(a)}}}},n.prototype.load=function(e){return h(this,e)},n.prototype.retrieve=function(e){return d(this,e)},n.prototype._handleRetrieveResult=function(n,r){var i=this;t.prototype._handleRetrieveResult.call(this,n,r),_(this,n,r,(function(t,n){return o(e.Comment,!0,i,t,n)}))},n.prototype.toJSON=function(){return m(this,{},{},this.m__items)},n.prototype.setMockData=function(t){var n=this;v(this,t,(function(t,r){return o(e.Comment,!0,n,t,r)}),(function(e){return n.m__items=e}))},n}(OfficeExtension.ClientObject);e.CommentCollection=F,function(e){e.plain="Plain",e.markdown="Markdown",e.delta="Delta"}(e.CommentTextFormat||(e.CommentTextFormat={})),function(e){e.placeHolderRendered="PlaceHolderRendered",e.initialCardRendered="InitialCardRendered"}(e.PersonaCardPerfPoint||(e.PersonaCardPerfPoint={})),function(e){e.notSet="NotSet",e.free="Free",e.idle="Idle",e.busy="Busy",e.idleBusy="IdleBusy",e.doNotDisturb="DoNotDisturb",e.unalertable="Unalertable",e.unavailable="Unavailable"}(e.UnifiedCommunicationAvailability||(e.UnifiedCommunicationAvailability={})),function(e){e.online="Online",e.notOnline="NotOnline",e.away="Away",e.busy="Busy",e.beRightBack="BeRightBack",e.onThePhone="OnThePhone",e.outToLunch="OutToLunch",e.inAMeeting="InAMeeting",e.outOfOffice="OutOfOffice",e.doNotDisturb="DoNotDisturb",e.inAConference="InAConference",e.getting="Getting",e.notABuddy="NotABuddy",e.disconnected="Disconnected",e.notInstalled="NotInstalled",e.urgentInterruptionsOnly="UrgentInterruptionsOnly",e.mayBeAvailable="MayBeAvailable",e.idle="Idle",e.inPresentation="InPresentation"}(e.UnifiedCommunicationStatus||(e.UnifiedCommunicationStatus={})),function(e){e.free="Free",e.busy="Busy",e.idle="Idle",e.doNotDistrub="DoNotDistrub",e.blocked="Blocked",e.notSet="NotSet",e.outOfOffice="OutOfOffice"}(e.UnifiedCommunicationPresence||(e.UnifiedCommunicationPresence={})),function(e){e.unknown="Unknown",e.free="Free",e.busy="Busy",e.elsewhere="Elsewhere",e.tentative="Tentative",e.outOfOffice="OutOfOffice"}(e.FreeBusyCalendarState||(e.FreeBusyCalendarState={})),function(e){e.unknown="Unknown",e.enterprise="Enterprise",e.contact="Contact",e.bot="Bot",e.phoneOnly="PhoneOnly",e.oneOff="OneOff",e.distributionList="DistributionList",e.personalDistributionList="PersonalDistributionList",e.anonymous="Anonymous",e.unifiedGroup="UnifiedGroup"}(e.PersonaType||(e.PersonaType={})),function(e){e.workPhone="WorkPhone",e.homePhone="HomePhone",e.mobilePhone="MobilePhone",e.businessFax="BusinessFax",e.otherPhone="OtherPhone"}(e.PhoneType||(e.PhoneType={})),function(e){e.workAddress="WorkAddress",e.homeAddress="HomeAddress",e.otherAddress="OtherAddress"}(e.AddressType||(e.AddressType={})),function(e){e.unknown="Unknown",e.individual="Individual",e.group="Group"}(e.MemberType||(e.MemberType={}));var k,D=function(t){function r(){return null!==t&&t.apply(this,arguments)||this}return __extends(r,t),Object.defineProperty(r.prototype,"_className",{get:function(){return"MemberInfoList"},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"_scalarPropertyNames",{get:function(){return["isWarmedUp","isWarmingUp"]},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"_scalarPropertyOriginalNames",{get:function(){return["IsWarmedUp","IsWarmingUp"]},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"isWarmedUp",{get:function(){return p("isWarmedUp",this._I,"MemberInfoList",this._isNull),this._I},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"isWarmingUp",{get:function(){return p("isWarmingUp",this._Is,"MemberInfoList",this._isNull),this._Is},enumerable:!0,configurable:!0}),r.prototype.getPersonaForMember=function(t){return n(e.Persona,this,"GetPersonaForMember",1,[t],!1,!1,null,4)},r.prototype.items=function(){return s(this,"Items",1,[],4,0)},r.prototype._handleResult=function(e){if(t.prototype._handleResult.call(this,e),!u(e)){var n=e;y(this,n),l(n.IsWarmedUp)||(this._I=n.IsWarmedUp),l(n.IsWarmingUp)||(this._Is=n.IsWarmingUp)}},r.prototype.load=function(e){return h(this,e)},r.prototype.retrieve=function(e){return d(this,e)},r.prototype._handleRetrieveResult=function(e,n){t.prototype._handleRetrieveResult.call(this,e,n),_(this,e,n)},r.prototype.toJSON=function(){return m(this,{isWarmedUp:this._I,isWarmingUp:this._Is},{})},r.prototype.setMockData=function(e){v(this,e)},r.prototype.ensureUnchanged=function(e){a(this,e)},r}(OfficeExtension.ClientObject);e.MemberInfoList=D,function(e){e.hostId="HostId",e.type="Type",e.photo="Photo",e.personaInfo="PersonaInfo",e.unifiedCommunicationInfo="UnifiedCommunicationInfo",e.organization="Organization",e.unifiedGroupInfo="UnifiedGroupInfo",e.members="Members",e.membership="Membership",e.capabilities="Capabilities",e.customizations="Customizations",e.viewableSources="ViewableSources",e.placeholder="Placeholder"}(k=e.PersonaDataUpdated||(e.PersonaDataUpdated={}));var M=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return __extends(t,e),Object.defineProperty(t.prototype,"_className",{get:function(){return"PersonaActions"},enumerable:!0,configurable:!0}),t.prototype.addContact=function(){s(this,"AddContact",0,[],0,0)},t.prototype.callPhoneNumber=function(e){s(this,"CallPhoneNumber",0,[e],0,0)},t.prototype.composeEmail=function(e){s(this,"ComposeEmail",0,[e],0,0)},t.prototype.composeInstantMessage=function(e){s(this,"ComposeInstantMessage",0,[e],0,0)},t.prototype.editContact=function(){s(this,"EditContact",0,[],0,0)},t.prototype.editContactByIdentifier=function(e){s(this,"EditContactByIdentifier",0,[e],0,0)},t.prototype.getChangePhotoUrlAndOpenInBrowser=function(){s(this,"GetChangePhotoUrlAndOpenInBrowser",0,[],0,0)},t.prototype.hideHoverCardForPersona=function(){s(this,"HideHoverCardForPersona",0,[],0,0)},t.prototype.openGroupCalendar=function(){s(this,"OpenGroupCalendar",0,[],0,0)},t.prototype.openLinkContactUx=function(){s(this,"OpenLinkContactUx",0,[],0,0)},t.prototype.openOutlookProperties=function(){s(this,"OpenOutlookProperties",0,[],0,0)},t.prototype.pinPersonaToQuickContacts=function(){s(this,"PinPersonaToQuickContacts",0,[],0,0)},t.prototype.scheduleMeeting=function(){s(this,"ScheduleMeeting",0,[],0,0)},t.prototype.showContactCard=function(e,t,n,r,i,o){s(this,"ShowContactCard",0,[e,t,n,r,i,o],0,0)},t.prototype.showContextMenu=function(e,t,n,r,i,o){s(this,"ShowContextMenu",0,[e,t,n,r,i,o],0,0)},t.prototype.showExpandedCard=function(e,t,n,r,i,o){s(this,"ShowExpandedCard",0,[e,t,n,r,i,o],0,0)},t.prototype.showHoverCardForPersona=function(e,t,n,r,i,o){s(this,"ShowHoverCardForPersona",0,[e,t,n,r,i,o],0,0)},t.prototype.startAudioCall=function(){s(this,"StartAudioCall",0,[],0,0)},t.prototype.startVideoCall=function(){s(this,"StartVideoCall",0,[],0,0)},t.prototype.subscribeToGroup=function(){s(this,"SubscribeToGroup",0,[],0,0)},t.prototype.toggleTagForAlerts=function(){s(this,"ToggleTagForAlerts",0,[],0,0)},t.prototype.unsubscribeFromGroup=function(){s(this,"UnsubscribeFromGroup",0,[],0,0)},t.prototype._handleResult=function(t){(e.prototype._handleResult.call(this,t),u(t))||y(this,t)},t.prototype._handleRetrieveResult=function(t,n){e.prototype._handleRetrieveResult.call(this,t,n),_(this,t,n)},t.prototype.toJSON=function(){return m(this,{},{})},t}(OfficeExtension.ClientObject);e.PersonaActions=M;var L=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return __extends(t,e),Object.defineProperty(t.prototype,"_className",{get:function(){return"PersonaInfoSource"},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_scalarPropertyNames",{get:function(){return["displayName","email","emailAddresses","sipAddresses","birthday","birthdays","title","jobInfoDepartment","companyName","office","linkedTitles","linkedDepartments","linkedCompanyNames","linkedOffices","phones","addresses","webSites","notes"]},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_scalarPropertyOriginalNames",{get:function(){return["DisplayName","Email","EmailAddresses","SipAddresses","Birthday","Birthdays","Title","JobInfoDepartment","CompanyName","Office","LinkedTitles","LinkedDepartments","LinkedCompanyNames","LinkedOffices","Phones","Addresses","WebSites","Notes"]},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"addresses",{get:function(){return p("addresses",this._A,"PersonaInfoSource",this._isNull),this._A},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"birthday",{get:function(){return p("birthday",this._B,"PersonaInfoSource",this._isNull),this._B},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"birthdays",{get:function(){return p("birthdays",this._Bi,"PersonaInfoSource",this._isNull),this._Bi},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"companyName",{get:function(){return p("companyName",this._C,"PersonaInfoSource",this._isNull),this._C},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"displayName",{get:function(){return p("displayName",this._D,"PersonaInfoSource",this._isNull),this._D},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"email",{get:function(){return p("email",this._E,"PersonaInfoSource",this._isNull),this._E},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"emailAddresses",{get:function(){return p("emailAddresses",this._Em,"PersonaInfoSource",this._isNull),this._Em},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"jobInfoDepartment",{get:function(){return p("jobInfoDepartment",this._J,"PersonaInfoSource",this._isNull),this._J},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"linkedCompanyNames",{get:function(){return p("linkedCompanyNames",this._L,"PersonaInfoSource",this._isNull),this._L},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"linkedDepartments",{get:function(){return p("linkedDepartments",this._Li,"PersonaInfoSource",this._isNull),this._Li},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"linkedOffices",{get:function(){return p("linkedOffices",this._Lin,"PersonaInfoSource",this._isNull),this._Lin},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"linkedTitles",{get:function(){return p("linkedTitles",this._Link,"PersonaInfoSource",this._isNull),this._Link},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"notes",{get:function(){return p("notes",this._N,"PersonaInfoSource",this._isNull),this._N},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"office",{get:function(){return p("office",this._O,"PersonaInfoSource",this._isNull),this._O},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"phones",{get:function(){return p("phones",this._P,"PersonaInfoSource",this._isNull),this._P},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"sipAddresses",{get:function(){return p("sipAddresses",this._S,"PersonaInfoSource",this._isNull),this._S},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"title",{get:function(){return p("title",this._T,"PersonaInfoSource",this._isNull),this._T},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"webSites",{get:function(){return p("webSites",this._W,"PersonaInfoSource",this._isNull),this._W},enumerable:!0,configurable:!0}),t.prototype._handleResult=function(t){if(e.prototype._handleResult.call(this,t),!u(t)){var n=t;y(this,n),l(n.Addresses)||(this._A=n.Addresses),l(n.Birthday)||(this._B=n.Birthday),l(n.Birthdays)||(this._Bi=n.Birthdays),l(n.CompanyName)||(this._C=n.CompanyName),l(n.DisplayName)||(this._D=n.DisplayName),l(n.Email)||(this._E=n.Email),l(n.EmailAddresses)||(this._Em=n.EmailAddresses),l(n.JobInfoDepartment)||(this._J=n.JobInfoDepartment),l(n.LinkedCompanyNames)||(this._L=n.LinkedCompanyNames),l(n.LinkedDepartments)||(this._Li=n.LinkedDepartments),l(n.LinkedOffices)||(this._Lin=n.LinkedOffices),l(n.LinkedTitles)||(this._Link=n.LinkedTitles),l(n.Notes)||(this._N=n.Notes),l(n.Office)||(this._O=n.Office),l(n.Phones)||(this._P=n.Phones),l(n.SipAddresses)||(this._S=n.SipAddresses),l(n.Title)||(this._T=n.Title),l(n.WebSites)||(this._W=n.WebSites)}},t.prototype.load=function(e){return h(this,e)},t.prototype.retrieve=function(e){return d(this,e)},t.prototype._handleRetrieveResult=function(t,n){e.prototype._handleRetrieveResult.call(this,t,n),_(this,t,n)},t.prototype.toJSON=function(){return m(this,{addresses:this._A,birthday:this._B,birthdays:this._Bi,companyName:this._C,displayName:this._D,email:this._E,emailAddresses:this._Em,jobInfoDepartment:this._J,linkedCompanyNames:this._L,linkedDepartments:this._Li,linkedOffices:this._Lin,linkedTitles:this._Link,notes:this._N,office:this._O,phones:this._P,sipAddresses:this._S,title:this._T,webSites:this._W},{})},t.prototype.setMockData=function(e){v(this,e)},t.prototype.ensureUnchanged=function(e){a(this,e)},t}(OfficeExtension.ClientObject);e.PersonaInfoSource=L;var H=function(n){function r(){return null!==n&&n.apply(this,arguments)||this}return __extends(r,n),Object.defineProperty(r.prototype,"_className",{get:function(){return"PersonaInfo"},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"_scalarPropertyNames",{get:function(){return["displayName","email","emailAddresses","sipAddresses","birthday","birthdays","title","jobInfoDepartment","companyName","office","linkedTitles","linkedDepartments","linkedCompanyNames","linkedOffices","webSites","notes","isPersonResolved"]},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"_scalarPropertyOriginalNames",{get:function(){return["DisplayName","Email","EmailAddresses","SipAddresses","Birthday","Birthdays","Title","JobInfoDepartment","CompanyName","Office","LinkedTitles","LinkedDepartments","LinkedCompanyNames","LinkedOffices","WebSites","Notes","IsPersonResolved"]},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"_navigationPropertyNames",{get:function(){return["sources"]},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"sources",{get:function(){return this._So||(this._So=t(e.PersonaInfoSource,this,"Sources",!1,4)),this._So},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"birthday",{get:function(){return p("birthday",this._B,"PersonaInfo",this._isNull),this._B},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"birthdays",{get:function(){return p("birthdays",this._Bi,"PersonaInfo",this._isNull),this._Bi},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"companyName",{get:function(){return p("companyName",this._C,"PersonaInfo",this._isNull),this._C},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"displayName",{get:function(){return p("displayName",this._D,"PersonaInfo",this._isNull),this._D},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"email",{get:function(){return p("email",this._E,"PersonaInfo",this._isNull),this._E},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"emailAddresses",{get:function(){return p("emailAddresses",this._Em,"PersonaInfo",this._isNull),this._Em},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"isPersonResolved",{get:function(){return p("isPersonResolved",this._I,"PersonaInfo",this._isNull),this._I},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"jobInfoDepartment",{get:function(){return p("jobInfoDepartment",this._J,"PersonaInfo",this._isNull),this._J},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"linkedCompanyNames",{get:function(){return p("linkedCompanyNames",this._L,"PersonaInfo",this._isNull),this._L},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"linkedDepartments",{get:function(){return p("linkedDepartments",this._Li,"PersonaInfo",this._isNull),this._Li},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"linkedOffices",{get:function(){return p("linkedOffices",this._Lin,"PersonaInfo",this._isNull),this._Lin},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"linkedTitles",{get:function(){return p("linkedTitles",this._Link,"PersonaInfo",this._isNull),this._Link},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"notes",{get:function(){return p("notes",this._N,"PersonaInfo",this._isNull),this._N},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"office",{get:function(){return p("office",this._O,"PersonaInfo",this._isNull),this._O},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"sipAddresses",{get:function(){return p("sipAddresses",this._S,"PersonaInfo",this._isNull),this._S},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"title",{get:function(){return p("title",this._T,"PersonaInfo",this._isNull),this._T},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"webSites",{get:function(){return p("webSites",this._W,"PersonaInfo",this._isNull),this._W},enumerable:!0,configurable:!0}),r.prototype.getAddresses=function(){return s(this,"GetAddresses",1,[],4,0)},r.prototype.getPhones=function(){return s(this,"GetPhones",1,[],4,0)},r.prototype._handleResult=function(e){if(n.prototype._handleResult.call(this,e),!u(e)){var t=e;y(this,t),l(t.Birthday)||(this._B=b(t.Birthday)),l(t.Birthdays)||(this._Bi=b(t.Birthdays)),l(t.CompanyName)||(this._C=t.CompanyName),l(t.DisplayName)||(this._D=t.DisplayName),l(t.Email)||(this._E=t.Email),l(t.EmailAddresses)||(this._Em=t.EmailAddresses),l(t.IsPersonResolved)||(this._I=t.IsPersonResolved),l(t.JobInfoDepartment)||(this._J=t.JobInfoDepartment),l(t.LinkedCompanyNames)||(this._L=t.LinkedCompanyNames),l(t.LinkedDepartments)||(this._Li=t.LinkedDepartments),l(t.LinkedOffices)||(this._Lin=t.LinkedOffices),l(t.LinkedTitles)||(this._Link=t.LinkedTitles),l(t.Notes)||(this._N=t.Notes),l(t.Office)||(this._O=t.Office),l(t.SipAddresses)||(this._S=t.SipAddresses),l(t.Title)||(this._T=t.Title),l(t.WebSites)||(this._W=t.WebSites),g(this,t,["sources","Sources"])}},r.prototype.load=function(e){return h(this,e)},r.prototype.retrieve=function(e){return d(this,e)},r.prototype._handleRetrieveResult=function(e,t){if(n.prototype._handleRetrieveResult.call(this,e,t),!u(e)){var r=e;l(r.Birthday)||(r.birthday=b(r.birthday)),l(r.Birthdays)||(r.birthdays=b(r.birthdays)),_(this,e,t)}},r.prototype.toJSON=function(){return m(this,{birthday:this._B,birthdays:this._Bi,companyName:this._C,displayName:this._D,email:this._E,emailAddresses:this._Em,isPersonResolved:this._I,jobInfoDepartment:this._J,linkedCompanyNames:this._L,linkedDepartments:this._Li,linkedOffices:this._Lin,linkedTitles:this._Link,notes:this._N,office:this._O,sipAddresses:this._S,title:this._T,webSites:this._W},{sources:this._So})},r.prototype.setMockData=function(e){v(this,e)},r.prototype.ensureUnchanged=function(e){a(this,e)},r}(OfficeExtension.ClientObject);e.PersonaInfo=H;var B=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return __extends(t,e),Object.defineProperty(t.prototype,"_className",{get:function(){return"PersonaUnifiedCommunicationInfo"},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_scalarPropertyNames",{get:function(){return["availability","status","isSelf","isTagged","customStatusString","isBlocked","presenceTooltip","isOutOfOffice","outOfOfficeNote","timezone","meetingLocation","meetingSubject","timezoneBias","idleStartTime","overallCapability","isOnBuddyList","presenceNote","voiceMailUri","availabilityText","availabilityTooltip","isDurationInAvailabilityText","freeBusyStatus","calendarState","presence"]},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_scalarPropertyOriginalNames",{get:function(){return["Availability","Status","IsSelf","IsTagged","CustomStatusString","IsBlocked","PresenceTooltip","IsOutOfOffice","OutOfOfficeNote","Timezone","MeetingLocation","MeetingSubject","TimezoneBias","IdleStartTime","OverallCapability","IsOnBuddyList","PresenceNote","VoiceMailUri","AvailabilityText","AvailabilityTooltip","IsDurationInAvailabilityText","FreeBusyStatus","CalendarState","Presence"]},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"availability",{get:function(){return p("availability",this._A,"PersonaUnifiedCommunicationInfo",this._isNull),this._A},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"availabilityText",{get:function(){return p("availabilityText",this._Av,"PersonaUnifiedCommunicationInfo",this._isNull),this._Av},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"availabilityTooltip",{get:function(){return p("availabilityTooltip",this._Ava,"PersonaUnifiedCommunicationInfo",this._isNull),this._Ava},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"calendarState",{get:function(){return p("calendarState",this._C,"PersonaUnifiedCommunicationInfo",this._isNull),this._C},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"customStatusString",{get:function(){return p("customStatusString",this._Cu,"PersonaUnifiedCommunicationInfo",this._isNull),this._Cu},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"freeBusyStatus",{get:function(){return p("freeBusyStatus",this._F,"PersonaUnifiedCommunicationInfo",this._isNull),this._F},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"idleStartTime",{get:function(){return p("idleStartTime",this._I,"PersonaUnifiedCommunicationInfo",this._isNull),this._I},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"isBlocked",{get:function(){return p("isBlocked",this._Is,"PersonaUnifiedCommunicationInfo",this._isNull),this._Is},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"isDurationInAvailabilityText",{get:function(){return p("isDurationInAvailabilityText",this._IsD,"PersonaUnifiedCommunicationInfo",this._isNull),this._IsD},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"isOnBuddyList",{get:function(){return p("isOnBuddyList",this._IsO,"PersonaUnifiedCommunicationInfo",this._isNull),this._IsO},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"isOutOfOffice",{get:function(){return p("isOutOfOffice",this._IsOu,"PersonaUnifiedCommunicationInfo",this._isNull),this._IsOu},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"isSelf",{get:function(){return p("isSelf",this._IsS,"PersonaUnifiedCommunicationInfo",this._isNull),this._IsS},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"isTagged",{get:function(){return p("isTagged",this._IsT,"PersonaUnifiedCommunicationInfo",this._isNull),this._IsT},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"meetingLocation",{get:function(){return p("meetingLocation",this._M,"PersonaUnifiedCommunicationInfo",this._isNull),this._M},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"meetingSubject",{get:function(){return p("meetingSubject",this._Me,"PersonaUnifiedCommunicationInfo",this._isNull),this._Me},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"outOfOfficeNote",{get:function(){return p("outOfOfficeNote",this._O,"PersonaUnifiedCommunicationInfo",this._isNull),this._O},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"overallCapability",{get:function(){return p("overallCapability",this._Ov,"PersonaUnifiedCommunicationInfo",this._isNull),this._Ov},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"presence",{get:function(){return p("presence",this._P,"PersonaUnifiedCommunicationInfo",this._isNull),this._P},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"presenceNote",{get:function(){return p("presenceNote",this._Pr,"PersonaUnifiedCommunicationInfo",this._isNull),this._Pr},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"presenceTooltip",{get:function(){return p("presenceTooltip",this._Pre,"PersonaUnifiedCommunicationInfo",this._isNull),this._Pre},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"status",{get:function(){return p("status",this._S,"PersonaUnifiedCommunicationInfo",this._isNull),this._S},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"timezone",{get:function(){return p("timezone",this._T,"PersonaUnifiedCommunicationInfo",this._isNull),this._T},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"timezoneBias",{get:function(){return p("timezoneBias",this._Ti,"PersonaUnifiedCommunicationInfo",this._isNull),this._Ti},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"voiceMailUri",{get:function(){return p("voiceMailUri",this._V,"PersonaUnifiedCommunicationInfo",this._isNull),this._V},enumerable:!0,configurable:!0}),t.prototype._handleResult=function(t){if(e.prototype._handleResult.call(this,t),!u(t)){var n=t;y(this,n),l(n.Availability)||(this._A=n.Availability),l(n.AvailabilityText)||(this._Av=n.AvailabilityText),l(n.AvailabilityTooltip)||(this._Ava=n.AvailabilityTooltip),l(n.CalendarState)||(this._C=n.CalendarState),l(n.CustomStatusString)||(this._Cu=n.CustomStatusString),l(n.FreeBusyStatus)||(this._F=n.FreeBusyStatus),l(n.IdleStartTime)||(this._I=b(n.IdleStartTime)),l(n.IsBlocked)||(this._Is=n.IsBlocked),l(n.IsDurationInAvailabilityText)||(this._IsD=n.IsDurationInAvailabilityText),l(n.IsOnBuddyList)||(this._IsO=n.IsOnBuddyList),l(n.IsOutOfOffice)||(this._IsOu=n.IsOutOfOffice),l(n.IsSelf)||(this._IsS=n.IsSelf),l(n.IsTagged)||(this._IsT=n.IsTagged),l(n.MeetingLocation)||(this._M=n.MeetingLocation),l(n.MeetingSubject)||(this._Me=n.MeetingSubject),l(n.OutOfOfficeNote)||(this._O=n.OutOfOfficeNote),l(n.OverallCapability)||(this._Ov=n.OverallCapability),l(n.Presence)||(this._P=n.Presence),l(n.PresenceNote)||(this._Pr=n.PresenceNote),l(n.PresenceTooltip)||(this._Pre=n.PresenceTooltip),l(n.Status)||(this._S=n.Status),l(n.Timezone)||(this._T=n.Timezone),l(n.TimezoneBias)||(this._Ti=n.TimezoneBias),l(n.VoiceMailUri)||(this._V=n.VoiceMailUri)}},t.prototype.load=function(e){return h(this,e)},t.prototype.retrieve=function(e){return d(this,e)},t.prototype._handleRetrieveResult=function(t,n){if(e.prototype._handleRetrieveResult.call(this,t,n),!u(t)){var r=t;l(r.IdleStartTime)||(r.idleStartTime=b(r.idleStartTime)),_(this,t,n)}},t.prototype.toJSON=function(){return m(this,{availability:this._A,availabilityText:this._Av,availabilityTooltip:this._Ava,calendarState:this._C,customStatusString:this._Cu,freeBusyStatus:this._F,idleStartTime:this._I,isBlocked:this._Is,isDurationInAvailabilityText:this._IsD,isOnBuddyList:this._IsO,isOutOfOffice:this._IsOu,isSelf:this._IsS,isTagged:this._IsT,meetingLocation:this._M,meetingSubject:this._Me,outOfOfficeNote:this._O,overallCapability:this._Ov,presence:this._P,presenceNote:this._Pr,presenceTooltip:this._Pre,status:this._S,timezone:this._T,timezoneBias:this._Ti,voiceMailUri:this._V},{})},t.prototype.setMockData=function(e){v(this,e)},t.prototype.ensureUnchanged=function(e){a(this,e)},t}(OfficeExtension.ClientObject);e.PersonaUnifiedCommunicationInfo=B;var q=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return __extends(t,e),Object.defineProperty(t.prototype,"_className",{get:function(){return"PersonaPhotoInfo"},enumerable:!0,configurable:!0}),t.prototype.getImageUri=function(e){return s(this,"getImageUri",1,[e],4,0)},t.prototype.getImageUriWithMetadata=function(e){return s(this,"getImageUriWithMetadata",1,[e],4,0)},t.prototype.getPlaceholderUri=function(e){return s(this,"getPlaceholderUri",1,[e],4,0)},t.prototype.setPlaceholderColor=function(e){s(this,"setPlaceholderColor",0,[e],0,0)},t.prototype._handleResult=function(t){(e.prototype._handleResult.call(this,t),u(t))||y(this,t)},t.prototype._handleRetrieveResult=function(t,n){e.prototype._handleRetrieveResult.call(this,t,n),_(this,t,n)},t.prototype.toJSON=function(){return m(this,{},{})},t}(OfficeExtension.ClientObject);e.PersonaPhotoInfo=q;var V=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return __extends(n,t),Object.defineProperty(n.prototype,"_className",{get:function(){return"PersonaCollection"},enumerable:!0,configurable:!0}),Object.defineProperty(n.prototype,"_isCollection",{get:function(){return!0},enumerable:!0,configurable:!0}),Object.defineProperty(n.prototype,"items",{get:function(){return p("items",this.m__items,"PersonaCollection",this._isNull),this.m__items},enumerable:!0,configurable:!0}),n.prototype.getCount=function(){return s(this,"GetCount",1,[],4,0)},n.prototype.getItem=function(t){return r(e.Persona,this,[t])},n.prototype._handleResult=function(n){if(t.prototype._handleResult.call(this,n),!u(n)){var r=n;if(y(this,r),!u(r[OfficeExtension.Constants.items])){this.m__items=[];for(var i=r[OfficeExtension.Constants.items],s=0;s<i.length;s++){var a=o(e.Persona,!0,this,i[s],s);a._handleResult(i[s]),this.m__items.push(a)}}}},n.prototype.load=function(e){return h(this,e)},n.prototype.retrieve=function(e){return d(this,e)},n.prototype._handleRetrieveResult=function(n,r){var i=this;t.prototype._handleRetrieveResult.call(this,n,r),_(this,n,r,(function(t,n){return o(e.Persona,!0,i,t,n)}))},n.prototype.toJSON=function(){return m(this,{},{},this.m__items)},n.prototype.setMockData=function(t){var n=this;v(this,t,(function(t,r){return o(e.Persona,!0,n,t,r)}),(function(e){return n.m__items=e}))},n}(OfficeExtension.ClientObject);e.PersonaCollection=V;var G=function(n){function r(){return null!==n&&n.apply(this,arguments)||this}return __extends(r,n),Object.defineProperty(r.prototype,"_className",{get:function(){return"PersonaOrganizationInfo"},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"_scalarPropertyNames",{get:function(){return["isWarmedUp","isWarmingUp"]},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"_scalarPropertyOriginalNames",{get:function(){return["IsWarmedUp","IsWarmingUp"]},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"_navigationPropertyNames",{get:function(){return["hierarchy","manager","directReports"]},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"directReports",{get:function(){return this._D||(this._D=t(e.PersonaCollection,this,"DirectReports",!0,4)),this._D},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"hierarchy",{get:function(){return this._H||(this._H=t(e.PersonaCollection,this,"Hierarchy",!0,4)),this._H},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"manager",{get:function(){return this._M||(this._M=t(e.Persona,this,"Manager",!1,4)),this._M},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"isWarmedUp",{get:function(){return p("isWarmedUp",this._I,"PersonaOrganizationInfo",this._isNull),this._I},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"isWarmingUp",{get:function(){return p("isWarmingUp",this._Is,"PersonaOrganizationInfo",this._isNull),this._Is},enumerable:!0,configurable:!0}),r.prototype._handleResult=function(e){if(n.prototype._handleResult.call(this,e),!u(e)){var t=e;y(this,t),l(t.IsWarmedUp)||(this._I=t.IsWarmedUp),l(t.IsWarmingUp)||(this._Is=t.IsWarmingUp),g(this,t,["directReports","DirectReports","hierarchy","Hierarchy","manager","Manager"])}},r.prototype.load=function(e){return h(this,e)},r.prototype.retrieve=function(e){return d(this,e)},r.prototype._handleRetrieveResult=function(e,t){n.prototype._handleRetrieveResult.call(this,e,t),_(this,e,t)},r.prototype.toJSON=function(){return m(this,{isWarmedUp:this._I,isWarmingUp:this._Is},{})},r.prototype.setMockData=function(e){v(this,e)},r.prototype.ensureUnchanged=function(e){a(this,e)},r}(OfficeExtension.ClientObject);e.PersonaOrganizationInfo=G,function(e){e.email="Email",e.workPhone="WorkPhone",e.workPhone2="WorkPhone2",e.workFax="WorkFax",e.mobilePhone="MobilePhone",e.homePhone="HomePhone",e.homePhone2="HomePhone2",e.otherPhone="OtherPhone",e.sipAddress="SipAddress",e.profile="Profile",e.office="Office",e.company="Company",e.workAddress="WorkAddress",e.homeAddress="HomeAddress",e.otherAddress="OtherAddress",e.birthday="Birthday"}(e.CustomizedData||(e.CustomizedData={}));var J=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return __extends(t,e),Object.defineProperty(t.prototype,"_className",{get:function(){return"UnifiedGroupInfo"},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_scalarPropertyNames",{get:function(){return["description","oneDrive","oneNote","isPublic","amIOwner","amIMember","amISubscribed","memberCount","ownerCount","hasGuests","site","planner","classification","subscriptionEnabled"]},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_scalarPropertyOriginalNames",{get:function(){return["Description","OneDrive","OneNote","IsPublic","AmIOwner","AmIMember","AmISubscribed","MemberCount","OwnerCount","HasGuests","Site","Planner","Classification","SubscriptionEnabled"]},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_scalarPropertyUpdateable",{get:function(){return[!0,!0,!0,!0,!0,!0,!0,!0,!0,!0,!0,!0,!0,!0]},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"amIMember",{get:function(){return p("amIMember",this._A,"UnifiedGroupInfo",this._isNull),this._A},set:function(e){this._A=e,c(this,"AmIMember",e,0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"amIOwner",{get:function(){return p("amIOwner",this._Am,"UnifiedGroupInfo",this._isNull),this._Am},set:function(e){this._Am=e,c(this,"AmIOwner",e,0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"amISubscribed",{get:function(){return p("amISubscribed",this._AmI,"UnifiedGroupInfo",this._isNull),this._AmI},set:function(e){this._AmI=e,c(this,"AmISubscribed",e,0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"classification",{get:function(){return p("classification",this._C,"UnifiedGroupInfo",this._isNull),this._C},set:function(e){this._C=e,c(this,"Classification",e,0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"description",{get:function(){return p("description",this._D,"UnifiedGroupInfo",this._isNull),this._D},set:function(e){this._D=e,c(this,"Description",e,0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"hasGuests",{get:function(){return p("hasGuests",this._H,"UnifiedGroupInfo",this._isNull),this._H},set:function(e){this._H=e,c(this,"HasGuests",e,0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"isPublic",{get:function(){return p("isPublic",this._I,"UnifiedGroupInfo",this._isNull),this._I},set:function(e){this._I=e,c(this,"IsPublic",e,0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"memberCount",{get:function(){return p("memberCount",this._M,"UnifiedGroupInfo",this._isNull),this._M},set:function(e){this._M=e,c(this,"MemberCount",e,0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"oneDrive",{get:function(){return p("oneDrive",this._O,"UnifiedGroupInfo",this._isNull),this._O},set:function(e){this._O=e,c(this,"OneDrive",e,0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"oneNote",{get:function(){return p("oneNote",this._On,"UnifiedGroupInfo",this._isNull),this._On},set:function(e){this._On=e,c(this,"OneNote",e,0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"ownerCount",{get:function(){return p("ownerCount",this._Ow,"UnifiedGroupInfo",this._isNull),this._Ow},set:function(e){this._Ow=e,c(this,"OwnerCount",e,0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"planner",{get:function(){return p("planner",this._P,"UnifiedGroupInfo",this._isNull),this._P},set:function(e){this._P=e,c(this,"Planner",e,0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"site",{get:function(){return p("site",this._S,"UnifiedGroupInfo",this._isNull),this._S},set:function(e){this._S=e,c(this,"Site",e,0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"subscriptionEnabled",{get:function(){return p("subscriptionEnabled",this._Su,"UnifiedGroupInfo",this._isNull),this._Su},set:function(e){this._Su=e,c(this,"SubscriptionEnabled",e,0)},enumerable:!0,configurable:!0}),t.prototype.set=function(e,t){this._recursivelySet(e,t,["description","oneDrive","oneNote","isPublic","amIOwner","amIMember","amISubscribed","memberCount","ownerCount","hasGuests","site","planner","classification","subscriptionEnabled"],[],[])},t.prototype.update=function(e){this._recursivelyUpdate(e)},t.prototype._handleResult=function(t){if(e.prototype._handleResult.call(this,t),!u(t)){var n=t;y(this,n),l(n.AmIMember)||(this._A=n.AmIMember),l(n.AmIOwner)||(this._Am=n.AmIOwner),l(n.AmISubscribed)||(this._AmI=n.AmISubscribed),l(n.Classification)||(this._C=n.Classification),l(n.Description)||(this._D=n.Description),l(n.HasGuests)||(this._H=n.HasGuests),l(n.IsPublic)||(this._I=n.IsPublic),l(n.MemberCount)||(this._M=n.MemberCount),l(n.OneDrive)||(this._O=n.OneDrive),l(n.OneNote)||(this._On=n.OneNote),l(n.OwnerCount)||(this._Ow=n.OwnerCount),l(n.Planner)||(this._P=n.Planner),l(n.Site)||(this._S=n.Site),l(n.SubscriptionEnabled)||(this._Su=n.SubscriptionEnabled)}},t.prototype.load=function(e){return h(this,e)},t.prototype.retrieve=function(e){return d(this,e)},t.prototype._handleRetrieveResult=function(t,n){e.prototype._handleRetrieveResult.call(this,t,n),_(this,t,n)},t.prototype.toJSON=function(){return m(this,{amIMember:this._A,amIOwner:this._Am,amISubscribed:this._AmI,classification:this._C,description:this._D,hasGuests:this._H,isPublic:this._I,memberCount:this._M,oneDrive:this._O,oneNote:this._On,ownerCount:this._Ow,planner:this._P,site:this._S,subscriptionEnabled:this._Su},{})},t.prototype.setMockData=function(e){v(this,e)},t.prototype.ensureUnchanged=function(e){a(this,e)},t}(OfficeExtension.ClientObject);e.UnifiedGroupInfo=J;var W;!function(e){e[e.immediate=0]="immediate",e[e.load=3]="load"}(W=e.PersonaPromiseType||(e.PersonaPromiseType={}));var z=function(){return function(){}}();e.PersonaInfoAndSource=z;var Q=function(r){function i(){return null!==r&&r.apply(this,arguments)||this}return __extends(i,r),Object.defineProperty(i.prototype,"_className",{get:function(){return"Persona"},enumerable:!0,configurable:!0}),Object.defineProperty(i.prototype,"_scalarPropertyNames",{get:function(){return["hostId","type","capabilities","diagnosticId","instanceId"]},enumerable:!0,configurable:!0}),Object.defineProperty(i.prototype,"_scalarPropertyOriginalNames",{get:function(){return["HostId","Type","Capabilities","DiagnosticId","InstanceId"]},enumerable:!0,configurable:!0}),Object.defineProperty(i.prototype,"_navigationPropertyNames",{get:function(){return["photo","personaInfo","unifiedCommunicationInfo","organization","unifiedGroupInfo","actions"]},enumerable:!0,configurable:!0}),Object.defineProperty(i.prototype,"actions",{get:function(){return this._A||(this._A=t(e.PersonaActions,this,"Actions",!1,4)),this._A},enumerable:!0,configurable:!0}),Object.defineProperty(i.prototype,"organization",{get:function(){return this._O||(this._O=t(e.PersonaOrganizationInfo,this,"Organization",!1,4)),this._O},enumerable:!0,configurable:!0}),Object.defineProperty(i.prototype,"personaInfo",{get:function(){return this._P||(this._P=t(e.PersonaInfo,this,"PersonaInfo",!1,4)),this._P},enumerable:!0,configurable:!0}),Object.defineProperty(i.prototype,"photo",{get:function(){return this._Ph||(this._Ph=t(e.PersonaPhotoInfo,this,"Photo",!1,4)),this._Ph},enumerable:!0,configurable:!0}),Object.defineProperty(i.prototype,"unifiedCommunicationInfo",{get:function(){return this._U||(this._U=t(e.PersonaUnifiedCommunicationInfo,this,"UnifiedCommunicationInfo",!1,4)),this._U},enumerable:!0,configurable:!0}),Object.defineProperty(i.prototype,"unifiedGroupInfo",{get:function(){return this._Un||(this._Un=t(e.UnifiedGroupInfo,this,"UnifiedGroupInfo",!1,4)),this._Un},enumerable:!0,configurable:!0}),Object.defineProperty(i.prototype,"capabilities",{get:function(){return p("capabilities",this._C,"Persona",this._isNull),this._C},enumerable:!0,configurable:!0}),Object.defineProperty(i.prototype,"diagnosticId",{get:function(){return p("diagnosticId",this._D,"Persona",this._isNull),this._D},enumerable:!0,configurable:!0}),Object.defineProperty(i.prototype,"hostId",{get:function(){return p("hostId",this._H,"Persona",this._isNull),this._H},enumerable:!0,configurable:!0}),Object.defineProperty(i.prototype,"instanceId",{get:function(){return p("instanceId",this._I,"Persona",this._isNull),this._I},enumerable:!0,configurable:!0}),Object.defineProperty(i.prototype,"type",{get:function(){return p("type",this._T,"Persona",this._isNull),this._T},enumerable:!0,configurable:!0}),i.prototype.set=function(e,t){this._recursivelySet(e,t,[],["unifiedGroupInfo"],["actions","organization","personaInfo","photo","unifiedCommunicationInfo"])},i.prototype.update=function(e){this._recursivelyUpdate(e)},i.prototype.dispose=function(){s(this,"Dispose",0,[],0,0)},i.prototype.getCustomizations=function(){return s(this,"GetCustomizations",1,[],4,0)},i.prototype.getMembers=function(){return n(e.MemberInfoList,this,"GetMembers",1,[],!1,!1,null,4)},i.prototype.getMembership=function(){return n(e.MemberInfoList,this,"GetMembership",1,[],!1,!1,null,4)},i.prototype.getViewableSources=function(){return s(this,"GetViewableSources",1,[],4,0)},i.prototype.reportTimeForRender=function(e,t){s(this,"ReportTimeForRender",0,[e,t],0,0)},i.prototype.warmup=function(e){s(this,"Warmup",0,[e],0,0)},i.prototype._handleResult=function(e){if(r.prototype._handleResult.call(this,e),!u(e)){var t=e;y(this,t),l(t.Capabilities)||(this._C=t.Capabilities),l(t.DiagnosticId)||(this._D=t.DiagnosticId),l(t.HostId)||(this._H=t.HostId),l(t.InstanceId)||(this._I=t.InstanceId),l(t.Type)||(this._T=t.Type),g(this,t,["actions","Actions","organization","Organization","personaInfo","PersonaInfo","photo","Photo","unifiedCommunicationInfo","UnifiedCommunicationInfo","unifiedGroupInfo","UnifiedGroupInfo"])}},i.prototype.load=function(e){return h(this,e)},i.prototype.retrieve=function(e){return d(this,e)},i.prototype._handleRetrieveResult=function(e,t){r.prototype._handleRetrieveResult.call(this,e,t),_(this,e,t)},i.prototype.toJSON=function(){return m(this,{capabilities:this._C,diagnosticId:this._D,hostId:this._H,instanceId:this._I,type:this._T},{organization:this._O,personaInfo:this._P,unifiedCommunicationInfo:this._U,unifiedGroupInfo:this._Un})},i.prototype.setMockData=function(e){v(this,e)},i.prototype.ensureUnchanged=function(e){a(this,e)},i}(OfficeExtension.ClientObject);e.Persona=Q;var K=function(){function t(){}return t.prototype.performAsyncOperation=function(e,t,n,r){var i=this;e!=W.immediate?r().then((function(e){if(e)n();else{var o=i;o.load("hostId"),o.context.sync().then((function(){var e=o.hostId;i.getPersonaLifetime().then((function(i){var s=function(a){return new OfficeExtension.CoreUtility.Promise((function(c,u){if(a.sendingPersonaHostId==e)for(var l=0;l<a.dataUpdated.length;++l){var p=a.dataUpdated[l];if(t==p)return void r().then((function(e){e&&(n(),i.onPersonaUpdated.remove(s),o.context.sync()),c(e)}))}c(!1)}))};i.onPersonaUpdated.add(s),o.context.sync()}))}))}})):n()},t.prototype.getOrganizationAsync=function(e){var t=this;return new OfficeExtension.CoreUtility.Promise((function(n,r){var i=t;t.performAsyncOperation(e,k.organization,(function(){var e=i.organization;e.load("*"),i.context.sync().then((function(){n(e)}))}),(function(){return new OfficeExtension.CoreUtility.Promise((function(e,t){var n=i.organization;n.load("isWarmedUp"),i.context.sync().then((function(){e(n.isWarmedUp)}))}))}))}))},t.prototype.getIsPersonaInfoResolvedCheck=function(){var e=this;return new OfficeExtension.CoreUtility.Promise((function(t,n){var r=e.personaInfo;r.load("isPersonResolved"),e.context.sync().then((function(){t(r.isPersonResolved)}))}))},t.prototype.getPersonaInfoAsync=function(e){var t=this;return new OfficeExtension.CoreUtility.Promise((function(n,r){var i=t;t.performAsyncOperation(e,k.personaInfo,(function(){var e=i.personaInfo;e.load(),i.context.sync().then((function(){n(e)}))}),(function(){return t.getIsPersonaInfoResolvedCheck()}))}))},t.prototype.getPersonaInfoWithSourceAsync=function(e){var t=this;return new OfficeExtension.CoreUtility.Promise((function(n,r){var i=t;t.performAsyncOperation(e,k.personaInfo,(function(){var e=new z;e.info=i.personaInfo,e.info.load(),e.source=i.personaInfo.sources,e.source.load(),i.context.sync().then((function(){n(e)}))}),(function(){return t.getIsPersonaInfoResolvedCheck()}))}))},t.prototype.getUnifiedCommunicationInfo=function(e){var t=this;return new OfficeExtension.CoreUtility.Promise((function(n,r){var i=t;t.performAsyncOperation(e,k.personaInfo,(function(){var e=i.unifiedCommunicationInfo;e.load("*"),i.context.sync().then((function(){n(e)}))}),(function(){return t.getIsPersonaInfoResolvedCheck()}))}))},t.prototype.getUnifiedGroupInfoAsync=function(e){var t=this;return new OfficeExtension.CoreUtility.Promise((function(n,r){var i=t;t.performAsyncOperation(e,k.personaInfo,(function(){var e=i.unifiedGroupInfo;e.load("*"),i.context.sync().then((function(){n(e)}))}),(function(){return t.getIsPersonaInfoResolvedCheck()}))}))},t.prototype.getTypeAsync=function(t){var n=this;return new OfficeExtension.CoreUtility.Promise((function(r,i){var o=n;n.performAsyncOperation(t,k.personaInfo,(function(){o.load("type"),o.context.sync().then((function(){r(e.PersonaType[o.type.valueOf()])}))}),(function(){return n.getIsPersonaInfoResolvedCheck()}))}))},t.prototype.getCustomizationsAsync=function(e){var t=this;return new OfficeExtension.CoreUtility.Promise((function(n,r){var i=t;t.performAsyncOperation(e,k.personaInfo,(function(){var e=i.getCustomizations();i.context.sync().then((function(){n(e.value)}))}),(function(){return t.getIsPersonaInfoResolvedCheck()}))}))},t.prototype.getMembersAsync=function(e){var t=this;return new OfficeExtension.CoreUtility.Promise((function(n,r){var i=t;t.performAsyncOperation(e,k.members,(function(){var e=i.getMembers();e.load("isWarmedUp"),i.context.sync().then((function(){n(e)}))}),(function(){return new OfficeExtension.CoreUtility.Promise((function(e,t){var n=i.getMembers();n.load("isWarmedUp"),i.context.sync().then((function(){e(n.isWarmedUp)}))}))}))}))},t.prototype.getMembershipAsync=function(e){var t=this;return new OfficeExtension.CoreUtility.Promise((function(n,r){var i=t;t.performAsyncOperation(e,k.membership,(function(){var e=i.getMembership();e.load("*"),i.context.sync().then((function(){n(e)}))}),(function(){return new OfficeExtension.CoreUtility.Promise((function(e){var t=i.getMembership();t.load("isWarmedUp"),i.context.sync().then((function(){e(t.isWarmedUp)}))}))}))}))},t.prototype.getPersonaLifetime=function(){var e=this;return new OfficeExtension.CoreUtility.Promise((function(t,n){var r=e;r.load("instanceId"),r.context.sync().then((function(){new X(r.context,r.instanceId).getPersonaLifetime().then((function(e){t(e)}))}))}))},t}();e.PersonaCustom=K,OfficeExtension.Utility.applyMixin(Q,K);var Z=function(t){function r(){return null!==t&&t.apply(this,arguments)||this}return __extends(r,t),Object.defineProperty(r.prototype,"_className",{get:function(){return"PersonaLifetime"},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"_scalarPropertyNames",{get:function(){return["instanceId"]},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"_scalarPropertyOriginalNames",{get:function(){return["InstanceId"]},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"instanceId",{get:function(){return p("instanceId",this._I,"PersonaLifetime",this._isNull),this._I},enumerable:!0,configurable:!0}),r.prototype.getPersona=function(t){return n(e.Persona,this,"GetPersona",1,[t],!1,!1,null,4)},r.prototype.getPersonaForOrgByEntryId=function(t,r,i,o){return n(e.Persona,this,"GetPersonaForOrgByEntryId",1,[t,r,i,o],!1,!1,null,4)},r.prototype.getPersonaForOrgEntry=function(t,r,i,o){return n(e.Persona,this,"GetPersonaForOrgEntry",1,[t,r,i,o],!1,!1,null,4)},r.prototype.getPolicies=function(){return s(this,"GetPolicies",1,[],4,0)},r.prototype._RegisterPersonaUpdatedEvent=function(){s(this,"_RegisterPersonaUpdatedEvent",0,[],0,0)},r.prototype._UnregisterPersonaUpdatedEvent=function(){s(this,"_UnregisterPersonaUpdatedEvent",0,[],0,0)},r.prototype._handleResult=function(e){if(t.prototype._handleResult.call(this,e),!u(e)){var n=e;y(this,n),l(n.InstanceId)||(this._I=n.InstanceId)}},r.prototype.load=function(e){return h(this,e)},r.prototype.retrieve=function(e){return d(this,e)},r.prototype._handleRetrieveResult=function(e,n){t.prototype._handleRetrieveResult.call(this,e,n),_(this,e,n)},Object.defineProperty(r.prototype,"onPersonaUpdated",{get:function(){var e=this;return this.m_personaUpdated||(this.m_personaUpdated=new OfficeExtension.GenericEventHandlers(this.context,this,"PersonaUpdated",{eventType:3502,registerFunc:function(){return e._RegisterPersonaUpdatedEvent()},unregisterFunc:function(){return e._UnregisterPersonaUpdatedEvent()},getTargetIdFunc:function(){return e.instanceId},eventArgsTransformFunc:function(e){var t={dataUpdated:e.dataUpdated,sendingPersonaHostId:e.sendingPersonaHostId};return OfficeExtension.Utility._createPromiseFromResult(t)}})),this.m_personaUpdated},enumerable:!0,configurable:!0}),r.prototype.toJSON=function(){return m(this,{instanceId:this._I},{})},r.prototype.setMockData=function(e){v(this,e)},r.prototype.ensureUnchanged=function(e){a(this,e)},r}(OfficeExtension.ClientObject);e.PersonaLifetime=Z;var $=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return __extends(t,e),Object.defineProperty(t.prototype,"_className",{get:function(){return"LokiTokenProvider"},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_scalarPropertyNames",{get:function(){return["emailOrUpn","instanceId"]},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_scalarPropertyOriginalNames",{get:function(){return["EmailOrUpn","InstanceId"]},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"emailOrUpn",{get:function(){return p("emailOrUpn",this._E,"LokiTokenProvider",this._isNull),this._E},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"instanceId",{get:function(){return p("instanceId",this._I,"LokiTokenProvider",this._isNull),this._I},enumerable:!0,configurable:!0}),t.prototype.requestClientAccessToken=function(){s(this,"RequestClientAccessToken",0,[],0,0)},t.prototype.requestIdentityUniqueId=function(){s(this,"RequestIdentityUniqueId",0,[],0,0)},t.prototype.requestToken=function(){s(this,"RequestToken",0,[],0,0)},t.prototype._RegisterClientAccessTokenAvailableEvent=function(){s(this,"_RegisterClientAccessTokenAvailableEvent",0,[],0,0)},t.prototype._RegisterIdentityUniqueIdAvailableEvent=function(){s(this,"_RegisterIdentityUniqueIdAvailableEvent",0,[],0,0)},t.prototype._RegisterLokiTokenAvailableEvent=function(){s(this,"_RegisterLokiTokenAvailableEvent",0,[],0,0)},t.prototype._UnregisterClientAccessTokenAvailableEvent=function(){s(this,"_UnregisterClientAccessTokenAvailableEvent",0,[],0,0)},t.prototype._UnregisterIdentityUniqueIdAvailableEvent=function(){s(this,"_UnregisterIdentityUniqueIdAvailableEvent",0,[],0,0)},t.prototype._UnregisterLokiTokenAvailableEvent=function(){s(this,"_UnregisterLokiTokenAvailableEvent",0,[],0,0)},t.prototype._handleResult=function(t){if(e.prototype._handleResult.call(this,t),!u(t)){var n=t;y(this,n),l(n.EmailOrUpn)||(this._E=n.EmailOrUpn),l(n.InstanceId)||(this._I=n.InstanceId)}},t.prototype.load=function(e){return h(this,e)},t.prototype.retrieve=function(e){return d(this,e)},t.prototype._handleRetrieveResult=function(t,n){e.prototype._handleRetrieveResult.call(this,t,n),_(this,t,n)},Object.defineProperty(t.prototype,"onClientAccessTokenAvailable",{get:function(){var e=this;return this.m_clientAccessTokenAvailable||(this.m_clientAccessTokenAvailable=new OfficeExtension.GenericEventHandlers(this.context,this,"ClientAccessTokenAvailable",{eventType:3505,registerFunc:function(){return e._RegisterClientAccessTokenAvailableEvent()},unregisterFunc:function(){return e._UnregisterClientAccessTokenAvailableEvent()},getTargetIdFunc:function(){return e.instanceId},eventArgsTransformFunc:function(e){var t={clientAccessToken:e.clientAccessToken,isAvailable:e.isAvailable,tokenTTLInSeconds:e.tokenTTLInSeconds};return OfficeExtension.Utility._createPromiseFromResult(t)}})),this.m_clientAccessTokenAvailable},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"onIdentityUniqueIdAvailable",{get:function(){var e=this;return this.m_identityUniqueIdAvailable||(this.m_identityUniqueIdAvailable=new OfficeExtension.GenericEventHandlers(this.context,this,"IdentityUniqueIdAvailable",{eventType:3504,registerFunc:function(){return e._RegisterIdentityUniqueIdAvailableEvent()},unregisterFunc:function(){return e._UnregisterIdentityUniqueIdAvailableEvent()},getTargetIdFunc:function(){return e.instanceId},eventArgsTransformFunc:function(e){var t={isAvailable:e.isAvailable,uniqueId:e.uniqueId};return OfficeExtension.Utility._createPromiseFromResult(t)}})),this.m_identityUniqueIdAvailable},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"onLokiTokenAvailable",{get:function(){var e=this;return this.m_lokiTokenAvailable||(this.m_lokiTokenAvailable=new OfficeExtension.GenericEventHandlers(this.context,this,"LokiTokenAvailable",{eventType:3503,registerFunc:function(){return e._RegisterLokiTokenAvailableEvent()},unregisterFunc:function(){return e._UnregisterLokiTokenAvailableEvent()},getTargetIdFunc:function(){return e.instanceId},eventArgsTransformFunc:function(e){var t={isAvailable:e.isAvailable,lokiAutoDiscoverUrl:e.lokiAutoDiscoverUrl,lokiToken:e.lokiToken};return OfficeExtension.Utility._createPromiseFromResult(t)}})),this.m_lokiTokenAvailable},enumerable:!0,configurable:!0}),t.prototype.toJSON=function(){return m(this,{emailOrUpn:this._E,instanceId:this._I},{})},t.prototype.setMockData=function(e){v(this,e)},t.prototype.ensureUnchanged=function(e){a(this,e)},t}(OfficeExtension.ClientObject);e.LokiTokenProvider=$;var Y=function(t){function r(){return null!==t&&t.apply(this,arguments)||this}return __extends(r,t),Object.defineProperty(r.prototype,"_className",{get:function(){return"LokiTokenProviderFactory"},enumerable:!0,configurable:!0}),r.prototype.getLokiTokenProvider=function(t){return n(e.LokiTokenProvider,this,"GetLokiTokenProvider",1,[t],!1,!1,null,4)},r.prototype._handleResult=function(e){(t.prototype._handleResult.call(this,e),u(e))||y(this,e)},r.prototype._handleRetrieveResult=function(e,n){t.prototype._handleRetrieveResult.call(this,e,n),_(this,e,n)},r.newObject=function(t){return i(e.LokiTokenProviderFactory,t,"Microsoft.People.LokiTokenProviderFactory",!1,4)},r.prototype.toJSON=function(){return m(this,{},{})},r}(OfficeExtension.ClientObject);e.LokiTokenProviderFactory=Y;var X=function(){function t(e,t){this.context=e,this.instanceId=t}return Object.defineProperty(t.prototype,"serviceContext",{get:function(){return this.m_serviceConext||(this.m_serviceConext=e.ServiceContext.newObject(this.context)),this.m_serviceConext},enumerable:!0,configurable:!0}),t.prototype.getPersonaLifetime=function(){var e=this;return new OfficeExtension.CoreUtility.Promise((function(t,n){var r=e.serviceContext.getPersonaLifetime(e.instanceId);e.context.sync().then((function(){r.load("instanceId"),e.context.sync().then((function(){t(r)}))}))}))},t.prototype.getInitialPersona=function(){var e=this;return new OfficeExtension.CoreUtility.Promise((function(t,n){var r=e.serviceContext.getInitialPersona(e.instanceId);e.context.sync().then((function(){t(r)}))}))},t.prototype.getLokiTokenProvider=function(){var e=this;return new OfficeExtension.CoreUtility.Promise((function(t,n){var r=e.serviceContext.getLokiTokenProvider(e.instanceId);e.context.sync().then((function(){r.load("instanceId"),e.context.sync().then((function(){t(r)}))}))}))},t}();e.PeopleApiContext=X;var ee=function(t){function r(){return null!==t&&t.apply(this,arguments)||this}return __extends(r,t),Object.defineProperty(r.prototype,"_className",{get:function(){return"ServiceContext"},enumerable:!0,configurable:!0}),r.prototype.accountEmailOrUpn=function(e){return s(this,"AccountEmailOrUpn",1,[e],4,0)},r.prototype.dispose=function(e){s(this,"Dispose",0,[e],0,0)},r.prototype.getInitialPersona=function(t){return n(e.Persona,this,"GetInitialPersona",1,[t],!1,!1,null,4)},r.prototype.getLokiTokenProvider=function(t){return n(e.LokiTokenProvider,this,"GetLokiTokenProvider",1,[t],!1,!1,null,4)},r.prototype.getPersonaLifetime=function(t){return n(e.PersonaLifetime,this,"GetPersonaLifetime",1,[t],!1,!1,null,4)},r.prototype.getPersonaPolicies=function(){return s(this,"GetPersonaPolicies",1,[],4,0)},r.prototype._handleResult=function(e){(t.prototype._handleResult.call(this,e),u(e))||y(this,e)},r.prototype._handleRetrieveResult=function(e,n){t.prototype._handleRetrieveResult.call(this,e,n),_(this,e,n)},r.newObject=function(t){return i(e.ServiceContext,t,"Microsoft.People.ServiceContext",!1,4)},r.prototype.toJSON=function(){return m(this,{},{})},r}(OfficeExtension.ClientObject);e.ServiceContext=ee;var te=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return __extends(n,t),Object.defineProperty(n.prototype,"_className",{get:function(){return"RichapiPcxFeatureChecks"},enumerable:!0,configurable:!0}),n.prototype.isAddChangePhotoLinkOnLpcPersonaImageFlightEnabled=function(){return s(this,"IsAddChangePhotoLinkOnLpcPersonaImageFlightEnabled",1,[],4,0)},n.prototype._handleResult=function(e){(t.prototype._handleResult.call(this,e),u(e))||y(this,e)},n.prototype._handleRetrieveResult=function(e,n){t.prototype._handleRetrieveResult.call(this,e,n),_(this,e,n)},n.newObject=function(t){return i(e.RichapiPcxFeatureChecks,t,"Microsoft.People.RichapiPcxFeatureChecks",!1,4)},n.prototype.toJSON=function(){return m(this,{},{})},n}(OfficeExtension.ClientObject);e.RichapiPcxFeatureChecks=te;var ne=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return __extends(n,t),Object.defineProperty(n.prototype,"_className",{get:function(){return"Tap"},enumerable:!0,configurable:!0}),n.prototype.getEnterpriseUserInfo=function(){return s(this,"GetEnterpriseUserInfo",1,[],5,0)},n.prototype.getMruFriendlyPath=function(e){return s(this,"GetMruFriendlyPath",1,[e],5,0)},n.prototype.launchFileUrlInOfficeApp=function(e,t){return s(this,"LaunchFileUrlInOfficeApp",1,[e,t],5,0)},n.prototype.performLocalSearch=function(e,t,n,r){return s(this,"PerformLocalSearch",1,[e,t,n,r],5,0)},n.prototype.readSearchCache=function(e,t,n){return s(this,"ReadSearchCache",1,[e,t,n],5,0)},n.prototype.writeSearchCache=function(e,t,n){return s(this,"WriteSearchCache",1,[e,t,n],5,0)},n.prototype._handleResult=function(e){(t.prototype._handleResult.call(this,e),u(e))||y(this,e)},n.prototype._handleRetrieveResult=function(e,n){t.prototype._handleRetrieveResult.call(this,e,n),_(this,e,n)},n.newObject=function(t){return i(e.Tap,t,"Microsoft.TapRichApi.Tap",!1,4)},n.prototype.toJSON=function(){return m(this,{},{})},n}(OfficeExtension.ClientObject);e.Tap=ne,function(e){e.unknown="Unknown",e.chart="Chart",e.smartArt="SmartArt",e.table="Table",e.image="Image",e.slide="Slide",e.ole="OLE",e.text="Text"}(e.ObjectType||(e.ObjectType={}));var re=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return __extends(n,t),Object.defineProperty(n.prototype,"_className",{get:function(){return"AppRuntimePersistenceService"},enumerable:!0,configurable:!0}),n.prototype.getAppRuntimeStartState=function(){return s(this,"GetAppRuntimeStartState",1,[],4,0)},n.prototype.setAppRuntimeStartState=function(e){s(this,"SetAppRuntimeStartState",0,[e],0,0)},n.prototype._handleResult=function(e){(t.prototype._handleResult.call(this,e),u(e))||y(this,e)},n.prototype._handleRetrieveResult=function(e,n){t.prototype._handleRetrieveResult.call(this,e,n),_(this,e,n)},n.newObject=function(t){return i(e.AppRuntimePersistenceService,t,"Microsoft.AppRuntime.AppRuntimePersistenceService",!1,4)},n.prototype.toJSON=function(){return m(this,{},{})},n}(OfficeExtension.ClientObject);e.AppRuntimePersistenceService=re;var ie,oe=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return __extends(n,t),Object.defineProperty(n.prototype,"_className",{get:function(){return"AppRuntimeService"},enumerable:!0,configurable:!0}),n.prototype.getAppRuntimeState=function(){return s(this,"GetAppRuntimeState",1,[],4,0)},n.prototype.setAppRuntimeState=function(e){s(this,"SetAppRuntimeState",0,[e],0,0)},n.prototype._handleResult=function(e){(t.prototype._handleResult.call(this,e),u(e))||y(this,e)},n.prototype._handleRetrieveResult=function(e,n){t.prototype._handleRetrieveResult.call(this,e,n),_(this,e,n)},n.newObject=function(t){return i(e.AppRuntimeService,t,"Microsoft.AppRuntime.AppRuntimeService",!1,4)},Object.defineProperty(n.prototype,"onVisibilityChanged",{get:function(){return this.m_visibilityChanged||(this.m_visibilityChanged=new OfficeExtension.GenericEventHandlers(this.context,this,"VisibilityChanged",{eventType:65539,registerFunc:function(){return OfficeExtension.Utility._createPromiseFromResult(null)},unregisterFunc:function(){return OfficeExtension.Utility._createPromiseFromResult(null)},getTargetIdFunc:function(){return""},eventArgsTransformFunc:function(e){var t={visibility:e.visibility};return OfficeExtension.Utility._createPromiseFromResult(t)}})),this.m_visibilityChanged},enumerable:!0,configurable:!0}),n.prototype.toJSON=function(){return m(this,{},{})},n}(OfficeExtension.ClientObject);e.AppRuntimeService=oe,function(e){e.inactive="Inactive",e.background="Background",e.visible="Visible"}(e.AppRuntimeState||(e.AppRuntimeState={})),function(e){e.hidden="Hidden",e.visible="Visible"}(e.Visibility||(e.Visibility={})),function(e){e.unknown="Unknown",e.basic="Basic",e.premium="Premium"}(ie=e.LicenseFeatureTier||(e.LicenseFeatureTier={}));var se=function(t){function r(){return null!==t&&t.apply(this,arguments)||this}return __extends(r,t),Object.defineProperty(r.prototype,"_className",{get:function(){return"License"},enumerable:!0,configurable:!0}),r.prototype.getFeatureTier=function(e,t){return s(this,"GetFeatureTier",1,[e,t],4,0)},r.prototype.getLicenseFeature=function(t){return n(e.LicenseFeature,this,"GetLicenseFeature",1,[t],!1,!1,null,4)},r.prototype.isFeatureEnabled=function(e,t){return s(this,"IsFeatureEnabled",1,[e,t],4,0)},r.prototype.isFreemiumUpsellEnabled=function(){return s(this,"IsFreemiumUpsellEnabled",1,[],4,0)},r.prototype.launchUpsellExperience=function(e){s(this,"LaunchUpsellExperience",1,[e],4,0)},r.prototype._TestFireStateChangedEvent=function(e){s(this,"_TestFireStateChangedEvent",0,[e],1,0)},r.prototype._handleResult=function(e){(t.prototype._handleResult.call(this,e),u(e))||y(this,e)},r.prototype._handleRetrieveResult=function(e,n){t.prototype._handleRetrieveResult.call(this,e,n),_(this,e,n)},r.newObject=function(t){return i(e.License,t,"Microsoft.Office.Licensing.License",!1,4)},r.prototype.toJSON=function(){return m(this,{},{})},r}(OfficeExtension.ClientObject);e.License=se;var ae=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return __extends(t,e),Object.defineProperty(t.prototype,"_className",{get:function(){return"LicenseFeature"},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_scalarPropertyNames",{get:function(){return["id"]},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_scalarPropertyOriginalNames",{get:function(){return["Id"]},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"id",{get:function(){return p("id",this._I,"LicenseFeature",this._isNull),this._I},enumerable:!0,configurable:!0}),t.prototype._RegisterStateChange=function(){s(this,"_RegisterStateChange",1,[],4,0)},t.prototype._UnregisterStateChange=function(){s(this,"_UnregisterStateChange",1,[],4,0)},t.prototype._handleResult=function(t){if(e.prototype._handleResult.call(this,t),!u(t)){var n=t;y(this,n),l(n.Id)||(this._I=n.Id)}},t.prototype.load=function(e){return h(this,e)},t.prototype.retrieve=function(e){return d(this,e)},t.prototype._handleIdResult=function(t){e.prototype._handleIdResult.call(this,t),u(t)||l(t.Id)||(this._I=t.Id)},t.prototype._handleRetrieveResult=function(t,n){e.prototype._handleRetrieveResult.call(this,t,n),_(this,t,n)},Object.defineProperty(t.prototype,"onStateChanged",{get:function(){var e=this;return this.m_stateChanged||(this.m_stateChanged=new OfficeExtension.GenericEventHandlers(this.context,this,"StateChanged",{eventType:1,registerFunc:function(){return e._RegisterStateChange()},unregisterFunc:function(){return e._UnregisterStateChange()},getTargetIdFunc:function(){return e.id},eventArgsTransformFunc:function(t){var n=P.LicenseFeature_StateChanged_EventArgsTransform(e,t);return OfficeExtension.Utility._createPromiseFromResult(n)}})),this.m_stateChanged},enumerable:!0,configurable:!0}),t.prototype.toJSON=function(){return m(this,{id:this._I},{})},t.prototype.setMockData=function(e){v(this,e)},t.prototype.ensureUnchanged=function(e){a(this,e)},t}(OfficeExtension.ClientObject);e.LicenseFeature=ae,function(e){e.LicenseFeature_StateChanged_EventArgsTransform=function(e,t){var n={feature:t.featureName,isEnabled:t.isEnabled,tier:t.tierName};return t.tierName&&(n.tier=0==t.tierName?ie.unknown:1==t.tierName?ie.basic:2==t.tierName?ie.premium:t.tierName),n}}(P=e._CC||(e._CC={})),function(e){e.apiNotAvailable="ApiNotAvailable",e.clientError="ClientError",e.controlIdNotFound="ControlIdNotFound",e.entryIdRequired="EntryIdRequired",e.generalException="GeneralException",e.hostRestartNeeded="HostRestartNeeded",e.instanceNotFound="InstanceNotFound",e.interactiveFlowAborted="InteractiveFlowAborted",e.invalidArgument="InvalidArgument",e.invalidGrant="InvalidGrant",e.invalidResourceUrl="InvalidResourceUrl",e.objectNotFound="ObjectNotFound",e.resourceNotSupported="ResourceNotSupported",e.serverError="ServerError",e.serviceUrlNotFound="ServiceUrlNotFound",e.ticketInvalidParams="TicketInvalidParams",e.ticketNetworkError="TicketNetworkError",e.ticketUnauthorized="TicketUnauthorized",e.ticketUninitialized="TicketUninitialized",e.ticketUnknownError="TicketUnknownError",e.unexpectedError="UnexpectedError",e.unsupportedUserIdentity="UnsupportedUserIdentity",e.userNotSignedIn="UserNotSignedIn"}(e.ErrorCodes||(e.ErrorCodes={})),e.Interfaces||(e.Interfaces={})}(OfficeCore||(OfficeCore={})),function(e){var t,n;!function(e){e.hidden="Hidden",e.taskpane="Taskpane"}(t=e.VisibilityMode||(e.VisibilityMode={})),function(e){e.none="None",e.load="Load"}(n=e.StartupBehavior||(e.StartupBehavior={})),function(e){function r(e){var t=new OfficeCore.RequestContext;return t._requestFlagModifier|=64,e&&(t._customData="WacPartition"),t}function i(e){return __awaiter(this,void 0,void 0,(function(){var t;return __generator(this,(function(n){switch(n.label){case 0:return t=r(!0),OfficeCore.AppRuntimeService.newObject(t).setAppRuntimeState(e),[4,t.sync()];case 1:return n.sent(),[2]}}))}))}var o;function s(e){return e===OfficeCore.Visibility.visible?t.taskpane:t.hidden}e.setStartupBehavior=function(e){return __awaiter(this,void 0,void 0,(function(){var t,i;return __generator(this,(function(o){switch(o.label){case 0:if(e!==n.load&&e!==n.none)throw OfficeExtension.Utility.createRuntimeError(OfficeExtension.ErrorCodes.invalidArgument,null,null);return t=e==n.load?OfficeCore.AppRuntimeState.background:OfficeCore.AppRuntimeState.inactive,i=r(!1),OfficeCore.AppRuntimePersistenceService.newObject(i).setAppRuntimeStartState(t),[4,i.sync()];case 1:return o.sent(),[2]}}))}))},e.getStartupBehavior=function(){return __awaiter(this,void 0,void 0,(function(){var e,t,i,o;return __generator(this,(function(s){switch(s.label){case 0:return e=r(!1),t=OfficeCore.AppRuntimePersistenceService.newObject(e),i=t.getAppRuntimeStartState(),[4,e.sync()];case 1:return s.sent(),o=i.value,[2,o==OfficeCore.AppRuntimeState.inactive?n.none:n.load]}}))}))},e._getState=function(){return __awaiter(this,void 0,void 0,(function(){var e,t,n;return __generator(this,(function(i){switch(i.label){case 0:return e=r(!0),t=OfficeCore.AppRuntimeService.newObject(e),n=t.getAppRuntimeState(),[4,e.sync()];case 1:return i.sent(),[2,n.value]}}))}))},e.showAsTaskpane=function(){return i(OfficeCore.AppRuntimeState.visible)},e.hide=function(){return i(OfficeCore.AppRuntimeState.background)},e.onVisibilityModeChanged=function(e){return __awaiter(this,void 0,void 0,(function(){var t,n,i=this;return __generator(this,(function(a){switch(a.label){case 0:return t=function(){if(!o){var e=r(!0);o=OfficeCore.AppRuntimeService.newObject(e)}return o}(),n=t.onVisibilityChanged.add((function(t){if(e){var n={visibilityMode:s(t.visibility)};e(n)}return null})),[4,t.context.sync()];case 1:return a.sent(),[2,function(){return __awaiter(i,void 0,void 0,(function(){return __generator(this,(function(e){switch(e.label){case 0:return n.remove(),[4,t.context.sync()];case 1:return e.sent(),[2]}}))}))}]}}))}))}}(e.addin||(e.addin={}))}(Office||(Office={})),function(e){!function(e){function t(){var e=new OfficeCore.RequestContext;return"web"==OSF._OfficeAppFactory.getHostInfo().hostPlatform&&(e._customData="WacPartition"),e}e.requestUpdate=function(e){var n=t(),r=n.ribbon;function i(e){e.controls.filter((function(e){return!!e.id})).forEach((function(e){var t=r.getButton(e.id);void 0!==e.enabled&&null!==e.enabled&&(t.enabled=e.enabled)}))}return e.tabs.filter((function(e){return!!e.id})).forEach((function(e){var t=r.getTab(e.id);void 0!==e.visible&&null!==e.visible&&t.setVisibility(e.visible),e.groups&&e.groups.length?e.groups.filter((function(e){return!!e.id})).forEach((function(e){i(e)})):i(e)})),n.sync()},e.requestCreateControls=function(e){var n=t();return n.ribbon.executeRequestCreate(JSON.stringify(e)),function(e){return new Promise((function(t,n){return setTimeout((function(){return t()}),e)}))}(250).then((function(){return n.sync()}))}}(e.ribbon||(e.ribbon={}))}(Office||(Office={})),function(e){var t;OfficeExtension.BatchApiHelper.createPropertyObject,OfficeExtension.BatchApiHelper.createMethodObject,OfficeExtension.BatchApiHelper.createIndexerObject,OfficeExtension.BatchApiHelper.createRootServiceObject,OfficeExtension.BatchApiHelper.createTopLevelServiceObject,OfficeExtension.BatchApiHelper.createChildItemObject,OfficeExtension.BatchApiHelper.invokeMethod,OfficeExtension.BatchApiHelper.invokeEnsureUnchanged,OfficeExtension.BatchApiHelper.invokeSetProperty,OfficeExtension.Utility.isNullOrUndefined,OfficeExtension.Utility.isUndefined,OfficeExtension.Utility.throwIfNotLoaded,OfficeExtension.Utility.throwIfApiNotSupported,OfficeExtension.Utility.load,OfficeExtension.Utility.retrieve,OfficeExtension.Utility.toJson,OfficeExtension.Utility.fixObjectPathIfNecessary,OfficeExtension.Utility._handleNavigationPropertyResults,OfficeExtension.Utility.adjustToDateTime,OfficeExtension.Utility.processRetrieveResult,OfficeExtension.Utility.setMockData,OfficeExtension.CommonUtility.calculateApiFlags;!function(e){e.generalException="GeneralException"}(t||(t={}));new OfficeExtension.LibraryBuilder({metadata:{version:"1.0.0",name:"OfficeCore",defaultApiSetName:"OfficeSharedApi",hostName:"Office",apiSets:[],strings:["AddinInternalService"],enumTypes:[],clientObjectTypes:[[1,0,0,0,[["notifyActionHandlerReady",0,2,0,4]],0,0,0,0,"Microsoft.InternalService.AddinInternalService",4]]},targetNamespaceObject:e})}(OfficeCore||(OfficeCore={})),function(e){var t;!function(t){var n,r=OfficeExtension.Utility.isNullOrUndefined;function i(){return function(e){n=e}(e.actions._association),(new OfficeExtension.ClientRequestContext).eventRegistration.register(5,"",s)}function o(e){if(e){var t=e.toUpperCase(),i=n.mappings[t];if(!r(i)&&"function"===typeof i)return i}throw OfficeExtension.Utility.createRuntimeError("invalidOperation","sourceData","ActionProxy._getFunction")}function s(e){try{OfficeExtension.Utility.log("ActionProxy._handleMessage"),OfficeExtension.Utility.checkArgumentNull(e,"args");for(var t=e.entries,n=0;n<t.length;n++)if(2===t[n].messageCategory)if("string"===typeof t[n].message&&(t[n].message=JSON.parse(t[n].message)),1e3===t[n].messageType){var r=null,i=o(t[n].message[0]);if(t[n].message.length>=2){var s=t[n].message[1];s&&(r=a(s)?JSON.parse(s):s)}i.apply(null,[r])}else OfficeExtension.Utility.log("ActionProxy._handleMessage unknown message type "+t[n].messageType)}catch(e){throw function(e){var t=function(e){var t="Unknown Error";if(e)try{e.toString&&(t=e.toString()),t=t+" "+JSON.stringify(e)}catch(e){t="Unexpected Error"}return t}(e);OfficeExtension.Utility.log(t)}(e),e}return OfficeExtension.Utility._createPromiseFromResult(null)}function a(e){return"string"===typeof e&&"{"===e[0]}function c(){try{Microsoft.Office.WebExtension.onReadyInternal().then((function(){return i()})).then((function(){return"web"===OSF._OfficeAppFactory.getHostInfo().hostPlatform&&"excel"!==OSF._OfficeAppFactory.getHostInfo().hostType?void 0:function(){var e=new OfficeExtension.ClientRequestContext,t=OfficeCore.AddinInternalService.newObject(e);return e._customData="WacPartition",t.notifyActionHandlerReady(),e.sync()}()}))}catch(e){}}!function(){OfficeExtension.Utility.log("ActionProxy.initOnce"),"undefined"!==typeof document&&(document.readyState&&"loading"!==document.readyState?(OfficeExtension.Utility.log("ActionProxy.initOnce: document.readyState is not loading state"),c()):document.addEventListener&&document.addEventListener("DOMContentLoaded",(function(){OfficeExtension.Utility.log("ActionProxy.initOnce: DOMContentLoaded event triggered"),c()})))}()}(t||(t={}))}(Office||(Office={}));var Visio;__extends=this&&this.__extends||function(){var e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var n in t)t.hasOwnProperty(n)&&(e[n]=t[n])};return function(t,n){function r(){this.constructor=t}e(t,n),t.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();!function(e){var t=OfficeExtension.BatchApiHelper.createPropertyObject,n=OfficeExtension.BatchApiHelper.createMethodObject,r=OfficeExtension.BatchApiHelper.createIndexerObject,i=(OfficeExtension.BatchApiHelper.createRootServiceObject,OfficeExtension.BatchApiHelper.createTopLevelServiceObject,OfficeExtension.BatchApiHelper.createChildItemObject),o=OfficeExtension.BatchApiHelper.invokeMethod,s=OfficeExtension.BatchApiHelper.invokeEnsureUnchanged,a=OfficeExtension.BatchApiHelper.invokeSetProperty,c=OfficeExtension.Utility.isNullOrUndefined,u=OfficeExtension.Utility.isUndefined,l=OfficeExtension.Utility.throwIfNotLoaded,p=(OfficeExtension.Utility.throwIfApiNotSupported,OfficeExtension.Utility.load),f=OfficeExtension.Utility.retrieve,h=OfficeExtension.Utility.toJson,d=OfficeExtension.Utility.fixObjectPathIfNecessary,m=OfficeExtension.Utility._handleNavigationPropertyResults,y=(OfficeExtension.Utility.adjustToDateTime,OfficeExtension.Utility.processRetrieveResult),g=OfficeExtension.Utility.setMockData,b=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return __extends(t,e),Object.defineProperty(t.prototype,"_className",{get:function(){return"Application"},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_scalarPropertyNames",{get:function(){return["showBorders","showToolbars"]},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_scalarPropertyOriginalNames",{get:function(){return["ShowBorders","ShowToolbars"]},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_scalarPropertyUpdateable",{get:function(){return[!0,!0]},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"showBorders",{get:function(){return l("showBorders",this._S,"Application",this._isNull),this._S},set:function(e){this._S=e,a(this,"ShowBorders",e,0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"showToolbars",{get:function(){return l("showToolbars",this._Sh,"Application",this._isNull),this._Sh},set:function(e){this._Sh=e,a(this,"ShowToolbars",e,0)},enumerable:!0,configurable:!0}),t.prototype.set=function(e,t){this._recursivelySet(e,t,["showBorders","showToolbars"],[],[])},t.prototype.update=function(e){this._recursivelyUpdate(e)},t.prototype.showToolbar=function(e,t){o(this,"ShowToolbar",0,[e,t],0,0)},t.prototype._handleResult=function(t){if(e.prototype._handleResult.call(this,t),!c(t)){var n=t;d(this,n),u(n.ShowBorders)||(this._S=n.ShowBorders),u(n.ShowToolbars)||(this._Sh=n.ShowToolbars)}},t.prototype.load=function(e){return p(this,e)},t.prototype.retrieve=function(e){return f(this,e)},t.prototype._handleRetrieveResult=function(t,n){e.prototype._handleRetrieveResult.call(this,t,n),y(this,t,n)},t.prototype.toJSON=function(){return h(this,{showBorders:this._S,showToolbars:this._Sh},{})},t.prototype.setMockData=function(e){g(this,e)},t.prototype.ensureUnchanged=function(e){s(this,e)},t}(OfficeExtension.ClientObject);e.Application=b;var _=function(r){function i(){return null!==r&&r.apply(this,arguments)||this}return __extends(i,r),Object.defineProperty(i.prototype,"_className",{get:function(){return"Document"},enumerable:!0,configurable:!0}),Object.defineProperty(i.prototype,"_navigationPropertyNames",{get:function(){return["view","application","pages"]},enumerable:!0,configurable:!0}),Object.defineProperty(i.prototype,"application",{get:function(){return this._A||(this._A=t(e.Application,this,"Application",!1,4)),this._A},enumerable:!0,configurable:!0}),Object.defineProperty(i.prototype,"pages",{get:function(){return this._P||(this._P=t(e.PageCollection,this,"Pages",!0,4)),this._P},enumerable:!0,configurable:!0}),Object.defineProperty(i.prototype,"view",{get:function(){return this._V||(this._V=t(e.DocumentView,this,"View",!1,4)),this._V},enumerable:!0,configurable:!0}),i.prototype.set=function(e,t){this._recursivelySet(e,t,[],["view","application"],["pages"])},i.prototype.update=function(e){this._recursivelyUpdate(e)},i.prototype.getActivePage=function(){return n(e.Page,this,"GetActivePage",1,[],!1,!1,null,4)},i.prototype.setActivePage=function(e){o(this,"SetActivePage",1,[e],4,0)},i.prototype.showTaskPane=function(e,t,n){o(this,"ShowTaskPane",1,[e,t,n],4,0)},i.prototype.startDataRefresh=function(){o(this,"StartDataRefresh",1,[],4,0)},i.prototype._RegisterDataVisualizerDiagramOperationCompletedEvent=function(){o(this,"_RegisterDataVisualizerDiagramOperationCompletedEvent",0,[],0,0)},i.prototype._UnregisterDataVisualizerDiagramOperationCompletedEvent=function(){o(this,"_UnregisterDataVisualizerDiagramOperationCompletedEvent",0,[],0,0)},i.prototype._handleResult=function(e){if(r.prototype._handleResult.call(this,e),!c(e)){var t=e;d(this,t),m(this,t,["application","Application","pages","Pages","view","View"])}},i.prototype.load=function(e){return p(this,e)},i.prototype.retrieve=function(e){return f(this,e)},i.prototype._handleRetrieveResult=function(e,t){r.prototype._handleRetrieveResult.call(this,e,t),y(this,e,t)},Object.defineProperty(i.prototype,"onDataRefreshComplete",{get:function(){var e=this;return this.m_dataRefreshComplete||(this.m_dataRefreshComplete=new OfficeExtension.EventHandlers(this.context,this,"DataRefreshComplete",{registerFunc:function(t){return e.context.eventRegistration.register(3,"",t)},unregisterFunc:function(t){return e.context.eventRegistration.unregister(3,"",t)},eventArgsTransformFunc:function(e){var t={document:this,success:e.ddaBinding.Object.success};return OfficeExtension.Utility._createPromiseFromResult(t)}})),this.m_dataRefreshComplete},enumerable:!0,configurable:!0}),Object.defineProperty(i.prototype,"onDataVisualizerDiagramOperationCompleted",{get:function(){return this.m_dataVisualizerDiagramOperationCompleted||(this.m_dataVisualizerDiagramOperationCompleted=new OfficeExtension.EventHandlers(this.context,this,"DataVisualizerDiagramOperationCompleted",null)),this.m_dataVisualizerDiagramOperationCompleted},enumerable:!0,configurable:!0}),Object.defineProperty(i.prototype,"onDocumentError",{get:function(){var e=this;return this.m_documentError||(this.m_documentError=new OfficeExtension.EventHandlers(this.context,this,"DocumentError",{registerFunc:function(t){return e.context.eventRegistration.register(15,"",t)},unregisterFunc:function(t){return e.context.eventRegistration.unregister(15,"",t)},eventArgsTransformFunc:function(e){return OfficeExtension.Utility._createPromiseFromResult(e.ddaBinding.Object)}})),this.m_documentError},enumerable:!0,configurable:!0}),Object.defineProperty(i.prototype,"onDocumentLoadComplete",{get:function(){var e=this;return this.m_documentLoadComplete||(this.m_documentLoadComplete=new OfficeExtension.EventHandlers(this.context,this,"DocumentLoadComplete",{registerFunc:function(t){return e.context.eventRegistration.register(7,"",t)},unregisterFunc:function(t){return e.context.eventRegistration.unregister(7,"",t)},eventArgsTransformFunc:function(e){var t={success:e.ddaBinding.Object.success};return OfficeExtension.Utility._createPromiseFromResult(t)}})),this.m_documentLoadComplete},enumerable:!0,configurable:!0}),Object.defineProperty(i.prototype,"onPageLoadComplete",{get:function(){var e=this;return this.m_pageLoadComplete||(this.m_pageLoadComplete=new OfficeExtension.EventHandlers(this.context,this,"PageLoadComplete",{registerFunc:function(t){return e.context.eventRegistration.register(1,"",t)},unregisterFunc:function(t){return e.context.eventRegistration.unregister(1,"",t)},eventArgsTransformFunc:function(e){return OfficeExtension.Utility._createPromiseFromResult(e.ddaBinding.Object)}})),this.m_pageLoadComplete},enumerable:!0,configurable:!0}),Object.defineProperty(i.prototype,"onSelectionChanged",{get:function(){var e=this;return this.m_selectionChanged||(this.m_selectionChanged=new OfficeExtension.EventHandlers(this.context,this,"SelectionChanged",{registerFunc:function(t){return e.context.eventRegistration.register(2,"",t)},unregisterFunc:function(t){return e.context.eventRegistration.unregister(2,"",t)},eventArgsTransformFunc:function(e){return OfficeExtension.Utility._createPromiseFromResult(e.ddaBinding.Object)}})),this.m_selectionChanged},enumerable:!0,configurable:!0}),Object.defineProperty(i.prototype,"onShapeMouseEnter",{get:function(){var e=this;return this.m_shapeMouseEnter||(this.m_shapeMouseEnter=new OfficeExtension.EventHandlers(this.context,this,"ShapeMouseEnter",{registerFunc:function(t){return e.context.eventRegistration.register(4,"",t)},unregisterFunc:function(t){return e.context.eventRegistration.unregister(4,"",t)},eventArgsTransformFunc:function(e){return OfficeExtension.Utility._createPromiseFromResult(e.ddaBinding.Object)}})),this.m_shapeMouseEnter},enumerable:!0,configurable:!0}),Object.defineProperty(i.prototype,"onShapeMouseLeave",{get:function(){var e=this;return this.m_shapeMouseLeave||(this.m_shapeMouseLeave=new OfficeExtension.EventHandlers(this.context,this,"ShapeMouseLeave",{registerFunc:function(t){return e.context.eventRegistration.register(5,"",t)},unregisterFunc:function(t){return e.context.eventRegistration.unregister(5,"",t)},eventArgsTransformFunc:function(e){return OfficeExtension.Utility._createPromiseFromResult(e.ddaBinding.Object)}})),this.m_shapeMouseLeave},enumerable:!0,configurable:!0}),Object.defineProperty(i.prototype,"onTaskPaneStateChanged",{get:function(){var e=this;return this.m_taskPaneStateChanged||(this.m_taskPaneStateChanged=new OfficeExtension.EventHandlers(this.context,this,"TaskPaneStateChanged",{registerFunc:function(t){return e.context.eventRegistration.register(18,"",t)},unregisterFunc:function(t){return e.context.eventRegistration.unregister(18,"",t)},eventArgsTransformFunc:function(e){return OfficeExtension.Utility._createPromiseFromResult(e.ddaBinding.Object)}})),this.m_taskPaneStateChanged},enumerable:!0,configurable:!0}),i.prototype.toJSON=function(){return h(this,{},{application:this._A,pages:this._P,view:this._V})},i.prototype.setMockData=function(e){g(this,e)},i.prototype.ensureUnchanged=function(e){s(this,e)},i}(OfficeExtension.ClientObject);e.Document=_;var v=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return __extends(t,e),Object.defineProperty(t.prototype,"_className",{get:function(){return"DocumentView"},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_scalarPropertyNames",{get:function(){return["disableHyperlinks","disableZoom","disablePan","hideDiagramBoundary","disablePanZoomWindow"]},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_scalarPropertyOriginalNames",{get:function(){return["DisableHyperlinks","DisableZoom","DisablePan","HideDiagramBoundary","DisablePanZoomWindow"]},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_scalarPropertyUpdateable",{get:function(){return[!0,!0,!0,!0,!0]},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"disableHyperlinks",{get:function(){return l("disableHyperlinks",this._D,"DocumentView",this._isNull),this._D},set:function(e){this._D=e,a(this,"DisableHyperlinks",e,0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"disablePan",{get:function(){return l("disablePan",this._Di,"DocumentView",this._isNull),this._Di},set:function(e){this._Di=e,a(this,"DisablePan",e,0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"disablePanZoomWindow",{get:function(){return l("disablePanZoomWindow",this._Dis,"DocumentView",this._isNull),this._Dis},set:function(e){this._Dis=e,a(this,"DisablePanZoomWindow",e,0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"disableZoom",{get:function(){return l("disableZoom",this._Disa,"DocumentView",this._isNull),this._Disa},set:function(e){this._Disa=e,a(this,"DisableZoom",e,0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"hideDiagramBoundary",{get:function(){return l("hideDiagramBoundary",this._H,"DocumentView",this._isNull),this._H},set:function(e){this._H=e,a(this,"HideDiagramBoundary",e,0)},enumerable:!0,configurable:!0}),t.prototype.set=function(e,t){this._recursivelySet(e,t,["disableHyperlinks","disableZoom","disablePan","hideDiagramBoundary","disablePanZoomWindow"],[],[])},t.prototype.update=function(e){this._recursivelyUpdate(e)},t.prototype._handleResult=function(t){if(e.prototype._handleResult.call(this,t),!c(t)){var n=t;d(this,n),u(n.DisableHyperlinks)||(this._D=n.DisableHyperlinks),u(n.DisablePan)||(this._Di=n.DisablePan),u(n.DisablePanZoomWindow)||(this._Dis=n.DisablePanZoomWindow),u(n.DisableZoom)||(this._Disa=n.DisableZoom),u(n.HideDiagramBoundary)||(this._H=n.HideDiagramBoundary)}},t.prototype.load=function(e){return p(this,e)},t.prototype.retrieve=function(e){return f(this,e)},t.prototype._handleRetrieveResult=function(t,n){e.prototype._handleRetrieveResult.call(this,t,n),y(this,t,n)},t.prototype.toJSON=function(){return h(this,{disableHyperlinks:this._D,disablePan:this._Di,disablePanZoomWindow:this._Dis,disableZoom:this._Disa,hideDiagramBoundary:this._H},{})},t.prototype.setMockData=function(e){g(this,e)},t.prototype.ensureUnchanged=function(e){s(this,e)},t}(OfficeExtension.ClientObject);e.DocumentView=v;var O=function(n){function r(){return null!==n&&n.apply(this,arguments)||this}return __extends(r,n),Object.defineProperty(r.prototype,"_className",{get:function(){return"Page"},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"_scalarPropertyNames",{get:function(){return["index","name","isBackground","width","height"]},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"_scalarPropertyOriginalNames",{get:function(){return["Index","Name","IsBackground","Width","Height"]},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"_navigationPropertyNames",{get:function(){return["shapes","view","comments","allShapes","dataVisualizerDiagrams"]},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"allShapes",{get:function(){return this._A||(this._A=t(e.ShapeCollection,this,"AllShapes",!0,4)),this._A},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"comments",{get:function(){return this._C||(this._C=t(e.CommentCollection,this,"Comments",!0,4)),this._C},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"dataVisualizerDiagrams",{get:function(){return this._D||(this._D=t(e.DataVisualizerDiagramCollection,this,"DataVisualizerDiagrams",!0,4)),this._D},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"shapes",{get:function(){return this._S||(this._S=t(e.ShapeCollection,this,"Shapes",!0,4)),this._S},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"view",{get:function(){return this._V||(this._V=t(e.PageView,this,"View",!1,4)),this._V},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"height",{get:function(){return l("height",this._H,"Page",this._isNull),this._H},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"index",{get:function(){return l("index",this._I,"Page",this._isNull),this._I},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"isBackground",{get:function(){return l("isBackground",this._Is,"Page",this._isNull),this._Is},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"name",{get:function(){return l("name",this._N,"Page",this._isNull),this._N},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"width",{get:function(){return l("width",this._W,"Page",this._isNull),this._W},enumerable:!0,configurable:!0}),r.prototype.set=function(e,t){this._recursivelySet(e,t,[],["view"],["allShapes","comments","dataVisualizerDiagrams","shapes"])},r.prototype.update=function(e){this._recursivelyUpdate(e)},r.prototype.activate=function(){o(this,"Activate",1,[],4,0)},r.prototype._handleResult=function(e){if(n.prototype._handleResult.call(this,e),!c(e)){var t=e;d(this,t),u(t.Height)||(this._H=t.Height),u(t.Index)||(this._I=t.Index),u(t.IsBackground)||(this._Is=t.IsBackground),u(t.Name)||(this._N=t.Name),u(t.Width)||(this._W=t.Width),m(this,t,["allShapes","AllShapes","comments","Comments","dataVisualizerDiagrams","DataVisualizerDiagrams","shapes","Shapes","view","View"])}},r.prototype.load=function(e){return p(this,e)},r.prototype.retrieve=function(e){return f(this,e)},r.prototype._handleRetrieveResult=function(e,t){n.prototype._handleRetrieveResult.call(this,e,t),y(this,e,t)},r.prototype.toJSON=function(){return h(this,{height:this._H,index:this._I,isBackground:this._Is,name:this._N,width:this._W},{allShapes:this._A,comments:this._C,dataVisualizerDiagrams:this._D,shapes:this._S,view:this._V})},r.prototype.setMockData=function(e){g(this,e)},r.prototype.ensureUnchanged=function(e){s(this,e)},r}(OfficeExtension.ClientObject);e.Page=O;var P=function(t){function r(){return null!==t&&t.apply(this,arguments)||this}return __extends(r,t),Object.defineProperty(r.prototype,"_className",{get:function(){return"PageView"},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"_scalarPropertyNames",{get:function(){return["zoom"]},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"_scalarPropertyOriginalNames",{get:function(){return["Zoom"]},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"_scalarPropertyUpdateable",{get:function(){return[!0]},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"zoom",{get:function(){return l("zoom",this._Z,"PageView",this._isNull),this._Z},set:function(e){this._Z=e,a(this,"Zoom",e,0)},enumerable:!0,configurable:!0}),r.prototype.set=function(e,t){this._recursivelySet(e,t,["zoom"],[],[])},r.prototype.update=function(e){this._recursivelyUpdate(e)},r.prototype.centerViewportOnShape=function(e){o(this,"CenterViewportOnShape",1,[e],4,0)},r.prototype.fitToWindow=function(){o(this,"FitToWindow",1,[],4,0)},r.prototype.getPosition=function(){return o(this,"GetPosition",1,[],4,0)},r.prototype.getSelection=function(){return n(e.Selection,this,"GetSelection",1,[],!1,!1,null,4)},r.prototype.isShapeInViewport=function(e){return o(this,"IsShapeInViewport",1,[e],4,0)},r.prototype.setPosition=function(e){o(this,"SetPosition",1,[e],4,0)},r.prototype._handleResult=function(e){if(t.prototype._handleResult.call(this,e),!c(e)){var n=e;d(this,n),u(n.Zoom)||(this._Z=n.Zoom)}},r.prototype.load=function(e){return p(this,e)},r.prototype.retrieve=function(e){return f(this,e)},r.prototype._handleRetrieveResult=function(e,n){t.prototype._handleRetrieveResult.call(this,e,n),y(this,e,n)},r.prototype.toJSON=function(){return h(this,{zoom:this._Z},{})},r.prototype.setMockData=function(e){g(this,e)},r.prototype.ensureUnchanged=function(e){s(this,e)},r}(OfficeExtension.ClientObject);e.PageView=P;var I=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return __extends(n,t),Object.defineProperty(n.prototype,"_className",{get:function(){return"PageCollection"},enumerable:!0,configurable:!0}),Object.defineProperty(n.prototype,"_isCollection",{get:function(){return!0},enumerable:!0,configurable:!0}),Object.defineProperty(n.prototype,"items",{get:function(){return l("items",this.m__items,"PageCollection",this._isNull),this.m__items},enumerable:!0,configurable:!0}),n.prototype.getCount=function(){return o(this,"GetCount",1,[],4,0)},n.prototype.getItem=function(t){return r(e.Page,this,[t])},n.prototype._handleResult=function(n){if(t.prototype._handleResult.call(this,n),!c(n)){var r=n;if(d(this,r),!c(r[OfficeExtension.Constants.items])){this.m__items=[];for(var o=r[OfficeExtension.Constants.items],s=0;s<o.length;s++){var a=i(e.Page,!0,this,o[s],s);a._handleResult(o[s]),this.m__items.push(a)}}}},n.prototype.load=function(e){return p(this,e)},n.prototype.retrieve=function(e){return f(this,e)},n.prototype._handleRetrieveResult=function(n,r){var o=this;t.prototype._handleRetrieveResult.call(this,n,r),y(this,n,r,(function(t,n){return i(e.Page,!0,o,t,n)}))},n.prototype.toJSON=function(){return h(this,{},{},this.m__items)},n.prototype.setMockData=function(t){var n=this;g(this,t,(function(t,r){return i(e.Page,!0,n,t,r)}),(function(e){return n.m__items=e}))},n}(OfficeExtension.ClientObject);e.PageCollection=I;var R=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return __extends(n,t),Object.defineProperty(n.prototype,"_className",{get:function(){return"ShapeCollection"},enumerable:!0,configurable:!0}),Object.defineProperty(n.prototype,"_isCollection",{get:function(){return!0},enumerable:!0,configurable:!0}),Object.defineProperty(n.prototype,"items",{get:function(){return l("items",this.m__items,"ShapeCollection",this._isNull),this.m__items},enumerable:!0,configurable:!0}),n.prototype.getCount=function(){return o(this,"GetCount",1,[],4,0)},n.prototype.getItem=function(t){return r(e.Shape,this,[t])},n.prototype._handleResult=function(n){if(t.prototype._handleResult.call(this,n),!c(n)){var r=n;if(d(this,r),!c(r[OfficeExtension.Constants.items])){this.m__items=[];for(var o=r[OfficeExtension.Constants.items],s=0;s<o.length;s++){var a=i(e.Shape,!0,this,o[s],s);a._handleResult(o[s]),this.m__items.push(a)}}}},n.prototype.load=function(e){return p(this,e)},n.prototype.retrieve=function(e){return f(this,e)},n.prototype._handleRetrieveResult=function(n,r){var o=this;t.prototype._handleRetrieveResult.call(this,n,r),y(this,n,r,(function(t,n){return i(e.Shape,!0,o,t,n)}))},n.prototype.toJSON=function(){return h(this,{},{},this.m__items)},n.prototype.setMockData=function(t){var n=this;g(this,t,(function(t,r){return i(e.Shape,!0,n,t,r)}),(function(e){return n.m__items=e}))},n}(OfficeExtension.ClientObject);e.ShapeCollection=R;var j=function(n){function r(){return null!==n&&n.apply(this,arguments)||this}return __extends(r,n),Object.defineProperty(r.prototype,"_className",{get:function(){return"Shape"},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"_scalarPropertyNames",{get:function(){return["name","id","text","select","isBoundToData"]},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"_scalarPropertyOriginalNames",{get:function(){return["Name","Id","Text","Select","IsBoundToData"]},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"_scalarPropertyUpdateable",{get:function(){return[!1,!1,!1,!0,!1]},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"_navigationPropertyNames",{get:function(){return["hyperlinks","shapeDataItems","view","subShapes","comments"]},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"comments",{get:function(){return this._C||(this._C=t(e.CommentCollection,this,"Comments",!0,4)),this._C},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"hyperlinks",{get:function(){return this._H||(this._H=t(e.HyperlinkCollection,this,"Hyperlinks",!0,4)),this._H},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"shapeDataItems",{get:function(){return this._Sh||(this._Sh=t(e.ShapeDataItemCollection,this,"ShapeDataItems",!0,4)),this._Sh},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"subShapes",{get:function(){return this._Su||(this._Su=t(e.ShapeCollection,this,"SubShapes",!0,4)),this._Su},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"view",{get:function(){return this._V||(this._V=t(e.ShapeView,this,"View",!1,4)),this._V},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"id",{get:function(){return l("id",this._I,"Shape",this._isNull),this._I},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"isBoundToData",{get:function(){return l("isBoundToData",this._Is,"Shape",this._isNull),this._Is},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"name",{get:function(){return l("name",this._N,"Shape",this._isNull),this._N},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"select",{get:function(){return l("select",this._S,"Shape",this._isNull),this._S},set:function(e){this._S=e,a(this,"Select",e,0)},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"text",{get:function(){return l("text",this._T,"Shape",this._isNull),this._T},enumerable:!0,configurable:!0}),r.prototype.set=function(e,t){this._recursivelySet(e,t,["select"],["view"],["comments","hyperlinks","shapeDataItems","subShapes"])},r.prototype.update=function(e){this._recursivelyUpdate(e)},r.prototype.getBounds=function(){return o(this,"GetBounds",1,[],4,0)},r.prototype._handleResult=function(e){if(n.prototype._handleResult.call(this,e),!c(e)){var t=e;d(this,t),u(t.Id)||(this._I=t.Id),u(t.IsBoundToData)||(this._Is=t.IsBoundToData),u(t.Name)||(this._N=t.Name),u(t.Select)||(this._S=t.Select),u(t.Text)||(this._T=t.Text),m(this,t,["comments","Comments","hyperlinks","Hyperlinks","shapeDataItems","ShapeDataItems","subShapes","SubShapes","view","View"])}},r.prototype.load=function(e){return p(this,e)},r.prototype.retrieve=function(e){return f(this,e)},r.prototype._handleIdResult=function(e){n.prototype._handleIdResult.call(this,e),c(e)||u(e.Id)||(this._I=e.Id)},r.prototype._handleRetrieveResult=function(e,t){n.prototype._handleRetrieveResult.call(this,e,t),y(this,e,t)},r.prototype.toJSON=function(){return h(this,{id:this._I,isBoundToData:this._Is,name:this._N,select:this._S,text:this._T},{comments:this._C,hyperlinks:this._H,shapeDataItems:this._Sh,subShapes:this._Su,view:this._V})},r.prototype.setMockData=function(e){g(this,e)},r.prototype.ensureUnchanged=function(e){s(this,e)},r}(OfficeExtension.ClientObject);e.Shape=j;var A=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return __extends(t,e),Object.defineProperty(t.prototype,"_className",{get:function(){return"ShapeView"},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_scalarPropertyNames",{get:function(){return["highlight"]},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_scalarPropertyOriginalNames",{get:function(){return["Highlight"]},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_scalarPropertyUpdateable",{get:function(){return[!0]},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"highlight",{get:function(){return l("highlight",this._H,"ShapeView",this._isNull),this._H},set:function(e){this._H=e,a(this,"Highlight",e,0)},enumerable:!0,configurable:!0}),t.prototype.set=function(e,t){this._recursivelySet(e,t,["highlight"],[],[])},t.prototype.update=function(e){this._recursivelyUpdate(e)},t.prototype.addOverlay=function(e,t,n,r,i,s){return o(this,"AddOverlay",1,[e,t,n,r,i,s],4,0)},t.prototype.removeOverlay=function(e){o(this,"RemoveOverlay",1,[e],4,0)},t.prototype.showOverlay=function(e,t){o(this,"ShowOverlay",1,[e,t],4,0)},t.prototype._handleResult=function(t){if(e.prototype._handleResult.call(this,t),!c(t)){var n=t;d(this,n),u(n.Highlight)||(this._H=n.Highlight)}},t.prototype.load=function(e){return p(this,e)},t.prototype.retrieve=function(e){return f(this,e)},t.prototype._handleRetrieveResult=function(t,n){e.prototype._handleRetrieveResult.call(this,t,n),y(this,t,n)},t.prototype.toJSON=function(){return h(this,{highlight:this._H},{})},t.prototype.setMockData=function(e){g(this,e)},t.prototype.ensureUnchanged=function(e){s(this,e)},t}(OfficeExtension.ClientObject);e.ShapeView=A;var C=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return __extends(n,t),Object.defineProperty(n.prototype,"_className",{get:function(){return"ShapeDataItemCollection"},enumerable:!0,configurable:!0}),Object.defineProperty(n.prototype,"_isCollection",{get:function(){return!0},enumerable:!0,configurable:!0}),Object.defineProperty(n.prototype,"items",{get:function(){return l("items",this.m__items,"ShapeDataItemCollection",this._isNull),this.m__items},enumerable:!0,configurable:!0}),n.prototype.getCount=function(){return o(this,"GetCount",1,[],4,0)},n.prototype.getItem=function(t){return r(e.ShapeDataItem,this,[t])},n.prototype._handleResult=function(n){if(t.prototype._handleResult.call(this,n),!c(n)){var r=n;if(d(this,r),!c(r[OfficeExtension.Constants.items])){this.m__items=[];for(var o=r[OfficeExtension.Constants.items],s=0;s<o.length;s++){var a=i(e.ShapeDataItem,!0,this,o[s],s);a._handleResult(o[s]),this.m__items.push(a)}}}},n.prototype.load=function(e){return p(this,e)},n.prototype.retrieve=function(e){return f(this,e)},n.prototype._handleRetrieveResult=function(n,r){var o=this;t.prototype._handleRetrieveResult.call(this,n,r),y(this,n,r,(function(t,n){return i(e.ShapeDataItem,!0,o,t,n)}))},n.prototype.toJSON=function(){return h(this,{},{},this.m__items)},n.prototype.setMockData=function(t){var n=this;g(this,t,(function(t,r){return i(e.ShapeDataItem,!0,n,t,r)}),(function(e){return n.m__items=e}))},n}(OfficeExtension.ClientObject);e.ShapeDataItemCollection=C;var S=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return __extends(t,e),Object.defineProperty(t.prototype,"_className",{get:function(){return"ShapeDataItem"},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_scalarPropertyNames",{get:function(){return["label","value","format","formattedValue"]},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_scalarPropertyOriginalNames",{get:function(){return["Label","Value","Format","FormattedValue"]},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"format",{get:function(){return l("format",this._F,"ShapeDataItem",this._isNull),this._F},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"formattedValue",{get:function(){return l("formattedValue",this._Fo,"ShapeDataItem",this._isNull),this._Fo},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"label",{get:function(){return l("label",this._L,"ShapeDataItem",this._isNull),this._L},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"value",{get:function(){return l("value",this._V,"ShapeDataItem",this._isNull),this._V},enumerable:!0,configurable:!0}),t.prototype._handleResult=function(t){if(e.prototype._handleResult.call(this,t),!c(t)){var n=t;d(this,n),u(n.Format)||(this._F=n.Format),u(n.FormattedValue)||(this._Fo=n.FormattedValue),u(n.Label)||(this._L=n.Label),u(n.Value)||(this._V=n.Value)}},t.prototype.load=function(e){return p(this,e)},t.prototype.retrieve=function(e){return f(this,e)},t.prototype._handleRetrieveResult=function(t,n){e.prototype._handleRetrieveResult.call(this,t,n),y(this,t,n)},t.prototype.toJSON=function(){return h(this,{format:this._F,formattedValue:this._Fo,label:this._L,value:this._V},{})},t.prototype.setMockData=function(e){g(this,e)},t.prototype.ensureUnchanged=function(e){s(this,e)},t}(OfficeExtension.ClientObject);e.ShapeDataItem=S;var E=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return __extends(n,t),Object.defineProperty(n.prototype,"_className",{get:function(){return"HyperlinkCollection"},enumerable:!0,configurable:!0}),Object.defineProperty(n.prototype,"_isCollection",{get:function(){return!0},enumerable:!0,configurable:!0}),Object.defineProperty(n.prototype,"items",{get:function(){return l("items",this.m__items,"HyperlinkCollection",this._isNull),this.m__items},enumerable:!0,configurable:!0}),n.prototype.getCount=function(){return o(this,"GetCount",1,[],4,0)},n.prototype.getItem=function(t){return r(e.Hyperlink,this,[t])},n.prototype._handleResult=function(n){if(t.prototype._handleResult.call(this,n),!c(n)){var r=n;if(d(this,r),!c(r[OfficeExtension.Constants.items])){this.m__items=[];for(var o=r[OfficeExtension.Constants.items],s=0;s<o.length;s++){var a=i(e.Hyperlink,!0,this,o[s],s);a._handleResult(o[s]),this.m__items.push(a)}}}},n.prototype.load=function(e){return p(this,e)},n.prototype.retrieve=function(e){return f(this,e)},n.prototype._handleRetrieveResult=function(n,r){var o=this;t.prototype._handleRetrieveResult.call(this,n,r),y(this,n,r,(function(t,n){return i(e.Hyperlink,!0,o,t,n)}))},n.prototype.toJSON=function(){return h(this,{},{},this.m__items)},n.prototype.setMockData=function(t){var n=this;g(this,t,(function(t,r){return i(e.Hyperlink,!0,n,t,r)}),(function(e){return n.m__items=e}))},n}(OfficeExtension.ClientObject);e.HyperlinkCollection=E;var x=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return __extends(t,e),Object.defineProperty(t.prototype,"_className",{get:function(){return"Hyperlink"},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_scalarPropertyNames",{get:function(){return["address","subAddress","description","extraInfo"]},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_scalarPropertyOriginalNames",{get:function(){return["Address","SubAddress","Description","ExtraInfo"]},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"address",{get:function(){return l("address",this._A,"Hyperlink",this._isNull),this._A},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"description",{get:function(){return l("description",this._D,"Hyperlink",this._isNull),this._D},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"extraInfo",{get:function(){return l("extraInfo",this._E,"Hyperlink",this._isNull),this._E},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"subAddress",{get:function(){return l("subAddress",this._S,"Hyperlink",this._isNull),this._S},enumerable:!0,configurable:!0}),t.prototype._handleResult=function(t){if(e.prototype._handleResult.call(this,t),!c(t)){var n=t;d(this,n),u(n.Address)||(this._A=n.Address),u(n.Description)||(this._D=n.Description),u(n.ExtraInfo)||(this._E=n.ExtraInfo),u(n.SubAddress)||(this._S=n.SubAddress)}},t.prototype.load=function(e){return p(this,e)},t.prototype.retrieve=function(e){return f(this,e)},t.prototype._handleRetrieveResult=function(t,n){e.prototype._handleRetrieveResult.call(this,t,n),y(this,t,n)},t.prototype.toJSON=function(){return h(this,{address:this._A,description:this._D,extraInfo:this._E,subAddress:this._S},{})},t.prototype.setMockData=function(e){g(this,e)},t.prototype.ensureUnchanged=function(e){s(this,e)},t}(OfficeExtension.ClientObject);e.Hyperlink=x;var N=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return __extends(n,t),Object.defineProperty(n.prototype,"_className",{get:function(){return"CommentCollection"},enumerable:!0,configurable:!0}),Object.defineProperty(n.prototype,"_isCollection",{get:function(){return!0},enumerable:!0,configurable:!0}),Object.defineProperty(n.prototype,"items",{get:function(){return l("items",this.m__items,"CommentCollection",this._isNull),this.m__items},enumerable:!0,configurable:!0}),n.prototype.getCount=function(){return o(this,"GetCount",1,[],4,0)},n.prototype.getItem=function(t){return r(e.Comment,this,[t])},n.prototype._handleResult=function(n){if(t.prototype._handleResult.call(this,n),!c(n)){var r=n;if(d(this,r),!c(r[OfficeExtension.Constants.items])){this.m__items=[];for(var o=r[OfficeExtension.Constants.items],s=0;s<o.length;s++){var a=i(e.Comment,!0,this,o[s],s);a._handleResult(o[s]),this.m__items.push(a)}}}},n.prototype.load=function(e){return p(this,e)},n.prototype.retrieve=function(e){return f(this,e)},n.prototype._handleRetrieveResult=function(n,r){var o=this;t.prototype._handleRetrieveResult.call(this,n,r),y(this,n,r,(function(t,n){return i(e.Comment,!0,o,t,n)}))},n.prototype.toJSON=function(){return h(this,{},{},this.m__items)},n.prototype.setMockData=function(t){var n=this;g(this,t,(function(t,r){return i(e.Comment,!0,n,t,r)}),(function(e){return n.m__items=e}))},n}(OfficeExtension.ClientObject);e.CommentCollection=N;var T=function(e){function t(){return null!==e&&e.apply(this,arguments)||this}return __extends(t,e),Object.defineProperty(t.prototype,"_className",{get:function(){return"Comment"},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_scalarPropertyNames",{get:function(){return["author","text","date"]},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_scalarPropertyOriginalNames",{get:function(){return["Author","Text","Date"]},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"_scalarPropertyUpdateable",{get:function(){return[!0,!0,!0]},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"author",{get:function(){return l("author",this._A,"Comment",this._isNull),this._A},set:function(e){this._A=e,a(this,"Author",e,0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"date",{get:function(){return l("date",this._D,"Comment",this._isNull),this._D},set:function(e){this._D=e,a(this,"Date",e,0)},enumerable:!0,configurable:!0}),Object.defineProperty(t.prototype,"text",{get:function(){return l("text",this._T,"Comment",this._isNull),this._T},set:function(e){this._T=e,a(this,"Text",e,0)},enumerable:!0,configurable:!0}),t.prototype.set=function(e,t){this._recursivelySet(e,t,["author","text","date"],[],[])},t.prototype.update=function(e){this._recursivelyUpdate(e)},t.prototype._handleResult=function(t){if(e.prototype._handleResult.call(this,t),!c(t)){var n=t;d(this,n),u(n.Author)||(this._A=n.Author),u(n.Date)||(this._D=n.Date),u(n.Text)||(this._T=n.Text)}},t.prototype.load=function(e){return p(this,e)},t.prototype.retrieve=function(e){return f(this,e)},t.prototype._handleRetrieveResult=function(t,n){e.prototype._handleRetrieveResult.call(this,t,n),y(this,t,n)},t.prototype.toJSON=function(){return h(this,{author:this._A,date:this._D,text:this._T},{})},t.prototype.setMockData=function(e){g(this,e)},t.prototype.ensureUnchanged=function(e){s(this,e)},t}(OfficeExtension.ClientObject);e.Comment=T;var w=function(n){function r(){return null!==n&&n.apply(this,arguments)||this}return __extends(r,n),Object.defineProperty(r.prototype,"_className",{get:function(){return"Selection"},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"_navigationPropertyNames",{get:function(){return["shapes"]},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"shapes",{get:function(){return this._S||(this._S=t(e.ShapeCollection,this,"Shapes",!0,4)),this._S},enumerable:!0,configurable:!0}),r.prototype._handleResult=function(e){if(n.prototype._handleResult.call(this,e),!c(e)){var t=e;d(this,t),m(this,t,["shapes","Shapes"])}},r.prototype.load=function(e){return p(this,e)},r.prototype.retrieve=function(e){return f(this,e)},r.prototype._handleRetrieveResult=function(e,t){n.prototype._handleRetrieveResult.call(this,e,t),y(this,e,t)},r.prototype.toJSON=function(){return h(this,{},{shapes:this._S})},r}(OfficeExtension.ClientObject);e.Selection=w;var U=function(t){function s(){return null!==t&&t.apply(this,arguments)||this}return __extends(s,t),Object.defineProperty(s.prototype,"_className",{get:function(){return"DataVisualizerDiagramCollection"},enumerable:!0,configurable:!0}),Object.defineProperty(s.prototype,"_isCollection",{get:function(){return!0},enumerable:!0,configurable:!0}),Object.defineProperty(s.prototype,"items",{get:function(){return l("items",this.m__items,"DataVisualizerDiagramCollection",this._isNull),this.m__items},enumerable:!0,configurable:!0}),s.prototype.add=function(t,r){return n(e.DataVisualizerDiagram,this,"Add",1,[t,r],!1,!0,null,4)},s.prototype.addPreferred=function(t,r){return n(e.DataVisualizerDiagram,this,"AddPreferred",0,[t,r],!1,!1,null,0)},s.prototype.getCount=function(){return o(this,"GetCount",1,[],4,0)},s.prototype.getItem=function(t){return r(e.DataVisualizerDiagram,this,[t])},s.prototype.getItemAt=function(t){return n(e.DataVisualizerDiagram,this,"GetItemAt",1,[t],!1,!1,null,4)},s.prototype.getItemOrNullObject=function(t){return n(e.DataVisualizerDiagram,this,"GetItemOrNullObject",1,[t],!1,!1,null,4)},s.prototype._handleResult=function(n){if(t.prototype._handleResult.call(this,n),!c(n)){var r=n;if(d(this,r),!c(r[OfficeExtension.Constants.items])){this.m__items=[];for(var o=r[OfficeExtension.Constants.items],s=0;s<o.length;s++){var a=i(e.DataVisualizerDiagram,!0,this,o[s],s);a._handleResult(o[s]),this.m__items.push(a)}}}},s.prototype.load=function(e){return p(this,e)},s.prototype.retrieve=function(e){return f(this,e)},s.prototype._handleRetrieveResult=function(n,r){var o=this;t.prototype._handleRetrieveResult.call(this,n,r),y(this,n,r,(function(t,n){return i(e.DataVisualizerDiagram,!0,o,t,n)}))},s.prototype.toJSON=function(){return h(this,{},{},this.m__items)},s.prototype.setMockData=function(t){var n=this;g(this,t,(function(t,r){return i(e.DataVisualizerDiagram,!0,n,t,r)}),(function(e){return n.m__items=e}))},s}(OfficeExtension.ClientObject);e.DataVisualizerDiagramCollection=U;var F=function(n){function r(){return null!==n&&n.apply(this,arguments)||this}return __extends(r,n),Object.defineProperty(r.prototype,"_className",{get:function(){return"DataVisualizerDiagram"},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"_scalarPropertyNames",{get:function(){return["id","mappings","type","dataHeaders"]},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"_scalarPropertyOriginalNames",{get:function(){return["ID","Mappings","Type","DataHeaders"]},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"_navigationPropertyNames",{get:function(){return["page"]},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"page",{get:function(){return this._P||(this._P=t(e.Page,this,"Page",!1,4)),this._P},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"dataHeaders",{get:function(){return l("dataHeaders",this._D,"DataVisualizerDiagram",this._isNull),this._D},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"id",{get:function(){return l("id",this._I,"DataVisualizerDiagram",this._isNull),this._I},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"mappings",{get:function(){return l("mappings",this._M,"DataVisualizerDiagram",this._isNull),this._M},enumerable:!0,configurable:!0}),Object.defineProperty(r.prototype,"type",{get:function(){return l("type",this._T,"DataVisualizerDiagram",this._isNull),this._T},enumerable:!0,configurable:!0}),r.prototype.set=function(e,t){this._recursivelySet(e,t,[],["page"],[])},r.prototype.delete=function(){o(this,"Delete",1,[],4,0)},r.prototype.getDataColumnValuesAsString=function(e){return o(this,"GetDataColumnValuesAsString",1,[e],4,0)},r.prototype.setConnection=function(e){o(this,"SetConnection",1,[e],4,0)},r.prototype.update=function(e,t,n){o(this,"Update",1,[e,t,n],4,0)},r.prototype.updateMappings=function(e){o(this,"UpdateMappings",1,[e],4,0)},r.prototype._handleResult=function(e){if(n.prototype._handleResult.call(this,e),!c(e)){var t=e;d(this,t),u(t.DataHeaders)||(this._D=t.DataHeaders),u(t.ID)||(this._I=t.ID),u(t.Mappings)||(this._M=t.Mappings),u(t.Type)||(this._T=t.Type),m(this,t,["page","Page"])}},r.prototype.load=function(e){return p(this,e)},r.prototype.retrieve=function(e){return f(this,e)},r.prototype._handleRetrieveResult=function(e,t){n.prototype._handleRetrieveResult.call(this,e,t),y(this,e,t)},r.prototype.toJSON=function(){return h(this,{dataHeaders:this._D,id:this._I,mappings:this._M,type:this._T},{page:this._P})},r.prototype.setMockData=function(e){g(this,e)},r.prototype.ensureUnchanged=function(e){s(this,e)},r}(OfficeExtension.ClientObject);e.DataVisualizerDiagram=F,function(e){e.left="Left",e.center="Center",e.right="Right"}(e.OverlayHorizontalAlignment||(e.OverlayHorizontalAlignment={})),function(e){e.top="Top",e.middle="Middle",e.bottom="Bottom"}(e.OverlayVerticalAlignment||(e.OverlayVerticalAlignment={})),function(e){e.text="Text",e.image="Image",e.html="Html"}(e.OverlayType||(e.OverlayType={})),function(e){e.commandBar="CommandBar",e.pageNavigationBar="PageNavigationBar",e.statusBar="StatusBar"}(e.ToolBarType||(e.ToolBarType={})),function(e){e.success="Success",e.unexpected="Unexpected",e.validationError="ValidationError",e.conflictError="ConflictError"}(e.DataVisualizerDiagramResultType||(e.DataVisualizerDiagramResultType={})),function(e){e.unknown="Unknown",e.create="Create",e.updateMappings="UpdateMappings",e.updateData="UpdateData",e.update="Update",e.delete="Delete"}(e.DataVisualizerDiagramOperationType||(e.DataVisualizerDiagramOperationType={})),function(e){e.unknown="Unknown",e.basicFlowchart="BasicFlowchart",e.crossFunctionalFlowchart_Horizontal="CrossFunctionalFlowchart_Horizontal",e.crossFunctionalFlowchart_Vertical="CrossFunctionalFlowchart_Vertical",e.audit="Audit",e.orgChart="OrgChart",e.network="Network"}(e.DataVisualizerDiagramType||(e.DataVisualizerDiagramType={})),function(e){e.unknown="Unknown",e.string="String",e.number="Number",e.date="Date",e.currency="Currency"}(e.ColumnType||(e.ColumnType={})),function(e){e.unknown="Unknown",e.excel="Excel"}(e.DataSourceType||(e.DataSourceType={})),function(e){e.horizontal="Horizontal",e.vertical="Vertical"}(e.CrossFunctionalFlowchartOrientation||(e.CrossFunctionalFlowchartOrientation={})),function(e){e.unknown="Unknown",e.pageDefault="PageDefault",e.flowchart_TopToBottom="Flowchart_TopToBottom",e.flowchart_BottomToTop="Flowchart_BottomToTop",e.flowchart_LeftToRight="Flowchart_LeftToRight",e.flowchart_RightToLeft="Flowchart_RightToLeft",e.wideTree_DownThenRight="WideTree_DownThenRight",e.wideTree_DownThenLeft="WideTree_DownThenLeft",e.wideTree_RightThenDown="WideTree_RightThenDown",e.wideTree_LeftThenDown="WideTree_LeftThenDown"}(e.LayoutVariant||(e.LayoutVariant={})),function(e){e.none="None",e.columnNotMapped="ColumnNotMapped",e.uniqueIdColumnError="UniqueIdColumnError",e.swimlaneColumnError="SwimlaneColumnError",e.delimiterError="DelimiterError",e.connectorColumnError="ConnectorColumnError",e.connectorColumnMappedElsewhere="ConnectorColumnMappedElsewhere",e.connectorLabelColumnMappedElsewhere="ConnectorLabelColumnMappedElsewhere",e.connectorColumnAndConnectorLabelMappedElsewhere="ConnectorColumnAndConnectorLabelMappedElsewhere"}(e.DataValidationErrorType||(e.DataValidationErrorType={})),function(e){e.fromTarget="FromTarget",e.toTarget="ToTarget"}(e.ConnectorDirection||(e.ConnectorDirection={})),function(e){e.none="None",e.dataVisualizerProcessMappings="DataVisualizerProcessMappings",e.dataVisualizerOrgChartMappings="DataVisualizerOrgChartMappings"}(e.TaskPaneType||(e.TaskPaneType={})),function(e){e.dataVisualizerDiagramOperationCompleted="DataVisualizerDiagramOperationCompleted"}(e.EventType||(e.EventType={})),function(e){e.accessDenied="AccessDenied",e.generalException="GeneralException",e.invalidArgument="InvalidArgument",e.itemNotFound="ItemNotFound",e.notImplemented="NotImplemented",e.unsupportedOperation="UnsupportedOperation"}(e.ErrorCodes||(e.ErrorCodes={})),e.Interfaces||(e.Interfaces={})}(Visio||(Visio={})),Object.defineProperty(OfficeExtension.SessionBase,"_overrideSession",{get:function(){return this._overrideSessionInternal?this._overrideSessionInternal:OfficeExtension.ClientRequestContext?OfficeExtension.ClientRequestContext._overrideSession:void 0},set:function(e){this._overrideSessionInternal=e},enumerable:!0,configurable:!0}),function(e){var t=function(t){function n(n){var r=t.call(this,n)||this;return r.m_document=new e.Document(r,OfficeExtension.ObjectPathFactory.createGlobalObjectObjectPath(r)),r._rootObject=r.m_document,r}return __extends(n,t),Object.defineProperty(n.prototype,"document",{get:function(){return this.m_document},enumerable:!0,configurable:!0}),n}(OfficeCore.RequestContext);e.RequestContext=t,e.run=function(t,n,r){return OfficeExtension.ClientRequestContext._runBatch("Visio.run",arguments,(function(t){return new e.RequestContext(t)}))}}(Visio||(Visio={})),OfficeExtension.Utility._doApiNotSupportedCheck=!0;