/**
 * Application Configuration
 * 
 * Central place for all configuration settings.
 * Update these values with your actual SharePoint and Azure AD settings.
 */
const AppConfig = {
    // SharePoint configuration
    sharePoint: {
        // SharePoint site URL
        siteUrl: "https://denobelsoftware.sharepoint.com/sites/DNSTest",
        
        // SharePoint list names
        lists: {
            services: "TestLijst" // Updated to match the list name used in test-api.html
        }
    },
    
    // Authentication configuration
    auth: {
        // Azure AD application (client) ID
        clientId: "a6bff1ae-205b-4896-9794-2b3c7db472a7",
        
        // Authentication endpoints
        endpoints: {
            graph: "https://graph.microsoft.com",
            sharePoint: "https://denobelsoftware.sharepoint.com/sites/DNSTest"
        },
        
        // Authentication redirect URL (relative to origin)
        redirectUrl: "/authcomplete.html"
    },
    
    // Cache configuration
    cache: {
        // Default cache duration in milliseconds (5 minutes)
        defaultDuration: 5 * 60 * 1000
    }
};
