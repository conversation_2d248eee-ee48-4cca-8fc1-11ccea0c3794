using Microsoft.Graph;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace TK_TaakvensterWeb
{
    /// <summary>
    /// Interface for SharePoint services
    /// </summary>
    public interface ISharePointService
    {
        /// <summary>
        /// Gets a SharePoint site by URL
        /// </summary>
        /// <param name="graphClient">Authenticated Graph client</param>
        /// <param name="siteUrl">Site relative URL</param>
        /// <param name="hostName">SharePoint host name</param>
        /// <returns>Site information</returns>
        Task<Site> GetSiteByUrl(GraphServiceClient graphClient, string siteUrl, string hostName);
        
        /// <summary>
        /// Gets a SharePoint list by name
        /// </summary>
        /// <param name="graphClient">Authenticated Graph client</param>
        /// <param name="siteId">Site ID</param>
        /// <param name="listName">List name</param>
        /// <returns>List information</returns>
        Task<List> GetListByName(GraphServiceClient graphClient, string siteId, string listName);
        
        /// <summary>
        /// Gets SharePoint list items with optional filtering
        /// </summary>
        /// <param name="graphClient">Authenticated Graph client</param>
        /// <param name="siteId">Site ID</param>
        /// <param name="listId">List ID</param>
        /// <param name="filter">Optional OData filter</param>
        /// <param name="orderBy">Optional OData orderby</param>
        /// <param name="top">Optional limit on number of items</param>
        /// <returns>Collection of list items</returns>
        Task<IListItemsCollectionPage> GetListItems(
            GraphServiceClient graphClient, 
            string siteId, 
            string listId, 
            string filter = null, 
            string orderBy = null, 
            int? top = null);
        
        /// <summary>
        /// Processes a SharePoint list item to extract fields
        /// </summary>
        /// <param name="item">SharePoint list item</param>
        /// <returns>Dictionary of processed fields</returns>
        IDictionary<string, object> ProcessListItem(ListItem item);
    }
}
