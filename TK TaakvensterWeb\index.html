<!DOCTYPE html>
<html lang="en" class="h-full">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON></title>
    <script src="https://appsforoffice.microsoft.com/lib/1/hosted/office.js"></script>
    <link href="https://res-1.cdn.office.net/files/fabric-cdn-prod_20230815.002/office-ui-fabric-core/11.0.0/css/fabric.min.css" rel="stylesheet">
    <link href="https://res-1.cdn.office.net/files/fabric-cdn-prod_20230815.002/office-ui-fabric-js/1.5.0/css/fabric.min.css" rel="stylesheet">
    <link href="https://res-1.cdn.office.net/files/fabric-cdn-prod_20230815.002/office-ui-fabric-js/1.5.0/css/fabric.components.min.css" rel="stylesheet">
    <link href="Content/debugpanel.css" rel="stylesheet" type="text/css" />
    <link href="Content/messagebanner-custom.css" rel="stylesheet" type="text/css" />
    <link href="Content/FabricUI/MessageBanner.css" rel="stylesheet" type="text/css" />
    <!-- Fluent UI Toast Styles -->
    <link href="/src/assets/fluent-toast.css" rel="stylesheet" type="text/css" />
    <!-- Tailwind CSS from CDN -->
    <script src="https://cdn.tailwindcss.com"></script>
    <!-- Tailwind CSS from CDN -->
    <!-- <style>
        .message-bar-list { all: revert; }
        fluent-message-bar::after, fluent-message-bar:before {
            all: revert;
         }
         fluent-message-bar {
            border-width:1px;
         } -->
    <!-- </style> -->
</head>
<body class="ms-Fabric m-0 h-full flex flex-col" dir="ltr">
    <div id="app"></div>
    <script type="module" src="/src/main.js"></script>
</body>
</html>
