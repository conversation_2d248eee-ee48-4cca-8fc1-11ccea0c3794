/* Fluent UI Toast Styles */
/* Based on Fluent UI design tokens and patterns */

.fluent-toast {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 12px 16px;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.12), 0 0 2px rgba(0, 0, 0, 0.08);
  font-family: 'Segoe UI', system-ui, sans-serif;
  font-size: 14px;
  line-height: 1.4;
  min-width: 280px;
  max-width: 400px;
  position: relative;
  background: var(--colorNeutralBackground1);
  border: 1px solid var(--colorNeutralStroke2);
}

.fluent-toast-content {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  flex: 1;
}

.fluent-toast-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  font-size: 12px;
  font-weight: 600;
  flex-shrink: 0;
  margin-top: 1px; /* Align with text baseline */
}

.fluent-toast-message {
  flex: 1;
  color: var(--colorNeutralForeground1);
  word-wrap: break-word;
}

.fluent-toast-dismiss {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  background: transparent;
  border-radius: 4px;
  cursor: pointer;
  color: var(--colorNeutralForeground3);
  flex-shrink: 0;
  transition: all 0.1s ease;
}

.fluent-toast-dismiss:hover {
  background: var(--colorNeutralBackground1Hover);
  color: var(--colorNeutralForeground2);
}

.fluent-toast-dismiss:active {
  background: var(--colorNeutralBackground1Pressed);
}

.fluent-toast-dismiss:focus-visible {
  outline: 2px solid var(--colorStrokeFocus2);
  outline-offset: 1px;
}

/* Success Toast */
.fluent-toast-success {
  background: var(--colorPaletteGreenBackground2);
  border-color: var(--colorPaletteGreenBorder2);
}

.fluent-toast-success .fluent-toast-message {
  color: var(--colorPaletteGreenForeground2);
}

.fluent-icon-success {
  color: var(--colorPaletteGreenForeground2);
}

/* Error Toast */
.fluent-toast-error {
  background: var(--colorPaletteRedBackground2);
  border-color: var(--colorPaletteRedBorder2);
}

.fluent-toast-error .fluent-toast-message {
  color: var(--colorPaletteRedForeground2);
}

.fluent-icon-error {
  color: var(--colorPaletteRedForeground2);
}

/* Warning Toast */
.fluent-toast-warning {
  background: var(--colorPaletteYellowBackground2);
  border-color: var(--colorPaletteYellowBorder2);
}

.fluent-toast-warning .fluent-toast-message {
  color: var(--colorPaletteYellowForeground2);
}

.fluent-icon-warning {
  color: var(--colorPaletteYellowForeground2);
}

/* Info Toast */
.fluent-toast-info {
  background: var(--colorPaletteBlueBa‌ckground2);
  border-color: var(--colorPaletteBlueBorder2);
}

.fluent-toast-info .fluent-toast-message {
  color: var(--colorPaletteBlueForeground2);
}

.fluent-icon-info {
  color: var(--colorPaletteBlueForeground2);
}

/* Fallback colors for when Fluent UI tokens aren't available */
@supports not (color: var(--colorNeutralBackground1)) {
  .fluent-toast {
    background: #ffffff;
    border-color: #e1e1e1;
    color: #323130;
  }
  
  .fluent-toast-success {
    background: #f3f9f1;
    border-color: #9fd89f;
  }
  
  .fluent-toast-success .fluent-toast-message,
  .fluent-icon-success {
    color: #0e700e;
  }
  
  .fluent-toast-error {
    background: #fdf3f4;
    border-color: #eeacb2;
  }
  
  .fluent-toast-error .fluent-toast-message,
  .fluent-icon-error {
    color: #c50e1f;
  }
  
  .fluent-toast-warning {
    background: #fffef5;
    border-color: #f9e2ae;
  }
  
  .fluent-toast-warning .fluent-toast-message,
  .fluent-icon-warning {
    color: #8a6c00;
  }
  
  .fluent-toast-info {
    background: #f5f9ff;
    border-color: #a9c8f5;
  }
  
  .fluent-toast-info .fluent-toast-message,
  .fluent-icon-info {
    color: #0e4775;
  }
  
  .fluent-toast-dismiss:hover {
    background: #f3f2f1;
  }
  
  .fluent-toast-dismiss:active {
    background: #edebe9;
  }
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .fluent-toast {
    min-width: 240px;
    max-width: calc(100vw - 32px);
    font-size: 13px;
  }
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .fluent-toast {
    border-width: 2px;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .fluent-toast-dismiss {
    transition: none;
  }
}
