<!--
  TEMPLATE FOR NEW STEPS

  Copy this file and rename it to YourStepName.vue
  Follow the TODO comments to customize it for your needs

  This template follows the established pattern that all existing steps use.
-->

<template>
  <div id="panel-X">
    <!-- TODO: Change panel ID -->
    <StepHeader :title="stepTitle" :subTitle="stepDescription" />
    <div class="step-container">
      <!-- TODO: Add any custom content before the list here -->

      <FlowbiteList :items="stepOptions" v-model="selectedIndex" @item-click="handleItemClick">
        <template #item="{ item }">
          <!-- TODO: Customize how items are displayed -->
          {{ item.label || item.naam }}
        </template>
      </FlowbiteList>

      <!-- TODO: Add any custom content after the list here -->
      <!-- Examples:
      <div class="custom-section">
        <input v-model="customField" placeholder="Enter custom text..." />
        <button @click="handleCustomAction">Custom Action</button>
      </div>
      -->
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useOffice } from '../../../office'
import { useErrorHandler } from '../../../shared'
import { getStepBehaviorConfig } from '../../../../config/appConfig'
import StepHeader from './StepHeader.vue'
import FlowbiteList from '../../../shared/components/common/FlowbiteList.vue'

// TODO: Update these step details
const stepTitle = 'Your Step Title'
const stepDescription = 'Describe what this step does for the user.'

// Standard event system - don't change this
const emit = defineEmits({
  'step-ready': null,
  'step-complete': null,
  'step-previous': null,
})

const { insertTextInContentControl } = useOffice()
const { withErrorHandling } = useErrorHandler()
const selectedIndex = ref(null)

// TODO: Replace with your step's data
const stepOptions = [
  {
    label: 'Option 1',
    value: 'Value that goes in document',
    controlTag: 'txtYourContentControl',
  },
  {
    label: 'Option 2',
    value: 'Another value',
    controlTag: 'txtYourContentControl',
  },
  // Add more options as needed
]

// TODO: Add any additional reactive data here
// const customField = ref('')

function handleItemClick(event) {
  processSelection(event.index)
}

async function processSelection(index) {
  try {
    // Validation
    if (index === null || index === undefined || index < 0 || index >= stepOptions.length) {
      await withErrorHandling(
        () => Promise.reject(new Error('Invalid selection')),
        'validating selection',
        { userMessage: 'Selecteer eerst een optie voordat u verder gaat.' },
      )
      return
    }

    selectedIndex.value = index
    const item = stepOptions[index]
    const itemLabel = item.label || item.naam

    // Insert into document
    await withErrorHandling(
      () => insertTextInContentControl(item.controlTag, item.value),
      `adding ${itemLabel}`,
      {
        userMessage: `Failed to add ${itemLabel}. Please try again.`,
        successMessage: `${itemLabel} is toegevoegd aan het document.`,
      },
    )

    // TODO: Add any custom logic after successful insertion here

    // Complete the step (with small delay for user feedback)
    setTimeout(() => {
      emit('step-complete')
    }, getStepBehaviorConfig().completionDelay)
  } catch (error) {
    // Reset selection on error
    selectedIndex.value = null
    console.error('Error processing selection:', error)
  }
}

// TODO: Add any custom functions here
// async function handleCustomAction() {
//   await withErrorHandling(
//     async () => {
//       // Your custom logic here
//     },
//     'performing custom action',
//     {
//       successMessage: 'Custom action completed!',
//       errorMessage: 'Custom action failed.'
//     }
//   )
// }

// Initialize step - don't change this
onMounted(() => {
  emit('step-ready')
})
</script>

<style scoped>
.step-container {
  margin-top: 1rem !important;
  width: 100%;
}

/* TODO: Add any custom styling here */
</style>
