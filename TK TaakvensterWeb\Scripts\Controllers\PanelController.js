/**
 * PanelController.js
 * Manages the multi-panel UI system with toggle functionality for the Word add-in
 */

const PanelController = (function() {
    // Private variables
    let _activePanelNumber = 1;
    
    // Private functions
    function _initialize() {
        console.log('Initializing panel controller...');
        
        // Show the first panel by default
        _showPanel(1);
        
        // Set up event handlers
        _setupEventHandlers();
        
        // Set up keyboard shortcuts
        _setupKeyboardListener();
        
        console.log('Panel controller initialized');
    }
    
    function _setupEventHandlers() {
        // Connect panel toggle buttons
        $('.toggle-button').click(_handlePanelToggleClicked);
    }
    
    function _handlePanelToggleClicked(event) {
        // Prevent default link behavior
        event.preventDefault();
        
        // Get the panel number from the data attribute
        var panelNumber = $(this).data('panel');
        
        // Show the selected panel
        _showPanel(panelNumber);
    }
    
    function _showPanel(panelNumber) {
        // Store active panel
        _activePanelNumber = panelNumber;
        
        // Hide all panels
        $('.panel').addClass('hidden');
        
        // Show the selected panel
        $('#panel-' + panelNumber).removeClass('hidden');
        
        // Update toggle button state
        _updateToggleButtonState(panelNumber);
        
        // Log panel change
        console.log(`Panel changed to ${panelNumber}`);
    }
    
    function _updateToggleButtonState(activePanelNumber) {
        // Remove active class from all toggle buttons
        $('.toggle-button').removeClass('active bg-blue-600 text-white').addClass('text-gray-700');
        
        // Add active class to the selected toggle button
        $('.toggle-button[data-panel="' + activePanelNumber + '"]').addClass('active bg-blue-600 text-white').removeClass('text-gray-700');
    }
    
    function _setupKeyboardListener() {
        $(document).keydown(function(event) {
            // Check if the key pressed is 'D' (keyCode 68)
            if (event.keyCode === 68) {
                // Toggle the visibility of the panel toggle
                $('#panel-toggle').toggleClass('hidden');
                
                console.log('Panel toggle visibility toggled with D key');
            }
            
            // Check if the key pressed is '1', '2', or '3' (keyCodes 49, 50, 51)
            if (event.keyCode >= 49 && event.keyCode <= 51) {
                // Calculate the panel number (1, 2, or 3)
                var panelNumber = event.keyCode - 48;
                
                // Show the selected panel
                _showPanel(panelNumber);
            }
        });
    }
    
    // Public API
    return {
        initialize: _initialize,
        showPanel: _showPanel,
        updateToggleButtonState: _updateToggleButtonState,
        getActivePanel: function() { return _activePanelNumber; }
    };
})();
