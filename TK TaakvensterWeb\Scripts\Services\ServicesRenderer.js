const ServicesRenderer = (function() {
    // Private variables
    const _containerSelector = Config.selectors.services.container;
    let _isInitialized = false;
    
    function _initialize() {
        if (_isInitialized) {
            console.log('Services renderer already initialized');
            return;
        }
        
        console.log('Initializing services renderer...');
        
        // Find the container
        const $container = $(_containerSelector);
        if ($container.length === 0) {
            console.error('Services container not found');
            return;
        }
        
        _isInitialized = true;
        console.log('Services renderer initialized');
    }
    
    function _createServiceItem(service) {
        // Get the template from the HTML document
        const $template = $('#' + Config.templates.services.serviceItem);
        if ($template.length === 0) {
            console.error('Service item template not found');
            return '';
        }
        
        // Clone the template content
        const templateContent = $template[0].content.cloneNode(true);
        const $serviceItem = $(templateContent).find(Config.selectors.services.serviceItem);
        
        // Set data attributes
        // TODO: We have no id and url, so why do this?
        $serviceItem.attr('data-service-id', service.id || '');
        $serviceItem.attr('data-service-url', service.url || '#');
        
        // Set text content
        $serviceItem.find(Config.selectors.services.primaryText).text(service.title || 'Untitled Service');
        
        return templateContent;
    }
    
    function _createErrorState(message) {
        // Get the template
        const $template = $('#' + Config.templates.services.errorState);
        if ($template.length === 0) {
            console.error('Error state template not found');
            return `<div class="error-fallback">${message || 'An error occurred'}</div>`;
        }
        
        // Clone the template content
        const templateContent = $template[0].content.cloneNode(true);
        
        // Set the error message if provided
        if (message) {
            $(templateContent).find('.error-message').text(message);
        }
        
        return templateContent;
    }
    
    function _setupUIComponents(container) {
        // Make sure container exists
        if (!container) {
            console.error('Container is null or undefined');
            return;
        }
        
        // Set up service item click handlers
        $(container).find(Config.selectors.services.serviceItem).each(function() {
            const $item = $(this);
            const $button = $item.find(Config.selectors.services.serviceButton);
            
            // Make the item clickable
            new fabric['ListItem']($item[0]);
            
            // Set up the button if it exists
            if ($button.length) {
                try {
                    new fabric['Button']($button[0]);
                } catch (error) {
                    console.warn("Could not initialize Button:", error);
                }
            }
        });
        
        // Spinner code removed
    }
    
    function renderServices(services) {
        // Make sure we're initialized
        if (!_isInitialized) {
            console.warn('Services renderer not initialized, initializing now...');
            _initialize();
        }
        
        // Find the container
        const $container = $(_containerSelector);
        if ($container.length === 0) {
            console.error('Services container not found');
            return;
        }
        
        // Clear the container
        $container.empty();
        
        // Check if we have services
        if (!services || services.length === 0) {
            console.log('No services to render, showing empty state');
            $container.html('<div class="empty-state">Geen diensten beschikbaar</div>');
            return;
        }
        
        // Create a document fragment to hold all the service items
        const fragment = document.createDocumentFragment();
        
        // Add each service to the fragment
        services.forEach(service => {
            const serviceItem = _createServiceItem(service);
            fragment.appendChild(serviceItem);
        });
        
        // Add the fragment to the container
        $container.append(fragment);
        
        // Set up UI components
        _setupUIComponents($container[0]);
        
        console.log(`Rendered ${services.length} services`);
    }
    
    function showError(message) {
        const $container = $(_containerSelector);
        if ($container.length === 0) return;
        
        // Log the error to the error handler
        OfficeUtils.Errors.handleError(new Error(message || 'Unknown error'), 'rendering services');
        
        // Show error notification
        OfficeUtils.UI.showNotification(
            'Fout bij Weergeven', 
            message || 'Er is een fout opgetreden bij het weergeven van diensten.'
        );
        
        // Clear container and show error
        $container.empty();
        $container.append(_createErrorState(message));
        
        // Initialize retry button
        _setupUIComponents($container[0]);
    }
    
    // Public API
    return {
        initialize: _initialize,
        renderServices: renderServices,
        showError: showError
    };
})();
