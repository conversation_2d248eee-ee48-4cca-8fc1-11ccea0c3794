using Microsoft.Graph;
using System.Threading.Tasks;

namespace TK_TaakvensterWeb
{
    /// <summary>
    /// Interface for Graph authentication services
    /// </summary>
    public interface IGraphAuthenticationService
    {
        /// <summary>
        /// Gets a Graph client using On-Behalf-Of flow with the provided user token
        /// </summary>
        /// <param name="userToken">User's access token</param>
        /// <returns>Authenticated GraphServiceClient or null if authentication fails</returns>
        Task<GraphServiceClient> GetGraphClientWithOBO(string userToken);
        
        /// <summary>
        /// Gets the last error that occurred during authentication
        /// </summary>
        string GetLastError();
    }
}
