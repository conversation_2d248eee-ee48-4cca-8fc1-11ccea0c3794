// OfficeUtils - Reusable utilities for Office Add-ins
// 
// This file serves as a facade for the modular utilities:
// - UI: User interface related functions
// - Features: Feature detection and compatibility
// - Debug: Debugging tools and utilities
// - Errors: Error handling and reporting
// - Word: Word-specific document manipulation
const OfficeUtils = (function() {
    // Public API - Facade that re-exports all modules
    return {
        // UI Module
        UI: window.UIUtils,
        
        // Feature Detection Module
        Features: window.FeatureDetection,
        
        // Debug Module
        Debug: window.DebugUtils,
        
        // Error Handling Module
        Errors: window.ErrorHandler,
        
        // Word Document Module
        Word: window.WordDocumentUtils
    };
})();

// Make it globally accessible
window.OfficeUtils = OfficeUtils;
