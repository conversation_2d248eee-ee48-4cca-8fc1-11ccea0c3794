﻿/*Copyright (c) Microsoft. All rights reserved. Licensed under the MIT license.*/
/**
 * MessageBanner styles
 *
 */

.MessageBanner {
    color: #333333;
    font-family: 'Segoe UI Regular WestEuropean', 'Segoe UI', Tahoma, Arial, sans-serif;
    font-size: 12px;
    font-weight: normal;
    position: relative;
    border-bottom: 1px solid #767676;
    background-color: #eff6fc;
    min-width: 320px;
    width: 98%;
    height: 52px;
    text-align: center;
    overflow: hidden;
    animation-name: ms-fadeIn, ms-slideDownIn20;
    animation-duration: 0.367s;
    animation-timing-function: cubic-bezier(0.1, 0.25, 0.75, 0.9);
    animation-fill-mode: both;
}

.MessageBanner .ms-Icon {
    font-size: 16px;
}

.MessageBanner.hide {
    animation-name: ms-fadeOut, ms-slideUpOut20;
    animation-timing-function: cubic-bezier(0.1, 0.25, 0.75, 0.9);
    animation-fill-mode: both;
}

.MessageBanner.is-hidden {
    display: none;
}

.MessageBanner-expand,
.MessageBanner-close {
    height: 52px;
    width: 40px;
    cursor: pointer;
    border: none;
    background-color: transparent;
}

.MessageBanner-expand:focus,
.MessageBanner-close:focus {
    outline: 1px solid transparent;
}

.MessageBanner-close {
    position: absolute;
    right: 0;
    top: 0;
    line-height: 52px;
    color: #666666;
}

.MessageBanner-text {
    display: inline-block;
    padding: 18px 0;
    margin-left: 0;
    max-width: 770px;
    overflow: hidden;
    text-align: left;
}

.MessageBanner-expand {
    display: none;
    vertical-align: top;
}

.MessageBanner-expand.is-visible {
    display: inline-block;
}

.MessageBanner-action {
    display: inline-block;
    vertical-align: top;
    margin-top: 10px;
    margin-left: 10px;
    padding-right: 36px;
}

.MessageBanner-action .Button {
    color: #ffffff;
}

.MessageBanner-clipper {
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    display: inline-block;
    vertical-align: top;
}

.MessageBanner.is-expanded {
    height: auto;
}

.MessageBanner.is-expanded .MessageBanner-clipper {
    white-space: normal;
}

@media screen and (max-width: 479px) {
    .MessageBanner-expand {
        display: inline-block;
    }

    .MessageBanner-action {
        margin: 0;
        display: block;
        text-align: right;
        padding: 0 10px 10px 0;
    }

    .MessageBanner-text {
        margin-left: -25px;
        padding: 18px 0 10px 0;
        min-width: 240px;
    }

    .MessageBanner-expand {
        padding: 0;
        margin-left: -5px;
        width: 20px;
    }

    .MessageBanner-expand .ms-Icon {
        color: #0078d7;
    }
}
