/**
 * Configuration settings for notifications (MessageBars and Toasts)
 * Adjust these values to change behavior across the application
 */

export const notificationSettings = {
  messageBars: {
    maxVisible: 3, // Maximum number of MessageBars to show at once
    defaultDuration: 5000, // Default duration in milliseconds before auto-dismissal (0 for persistent)
    maxDuration: 10000 // Maximum duration for persistent critical messages
  },
  toasts: {
    maxVisible: 3, // Maximum number of toasts to show at once
    defaultDuration: 3000, // Default duration in milliseconds before auto-dismissal
    maxDuration: 8000 // Maximum duration for longer toast messages
  }
};
