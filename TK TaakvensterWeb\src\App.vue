<template>
  <div id="app" class="ms-Fabric h-screen flex flex-col">
    <template v-if="!isWordCompatible">
      <MessageBarList />

      <StepManager :steps="stepConfig" />
      <Footer title="Tweede Kamer" />

      <TestPanelControls v-if="isDevelopment" v-model:show-panel="showMessageBarTest" />
    </template>
    <IncompatibleWordVersionPanel v-else />
  </div>
</template>

<script setup>
import { ref } from 'vue'

import { Footer } from './features/shared'
import { StepManager, useStepConfig } from './features/steps'
import { IncompatibleWordVersionPanel, useWordCompatibility } from './features/office'
import { MessageBarList } from './features/notifications'
import { TestPanelControls } from './features/development'

import { useEnvironment } from './features/shared'

// Configure Word feature detection
const { isWordCompatible } = useWordCompatibility()

// Environment detection
const { isDevelopment } = useEnvironment()

// Development test panel flag
const showMessageBarTest = ref(false)

// Step configuration
const stepConfig = useStepConfig()
</script>
