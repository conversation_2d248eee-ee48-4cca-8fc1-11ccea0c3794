<template>
  <div v-show="isVisible" class="panel-toggle-overlay" role="tablist" aria-label="Navigation panel">
    <button
      v-for="(step, index) in steps"
      :key="index"
      :id="`tab-${index}`"
      :aria-controls="`panel-${index + 1}`"
      :aria-selected="currentStep === index"
      :tabindex="currentStep === index ? 0 : -1"
      role="tab"
      class="toggle-button"
      :class="{ active: currentStep === index }"
      @click="selectStep(index)"
    >
      {{ index + 1 }}
    </button>
  </div>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useLogger } from '../../shared'
import { usePanelKeyboardShortcuts } from '../composables/usePanelKeyboardShortcuts'

const { debug } = useLogger()

// Component interface
const props = defineProps({
  currentStep: {
    type: Number,
    required: true,
    validator: (value) => value >= 0 && Number.isInteger(value),
  },
  steps: {
    type: Array,
    required: true,
    validator: (value) => Array.isArray(value) && value.length > 0,
  },
})

const emit = defineEmits(['update:currentStep'])
const isVisible = ref(false)
const stepCount = computed(() => props.steps.length)

function togglePanel() {
  isVisible.value = !isVisible.value
  debug(`Panel ${isVisible.value ? 'opened' : 'closed'}`)
}

function selectStep(stepIndex) {
  if (stepIndex >= 0 && stepIndex < props.steps.length) {
    emit('update:currentStep', stepIndex)
    debug(`Step ${stepIndex + 1} selected`)
  }
}

// Enable keyboard shortcuts to open the panel and toggle steps
usePanelKeyboardShortcuts({
  isVisible,
  stepCount,
  onToggle: togglePanel,
  onSelectStep: selectStep,
})
</script>

<style scoped>
.panel-toggle-overlay {
  position: absolute;
  top: 10px;
  right: 10px;
  z-index: 10;
  background-color: #f3f4f6;
  border-radius: 0.25rem;
  padding: 0.25rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  display: flex;
}

.toggle-button {
  display: inline-block;
  width: 1.5rem;
  height: 1.5rem;
  line-height: 1.5rem;
  text-align: center;
  margin: 0 0.125rem;
  border-radius: 0.25rem;
  color: #4b5563;
  background: none;
  border: none;
  cursor: pointer;
  transition: all 0.2s;
}

.toggle-button:hover {
  background-color: #2563eb;
  color: white;
}

.toggle-button.active {
  background-color: #2563eb;
  color: white;
}

.toggle-button:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.3);
}
</style>
