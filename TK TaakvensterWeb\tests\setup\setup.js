import { vi } from 'vitest'
import { createPinia } from 'pinia'

// Mock window.Word for Office API
global.Word = {
  run: vi.fn(),
}

// Mock console methods to avoid cluttering test output
vi.spyOn(console, 'error').mockImplementation(() => {})
vi.spyOn(console, 'warn').mockImplementation(() => {})

// Mock FluentUI components globally to prevent dependency issues
vi.mock('@fluentui/web-components', () => ({
  provideFluentDesignSystem: vi.fn(),
  fluentButton: vi.fn(),
  fluentMessageBar: vi.fn(),
  MessageBarType: {
    info: 'info',
    warning: 'warning',
    error: 'error',
    success: 'success',
  },
}))

// Mock FluentUI wrappers
vi.mock('../src/features/office/wrappers/fluent-ui/FluentMessageBarWrapper.vue', () => ({
  default: {
    name: 'FluentMessageBarWrapper',
    props: ['type', 'message'],
    template: '<div class="mock-message-bar">{{ message }}</div>',
  },
}))

vi.mock('../src/features/office/wrappers/fluent-ui/FluentButtonWrapper.vue', () => ({
  default: {
    name: 'FluentButtonWrapper',
    props: ['text'],
    template: '<button class="mock-button">{{ text }}</button>',
  },
}))

// Create a global Pinia instance
export const pinia = createPinia()
