/**
 * Test data for ProfielAfzender step testing
 * Contains realistic Dutch government profile data for various test scenarios
 */

export const validProfiles = [
  {
    FirstName: "<PERSON>",
    LastName: "<PERSON> <PERSON><PERSON>",
    Role: "Sjabloon-ontwikkelaar",
    EmailAddress: "j.de<PERSON><PERSON>@tweedekamer.nl",
    CommissionId: "infrastructuur-waterstaat"
  },
  {
    FirstName: "<PERSON>",
    LastName: "van der Berg",
    Role: "Commissievoorzitter",
    EmailAddress: "m.<PERSON>@tweedekamer.nl",
    CommissionId: "binnenlandse-zaken"
  },
  {
    FirstName: "<PERSON>",
    LastName: "<PERSON><PERSON>",
    Role: "Commissiel<PERSON>",
    EmailAddress: "<EMAIL>",
    CommissionId: "financien"
  },
  {
    FirstName: "<PERSON>",
    LastName: "Hend<PERSON><PERSON>",
    Role: "Commissiesecretaris",
    EmailAddress: "<EMAIL>",
    CommissionId: "justitie-veiligheid"
  },
  {
    FirstName: "<PERSON>",
    LastName: "<PERSON>",
    Role: "Beleidsmedewerker",
    EmailAddress: "r.van<PERSON><PERSON>@tweedekamer.nl",
    CommissionId: "economische-zaken"
  }
]

export const partialProfiles = [
  {
    // Missing Role
    FirstName: "Linda",
    LastName: "Bakker",
    EmailAddress: "<EMAIL>",
    CommissionId: "onderwijs-cultuur"
  },
  {
    // Missing EmailAddress
    FirstName: "Thomas",
    LastName: "de Wit",
    Role: "Commissielid",
    CommissionId: "volksgezondheid"
  },
  {
    // Missing CommissionId
    FirstName: "Emma",
    LastName: "Visser",
    Role: "Beleidsadviseur",
    EmailAddress: "<EMAIL>"
  },
  {
    // Only name (minimal valid profile)
    FirstName: "Mark",
    LastName: "van Leeuwen"
  }
]

export const invalidProfiles = [
  {
    // Missing FirstName - should be skipped
    FirstName: "",
    LastName: "Invalid",
    Role: "Test Role",
    EmailAddress: "<EMAIL>",
    CommissionId: "test-commission"
  },
  {
    // Missing LastName - should be skipped
    FirstName: "Invalid",
    LastName: null,
    Role: "Test Role",
    EmailAddress: "<EMAIL>",
    CommissionId: "test-commission"
  },
  {
    // Missing both names - should be skipped
    FirstName: undefined,
    LastName: "",
    Role: "Test Role",
    EmailAddress: "<EMAIL>",
    CommissionId: "test-commission"
  },
  {
    // Whitespace only names - should be skipped
    FirstName: "   ",
    LastName: "\t\n",
    Role: "Test Role",
    EmailAddress: "<EMAIL>",
    CommissionId: "test-commission"
  }
]

export const profilesWithInvalidCommissions = [
  {
    FirstName: "Valid",
    LastName: "NoCommission",
    Role: "Test Role",
    EmailAddress: "<EMAIL>",
    CommissionId: "non-existent-commission"
  },
  {
    FirstName: "Another",
    LastName: "InvalidCommission",
    Role: "Test Role",
    EmailAddress: "<EMAIL>",
    CommissionId: "also-non-existent"
  }
]

/**
 * Expected formatted output for testing content control population
 */
export const expectedFormattedOutputs = {
  completeProfile: {
    profile: validProfiles[0],
    expectedPersonalData: "Jan de Bont\nSjabloon-ontwikkelaar\<EMAIL>"
  },
  profileWithoutRole: {
    profile: {
      FirstName: "Test",
      LastName: "User",
      EmailAddress: "<EMAIL>"
    },
    expectedPersonalData: "Test User\<EMAIL>"
  },
  profileWithoutEmail: {
    profile: {
      FirstName: "Test",
      LastName: "User",
      Role: "Test Role"
    },
    expectedPersonalData: "Test User\nTest Role"
  },
  minimalProfile: {
    profile: {
      FirstName: "Test",
      LastName: "User"
    },
    expectedPersonalData: "Test User"
  }
}

/**
 * Test scenarios for different validation cases
 */
export const testScenarios = {
  validSelection: {
    profileIndex: 0,
    expectedProfile: validProfiles[0],
    shouldSucceed: true
  },
  invalidIndex: {
    profileIndex: -1,
    expectedProfile: null,
    shouldSucceed: false,
    expectedError: "Invalid selection"
  },
  outOfBounds: {
    profileIndex: 999,
    expectedProfile: null,
    shouldSucceed: false,
    expectedError: "Invalid selection"
  },
  nullIndex: {
    profileIndex: null,
    expectedProfile: null,
    shouldSucceed: false,
    expectedError: "Invalid selection"
  }
}
