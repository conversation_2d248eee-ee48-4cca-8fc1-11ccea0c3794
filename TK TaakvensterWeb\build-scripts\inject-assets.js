import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

console.log('🚀 Asset injection script started');

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

/**
 * Generates index.html from template and injects Vite-built assets
 * This script reads the Vite manifest and generates index.html from index.template.html
 */
function injectAssets(mode = 'production') {
  const distPath = path.join(__dirname, '..', 'dist');
  const templatePath = path.join(__dirname, '..', 'index.template.html');
  const indexPath = path.join(__dirname, '..', 'index.html');
  const manifestPath = path.join(distPath, '.vite', 'manifest.json');

  console.log('🔍 Asset injection starting...');
  console.log('   Dist path:', distPath);
  console.log('   Index path:', indexPath);
  console.log('   Manifest path:', manifestPath);

  // Check if dist folder exists
  if (!fs.existsSync(distPath)) {
    console.log('⚠️  Dist folder not found. Skipping asset injection.');
    return;
  }
  console.log('✅ Dist folder found');

  // Check if template exists
  if (!fs.existsSync(templatePath)) {
    console.log('❌ Template file not found:', templatePath);
    return;
  }
  console.log('✅ Template file found');

  // Read the Vite manifest (only in production mode)
  let manifest = {};
  if (mode === 'production') {
    if (fs.existsSync(manifestPath)) {
      try {
        const manifestContent = fs.readFileSync(manifestPath, 'utf8');
        manifest = JSON.parse(manifestContent);
        console.log('✅ Manifest file found and parsed');
      } catch (error) {
        console.error('❌ Error reading manifest:', error.message);
        console.log('🔍 Falling back to scanning dist folder...');
        manifest = scanDistFolder(distPath);
      }
    } else {
      console.log('⚠️  Manifest file not found. Scanning dist folder...');
      manifest = scanDistFolder(distPath);
    }
  } else {
    console.log('📝 Development mode: Skipping manifest, using dev script');
  }

  // Read template file
  let templateContent;
  try {
    templateContent = fs.readFileSync(templatePath, 'utf8');
  } catch (error) {
    console.error('❌ Error reading template:', error.message);
    return;
  }

  // Generate CSS and JS asset tags
  let cssAssets = '';
  let jsAssets = '';
  
  if (mode === 'development') {
    // In development mode, Vite handles CSS via JS imports
    cssAssets = '';
    jsAssets = '<script type="module" src="/src/main.js"></script>';
  } else {
    // In production mode, use built assets
    cssAssets = generateCssAssets(manifest);
    jsAssets = generateJsAssets(manifest);
  }

  // Replace placeholders in template
  let htmlContent = templateContent
    .replace('{{VITE_CSS_ASSETS}}', cssAssets)
    .replace('{{VITE_JS_ASSETS}}', jsAssets);

  // Write generated HTML
  try {
    fs.writeFileSync(indexPath, htmlContent, 'utf8');
    console.log(`✅ Successfully generated index.html from template (${mode} mode)`);
    console.log('📋 CSS assets:', cssAssets ? 'injected' : 'none');
    console.log('📋 JS assets:', jsAssets ? 'injected' : 'none');
  } catch (error) {
    console.error('❌ Error writing index.html:', error.message);
  }
}

/**
 * Scans dist folder for assets when manifest is not available
 */
function scanDistFolder(distPath) {
  const manifest = {};
  
  try {
    const assetsPath = path.join(distPath, 'assets');
    if (fs.existsSync(assetsPath)) {
      const files = fs.readdirSync(assetsPath);
      
      files.forEach(file => {
        const filePath = `dist/assets/${file}`;
        if (file.endsWith('.js')) {
          manifest[`main-${file}`] = { file: filePath, isEntry: true };
        } else if (file.endsWith('.css')) {
          manifest[`main-${file}`] = { file: filePath, css: [filePath] };
        }
      });
    }
  } catch (error) {
    console.error('❌ Error scanning dist folder:', error.message);
  }
  
  return manifest;
}

/**
 * Generates CSS asset tags from manifest
 */
function generateCssAssets(manifest) {
  const cssFiles = [];
  
  Object.values(manifest).forEach(entry => {
    if (entry.css) {
      entry.css.forEach(cssFile => {
        cssFiles.push(`  <link rel="stylesheet" href="${cssFile}" />`);
      });
    }
  });
  
  return cssFiles.length > 0 ? cssFiles.join('\n') : '';
}

/**
 * Generates JS asset tags from manifest
 */
function generateJsAssets(manifest) {
  const jsFiles = [];
  
  Object.values(manifest).forEach(entry => {
    if (entry.isEntry && entry.file && entry.file.endsWith('.js')) {
      jsFiles.push(`  <script type="module" src="${entry.file}"></script>`);
    }
  });
  
  return jsFiles.length > 0 ? jsFiles.join('\n') : '';
}

// Run the script
if (import.meta.url === `file://${process.argv[1]}` || import.meta.url.endsWith('inject-assets.js')) {
  const mode = process.argv[2] || 'production';
  console.log(`🔧 Running asset injection in ${mode} mode...`);
  injectAssets(mode);
}

export { injectAssets };
