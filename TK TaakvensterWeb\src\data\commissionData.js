/**
 * Static commission data for ProfielAfzender step
 * 
 * This file contains realistic Dutch government commission data.
 * In the future, this can be replaced with API calls while
 * maintaining the same data structure.
 */

/**
 * Commission data structure:
 * - id: string (required) - Unique identifier for linking with profiles
 * - CommissionName: string (required) - Full name of the commission
 * - POBoxNumber: string (optional) - Post office box number
 * - ZipCode: string (optional) - Postal code
 * - City: string (optional) - City name
 * - StreetName: string (optional) - Street name
 * - HouseNumber: string (optional) - House number
 */
export const commissionData = [
  {
    id: "infrastructuur-waterstaat",
    CommissionName: "Vaste commissie voor Infrastructuur en Waterstaat",
    POBoxNumber: "Postbus 20018",
    ZipCode: "2500 EA",
    City: "Den Haag",
    StreetName: "Prinses Irenepad",
    HouseNumber: "1"
  },
  {
    id: "binnenlandse-zaken",
    CommissionName: "Vaste commissie voor Binnenlandse Zaken",
    POBoxNumber: "Postbus 20018",
    ZipCode: "2500 EA",
    City: "Den Haag",
    StreetName: "Binnenhof",
    HouseNumber: "4"
  },
  {
    id: "financien",
    CommissionName: "Vaste commissie voor Financiën",
    POBoxNumber: "Postbus 20018",
    ZipCode: "2500 EA",
    City: "Den Haag",
    StreetName: "Lange Voorhout",
    HouseNumber: "8"
  },
  {
    id: "justitie-veiligheid",
    CommissionName: "Vaste commissie voor Justitie en Veiligheid",
    POBoxNumber: "Postbus 20018",
    ZipCode: "2500 EA",
    City: "Den Haag",
    StreetName: "Korte Voorhout",
    HouseNumber: "3"
  },
  {
    id: "economische-zaken",
    CommissionName: "Vaste commissie voor Economische Zaken en Klimaat",
    POBoxNumber: "Postbus 20018",
    ZipCode: "2500 EA",
    City: "Den Haag",
    StreetName: "Bezuidenhoutseweg",
    HouseNumber: "73"
  },
  {
    id: "onderwijs-cultuur",
    CommissionName: "Vaste commissie voor Onderwijs, Cultuur en Wetenschap",
    POBoxNumber: "Postbus 20018",
    ZipCode: "2500 EA",
    City: "Den Haag",
    StreetName: "Rijnstraat",
    HouseNumber: "50"
  },
  {
    id: "volksgezondheid",
    CommissionName: "Vaste commissie voor Volksgezondheid, Welzijn en Sport",
    POBoxNumber: "Postbus 20018",
    ZipCode: "2500 EA",
    City: "Den Haag",
    StreetName: "Parnassusplein",
    HouseNumber: "5"
  },
  {
    id: "sociale-zaken",
    CommissionName: "Vaste commissie voor Sociale Zaken en Werkgelegenheid",
    POBoxNumber: "Postbus 20018",
    ZipCode: "2500 EA",
    City: "Den Haag",
    StreetName: "Anna van Hannoverstraat",
    HouseNumber: "4"
  },
  {
    id: "defensie",
    CommissionName: "Vaste commissie voor Defensie",
    POBoxNumber: "Postbus 20018",
    ZipCode: "2500 EA",
    City: "Den Haag",
    StreetName: "Kalvermarkt",
    HouseNumber: "32"
  },
  {
    id: "buitenlandse-zaken",
    CommissionName: "Vaste commissie voor Buitenlandse Zaken",
    POBoxNumber: "Postbus 20018",
    ZipCode: "2500 EA",
    City: "Den Haag",
    StreetName: "Lange Houtstraat",
    HouseNumber: "19"
  },
  {
    id: "landbouw-natuur",
    CommissionName: "Vaste commissie voor Landbouw, Natuur en Voedselkwaliteit",
    POBoxNumber: "Postbus 20018",
    ZipCode: "2500 EA",
    City: "Den Haag",
    StreetName: "Benoordenhoutseweg",
    HouseNumber: "22-24"
  },
  {
    id: "klimaat-energie",
    CommissionName: "Vaste commissie voor Klimaat en Energie",
    POBoxNumber: "Postbus 20018",
    ZipCode: "2500 EA",
    City: "Den Haag",
    StreetName: "Nieuwe Uitleg",
    HouseNumber: "1"
  },
  {
    id: "digitale-zaken",
    CommissionName: "Vaste commissie voor Digitale Zaken",
    POBoxNumber: "Postbus 20018",
    ZipCode: "2500 EA",
    City: "Den Haag",
    StreetName: "Schedeldoekshaven",
    HouseNumber: "200"
  },
  {
    id: "wonen-ruimtelijke-ordening",
    CommissionName: "Vaste commissie voor Wonen en Ruimtelijke Ordening",
    POBoxNumber: "Postbus 20018",
    ZipCode: "2500 EA",
    City: "Den Haag",
    StreetName: "Kneuterdijk",
    HouseNumber: "22"
  },
  {
    id: "europese-zaken",
    CommissionName: "Vaste commissie voor Europese Zaken",
    POBoxNumber: "Postbus 20018",
    ZipCode: "2500 EA",
    City: "Den Haag",
    StreetName: "Tournooiveld",
    HouseNumber: "2"
  },
  {
    id: "koninkrijksrelaties",
    CommissionName: "Vaste commissie voor Koninkrijksrelaties",
    POBoxNumber: "Postbus 20018",
    ZipCode: "2500 EA",
    City: "Den Haag",
    StreetName: "Schedeldoekshaven",
    HouseNumber: "100"
  }
]

/**
 * Test data for validation scenarios
 * These commissions have various data issues for testing purposes
 */
export const invalidCommissionData = [
  {
    // Missing CommissionName - should be skipped
    id: "invalid-1",
    CommissionName: "",
    POBoxNumber: "Postbus 12345",
    ZipCode: "1234 AB",
    City: "Test City"
  },
  {
    // Null CommissionName - should be skipped
    id: "invalid-2", 
    CommissionName: null,
    POBoxNumber: "Postbus 67890",
    ZipCode: "5678 CD",
    City: "Test City"
  },
  {
    // Valid commission with minimal data
    id: "minimal-valid",
    CommissionName: "Test Commissie met Minimale Gegevens"
    // No address data - should be accepted
  },
  {
    // Valid commission with partial address
    id: "partial-address",
    CommissionName: "Test Commissie met Gedeeltelijk Adres",
    StreetName: "Teststraat",
    HouseNumber: "123"
    // No POBox or ZipCode/City - should be accepted
  }
]

/**
 * Get all valid commissions (filters out invalid ones)
 * @returns {Array} Array of valid commission objects
 */
export function getValidCommissions() {
  return commissionData.filter(commission => {
    return commission.CommissionName && 
           commission.CommissionName.trim() !== ''
  })
}

/**
 * Get commission by ID
 * @param {string} commissionId - Commission ID to find
 * @returns {Object|null} Commission object or null if not found
 */
export function getCommissionById(commissionId) {
  if (!commissionId) return null
  return getValidCommissions().find(commission => commission.id === commissionId) || null
}

/**
 * Format commission address for txtAfzenderCommissie2
 * @param {Object} commission - Commission object
 * @returns {string} Formatted address string
 */
export function formatCommissionAddress(commission) {
  if (!commission) return ''
  
  const parts = []
  
  // POBox part: "Postbus 20018, 2500 EA Den Haag"
  if (commission.POBoxNumber) {
    const poBoxParts = [commission.POBoxNumber]
    if (commission.ZipCode && commission.City) {
      poBoxParts.push(`${commission.ZipCode} ${commission.City}`)
    } else if (commission.ZipCode) {
      poBoxParts.push(commission.ZipCode)
    } else if (commission.City) {
      poBoxParts.push(commission.City)
    }
    parts.push(poBoxParts.join(', '))
  }
  
  // Street part: "Prinses Irenepad 1, 2500 EA Den Haag"
  if (commission.StreetName) {
    const streetParts = []
    if (commission.HouseNumber) {
      streetParts.push(`${commission.StreetName} ${commission.HouseNumber}`)
    } else {
      streetParts.push(commission.StreetName)
    }
    
    if (commission.ZipCode && commission.City) {
      streetParts.push(`${commission.ZipCode} ${commission.City}`)
    } else if (commission.ZipCode) {
      streetParts.push(commission.ZipCode)
    } else if (commission.City) {
      streetParts.push(commission.City)
    }
    parts.push(streetParts.join(', '))
  }
  
  return parts.join(' | ')
}

/**
 * Get commission display name
 * @param {Object} commission - Commission object
 * @returns {string} Commission name for display
 */
export function getCommissionDisplayName(commission) {
  if (!commission || !commission.CommissionName) {
    return 'Onbekende commissie'
  }
  return commission.CommissionName
}
