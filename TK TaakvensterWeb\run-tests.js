const { execSync } = require('child_process');
const fs = require('fs');
const stripAnsi = require('strip-ansi');

// Run the tests and capture output
try {
  const output = execSync('npm test', { encoding: 'utf8' });
  
  // Strip ANSI escape sequences
  const cleanOutput = stripAnsi(output);
  
  // Write to file
  fs.writeFileSync('testoutput.txt', cleanOutput);
  
  // Also print to console
  console.log(cleanOutput);
  
  console.log('\nTest output has been saved to testoutput.txt');
} catch (error) {
  // Handle any errors
  const cleanOutput = stripAnsi(error.stdout);
  fs.writeFileSync('testoutput.txt', cleanOutput);
  console.log(cleanOutput);
  process.exit(1);
}
