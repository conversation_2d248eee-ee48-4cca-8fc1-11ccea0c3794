<template>
  <div id="panel-3">
    <StepHeader :title="stepTitle" :subTitle="stepDescription" />
    <div class="commissie-container">
      <FlowbiteList
        :items="commissies"
        v-model="selectedCommissie"
        @item-click="handleCommissieClick"
      >
        <template #item="{ item }">
          {{ item.naam }}
        </template>
      </FlowbiteList>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useOffice } from '../../../office'
import { useErrorHandler } from '../../../shared'
import { getStepBehaviorConfig } from '../../../../config/appConfig'
import StepHeader from './StepHeader.vue'
import FlowbiteList from '../../../shared/components/common/FlowbiteList.vue'

// Self-contained step information (no props needed)
const stepTitle = 'Commissies'
const stepDescription = 'Selecteer een commissie om toe te voegen aan het document.'

// New minimal event system
const emit = defineEmits({
  'step-ready': null, // Component loaded and ready
  'step-complete': null, // User completed this step
  'step-previous': null, // User wants to go back
})

const { insertTextInContentControl } = useOffice()
const { withErrorHandling } = useErrorHandler()
const selectedCommissie = ref(null)

// Mock data for commissies (committees), to be replaced with actual data from SharePoint
const commissies = [
  {
    naam: 'Binnenlandse Zaken',
    value: 'Commissie voor Binnenlandse Zaken',
    controlTag: 'Commissie',
  },
  {
    naam: 'Buitenlandse Zaken',
    value: 'Commissie voor Buitenlandse Zaken',
    controlTag: 'Commissie',
  },
  { naam: 'Defensie', value: 'Commissie voor Defensie', controlTag: 'Commissie' },
  { naam: 'Economische Zaken', value: 'Commissie voor Economische Zaken', controlTag: 'Commissie' },
  { naam: 'Financiën', value: 'Commissie voor Financiën', controlTag: 'Commissie' },
  {
    naam: 'Justitie en Veiligheid',
    value: 'Commissie voor Justitie en Veiligheid',
    controlTag: 'Commissie',
  },
  {
    naam: 'Onderwijs, Cultuur en Wetenschap',
    value: 'Commissie voor Onderwijs, Cultuur en Wetenschap',
    controlTag: 'Commissie',
  },
  {
    naam: 'Sociale Zaken en Werkgelegenheid',
    value: 'Commissie voor Sociale Zaken en Werkgelegenheid',
    controlTag: 'Commissie',
  },
  {
    naam: 'Volksgezondheid, Welzijn en Sport',
    value: 'Commissie voor Volksgezondheid, Welzijn en Sport',
    controlTag: 'Commissie',
  },
]

// Handle commissie click from FlowbiteList
function handleCommissieClick(event) {
  // selectedCommissie is already updated via v-model binding
  // so we just need to process the document insertion
  addSelectedCommissieToDocument(event.index)
}

// Add the selected commissie to the document
async function addSelectedCommissieToDocument(index) {
  try {
    // Validate that a valid index was provided
    if (index === null || index === undefined || index < 0 || index >= commissies.length) {
      await withErrorHandling(() => Promise.reject(new Error('Invalid selection')), {
        errorMessage: 'Selecteer eerst een commissie voordat u verder gaat.',
      })
      return
    }

    selectedCommissie.value = index
    const commissie = commissies[selectedCommissie.value]

    await withErrorHandling(
      async () => {
        await insertTextInContentControl(commissie.controlTag, commissie.value)
        setTimeout(() => {
          emit('step-complete')
        }, getStepBehaviorConfig().completionDelay) // Delay to ensure notification is visible before navigation
      },
      {
        errorMessage: `Er is een fout opgetreden bij het toevoegen van de commissie '${commissie.naam}' aan het document.`,
        successMessage: `Commissie '${commissie.naam}' is toegevoegd aan het document.`,
      },
    )
  } catch (error) {
    // Reset selection on error
    selectedCommissie.value = null
    console.error('Error adding commissie:', error)
  }
}

// Signal that component is ready (like VBA UserForm_Initialize)
onMounted(() => {
  emit('step-ready')
})
</script>

<style scoped>
.commissie-container {
  margin-top: 1rem !important;
  width: 100%;
}
</style>
