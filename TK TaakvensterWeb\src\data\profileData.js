/**
 * Static profile data for ProfielAfzender step
 * 
 * This file contains realistic Dutch government profile data.
 * In the future, this can be replaced with API calls while
 * maintaining the same data structure.
 */

/**
 * Profile data structure:
 * - FirstName: string (required) - First name of the person
 * - LastName: string (required) - Last name of the person  
 * - Role: string (optional) - Job title or role
 * - EmailAddress: string (optional) - Email address
 * - CommissionId: string (optional) - Links to commission data
 */
export const profileData = [
  {
    FirstName: "Jan",
    LastName: "de <PERSON><PERSON>",
    Role: "Sjabloon-ontwikkelaar",
    EmailAddress: "<EMAIL>",
    CommissionId: "infrastructuur-waterstaat"
  },
  {
    FirstName: "<PERSON>",
    LastName: "van der <PERSON>",
    Role: "Commissievoorzitter",
    EmailAddress: "m.vanden<PERSON>@tweedekamer.nl", 
    CommissionId: "binnenlandse-zaken"
  },
  {
    FirstName: "<PERSON>",
    LastName: "<PERSON><PERSON>",
    Role: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
    EmailAddress: "p.j<PERSON><PERSON>@tweedekamer.nl",
    CommissionId: "financien"
  },
  {
    FirstName: "<PERSON>",
    LastName: "He<PERSON><PERSON><PERSON>",
    Role: "Commissiesecretaris",
    EmailAddress: "<EMAIL>",
    CommissionId: "justitie-veiligheid"
  },
  {
    FirstName: "Robert",
    LastName: "van Dijk",
    Role: "Beleidsmedewerker",
    EmailAddress: "<EMAIL>",
    CommissionId: "economische-zaken"
  },
  {
    FirstName: "Linda",
    LastName: "Bakker",
    Role: "Commissievoorzitter",
    EmailAddress: "<EMAIL>",
    CommissionId: "onderwijs-cultuur"
  },
  {
    FirstName: "Thomas",
    LastName: "de Wit",
    Role: "Commissielid",
    EmailAddress: "<EMAIL>",
    CommissionId: "volksgezondheid"
  },
  {
    FirstName: "Emma",
    LastName: "Visser",
    Role: "Beleidsadviseur",
    EmailAddress: "<EMAIL>",
    CommissionId: "sociale-zaken"
  },
  {
    FirstName: "Mark",
    LastName: "van Leeuwen",
    Role: "Commissielid",
    EmailAddress: "<EMAIL>",
    CommissionId: "defensie"
  },
  {
    FirstName: "Anouk",
    LastName: "Peters",
    Role: "Commissiesecretaris",
    EmailAddress: "<EMAIL>",
    CommissionId: "buitenlandse-zaken"
  },
  {
    FirstName: "David",
    LastName: "Smit",
    Role: "Beleidsmedewerker",
    EmailAddress: "<EMAIL>",
    CommissionId: "landbouw-natuur"
  },
  {
    FirstName: "Iris",
    LastName: "de Jong",
    Role: "Commissielid",
    EmailAddress: "<EMAIL>",
    CommissionId: "klimaat-energie"
  },
  {
    FirstName: "Jeroen",
    LastName: "Mulder",
    Role: "Commissievoorzitter",
    EmailAddress: "<EMAIL>",
    CommissionId: "digitale-zaken"
  },
  {
    FirstName: "Nathalie",
    LastName: "Groot",
    Role: "Beleidsadviseur",
    EmailAddress: "<EMAIL>",
    CommissionId: "wonen-ruimtelijke-ordening"
  },
  {
    FirstName: "Bas",
    LastName: "van der Meer",
    Role: "Commissielid",
    EmailAddress: "<EMAIL>",
    CommissionId: "infrastructuur-waterstaat"
  },
  {
    FirstName: "Marloes",
    LastName: "Koning",
    Role: "Commissiesecretaris",
    EmailAddress: "<EMAIL>",
    CommissionId: "europese-zaken"
  },
  {
    FirstName: "Sander",
    LastName: "Brouwer",
    Role: "Beleidsmedewerker",
    EmailAddress: "<EMAIL>",
    CommissionId: "koninkrijksrelaties"
  },
  {
    FirstName: "Femke",
    LastName: "van Dam",
    Role: "Commissielid",
    EmailAddress: "<EMAIL>",
    CommissionId: "financien"
  },
  {
    FirstName: "Rick",
    LastName: "Scholten",
    Role: "Beleidsadviseur",
    EmailAddress: "<EMAIL>",
    CommissionId: "binnenlandse-zaken"
  },
  {
    FirstName: "Lotte",
    LastName: "van Beek",
    Role: "Commissielid",
    EmailAddress: "<EMAIL>",
    CommissionId: "justitie-veiligheid"
  }
]

/**
 * Test data for validation scenarios
 * These profiles have various data issues for testing purposes
 */
export const invalidProfileData = [
  {
    // Missing FirstName - should be skipped
    FirstName: "",
    LastName: "Invalid",
    Role: "Test Role",
    EmailAddress: "<EMAIL>",
    CommissionId: "test-commission"
  },
  {
    // Missing LastName - should be skipped
    FirstName: "Invalid",
    LastName: null,
    Role: "Test Role", 
    EmailAddress: "<EMAIL>",
    CommissionId: "test-commission"
  },
  {
    // Missing both names - should be skipped
    FirstName: undefined,
    LastName: "",
    Role: "Test Role",
    EmailAddress: "<EMAIL>",
    CommissionId: "test-commission"
  },
  {
    // Valid profile with minimal data
    FirstName: "Valid",
    LastName: "Minimal",
    // No role, email, or commission - should be accepted
  },
  {
    // Valid profile with invalid commission ID
    FirstName: "Valid",
    LastName: "NoCommission",
    Role: "Test Role",
    EmailAddress: "<EMAIL>",
    CommissionId: "non-existent-commission"
  }
]

/**
 * Get all valid profiles (filters out invalid ones)
 * @returns {Array} Array of valid profile objects
 */
export function getValidProfiles() {
  return profileData.filter(profile => {
    return profile.FirstName && 
           profile.FirstName.trim() !== '' &&
           profile.LastName && 
           profile.LastName.trim() !== ''
  })
}

/**
 * Get profile by index
 * @param {number} index - Index of the profile
 * @returns {Object|null} Profile object or null if not found
 */
export function getProfileByIndex(index) {
  const validProfiles = getValidProfiles()
  return validProfiles[index] || null
}

/**
 * Get profiles by commission ID
 * @param {string} commissionId - Commission ID to filter by
 * @returns {Array} Array of profiles for the commission
 */
export function getProfilesByCommission(commissionId) {
  return getValidProfiles().filter(profile => profile.CommissionId === commissionId)
}

/**
 * Format profile display name
 * @param {Object} profile - Profile object
 * @returns {string} Formatted display name
 */
export function formatProfileDisplayName(profile) {
  if (!profile || !profile.FirstName || !profile.LastName) {
    return 'Onbekend profiel'
  }
  return `${profile.FirstName} ${profile.LastName}`
}
