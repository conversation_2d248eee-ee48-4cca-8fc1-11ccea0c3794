# Requirements for Profile Sender Step Implementation

## Overview

Add a new step, `ProfielAfzender`, to handle sender profile selection and populate related content controls in a Word add-in. The step displays a static list of profiles and links them to commission data, with error handling and configuration support.

## Feature Requirements

### 1. ProfielAfzender Step Activation

- **Purpose**: Introduce a new step called `ProfielAfzender` to select a sender profile.
- **Activation Logic**:
  - Check for the presence of the following content controls in the document:
    - `txtAfzenderPersoonlijk`
    - `txtAfzenderCommissie1`
    - `txtAfzenderCommissie2`
  - If any content control is missing, do not activate the `ProfielAfzender` step.
  - Reuse existing logic for step activation (refer to current codebase for implementation details, e.g., step validation logic).
- **Error Handling**:
  - If the step cannot be activated due to missing content controls, display an error message using the existing error-handling flow (message bar display). Make sure this is the same as the flow for existing steps. Be sure to mention this in your work suggestion so the user understands the flow.
  - Ensure the step activation failure does not disrupt other functionalities.

### 2. Profile Data Display

- **UI Component**:
  - Display a static list of profiles in the `ProfielAfzender` step UI.
  - Each list item shows the profile's `FirstName` and `LastName`.
- **Profile Data Structure**:
  - Each profile contains:
    - `FirstName` (string, required)
    - `LastName` (string, required)
    - `Role` (string, optional)
    - `EmailAddress` (string, optional)
    - `CommissionId` (string, links to a commission in a separate data list)
- **Data Validation**:
  - Ensure `FirstName` and `LastName` are non-empty strings (not empty, null, or undefined).
  - If `FirstName` or `LastName` is empty or both, skip the profile and log a warning to the console using the existing logging framework.
- **Performance**:

  - Optimize the profile list rendering to handle large datasets efficiently (note that we'll be implementing search later on - but not now)

- **Behavior**:
  - Profiles are linked to commissions via `CommissionId`, but commission data is not displayed in the UI (used for data population only).

### 3. Commission Data Structure

- **Commission Data**:
  - Each commission contains:
    - `CommissionName` (string, required, e.g., "Vaste commissie voor Infrastructuur en Waterstaat")
    - `POBoxNumber` (string, optional, e.g., "Postbus 20018")
    - `ZipCode` (string, optional, e.g., "2500 EA Den Haag")
    - `City` (string, optional, e.g., "Den Haag")
    - `StreetName` (string, optional, e.g., "Prinses Irenepad")
    - `HouseNumber` (string, optional, e.g., "1")
  - Note: `ZipCode` and `City` may overlap between `POBox` and `StreetAddress` if they refer to the same location.
- **Data Validation**:
  - Ensure `CommissionName` is a non-empty string (not empty, null, or undefined).
  - If `CommissionName` is empty, skip the commission and log a warning to the console.
  - Other fields (`POBoxNumber`, `ZipCode`, `City`, `StreetName`, `HouseNumber`) are optional and should be included only if non-empty during population.
- **Data Storage**:
  - Store commission data in a separate data list, linked to profiles via `CommissionId`.
  - Design this data list in such a way that, at a later stage, we can easily get the data through an API (you might use a pinia datastore if applicable)

### 4. Data Population on Profile Selection

- **Behavior**:
  - When a profile is selected in the `ProfielAfzender` step UI, populate the three content controls:
    - **txtAfzenderPersoonlijk**:
      - Insert personal data in the format: `{FirstName} {LastName}\n{Role}\n{EmailAddress}`.
      - Omit any empty, null, or undefined fields (e.g., if only `EmailAddress` exists, populate only `<EMAIL>`).
      - Example:
        ```
        Jan de Bont
        Sjabloon-ontwikkelaar
        <EMAIL>
        ```
    - **txtAfzenderCommissie1**:
      - Insert fixed text `"Tweede Kamer der Staten-Generaal"` followed by ` | {CommissionName}`.
      - Omit `CommissionName` if empty.
      - Example: `Tweede Kamer der Staten-Generaal | Vaste commissie voor Infrastructuur en Waterstaat`.
    - **txtAfzenderCommissie2**:
      - Insert `POBoxNumber, ZipCode City` and `StreetName HouseNumber, ZipCode City`, separated by `|`.
      - Combine fields only if non-empty (e.g., if `POBoxNumber` is empty, include only `StreetName HouseNumber, ZipCode City`).
      - Example: `Postbus 20018, 2500 EA Den Haag | Prinses Irenepad 1, 2500 EA Den Haag`.
- **Missing Commission Handling**:
  - If a profile’s `CommissionId` does not match any commission, populate only `txtAfzenderPersoonlijk`, log an error to the console, and include the error in the collected error list for display in the message bar.
- **Error Handling**:
  - If insertion into any content control fails, log an error to the console, include it in the collected error list, and continue processing remaining content controls.
  - Collect all errors (e.g., content control insertion failures, missing `CommissionId`) during execution.
  - After all logic is complete, display a single error message in the message bar listing all collected errors using the existing error-handling flow.
  - Make sure the error message is a friendly, easy to understand message to the user
  - Make sure to display additional info in the console when in development mode (see other steps for the logic about this)
- **Logging**:
  - Log key actions to the console (e.g., profile selection, content control population success/failure, missing `CommissionId`) using the existing logging framework.

### 5. Configuration

- **Fixed Text**:
  - Store the fixed text `"Tweede Kamer der Staten-Generaal"` in a configuration file (e.g., `config.js` or `config.json`).
  - Check for an existing configuration file and use it if present; otherwise, create a new one.
  - Ensure the configuration file is extensible for future settings (e.g., other fixed strings or UI options).
- **Implementation**:
  - Use a Vue.js configuration file to manage fixed text and other settings.
  - Check if an existing one is present and if so, use that
  - Example structure:
    ```javascript
    // config.js
    export const appConfig = {
      fixedText: {
        commissionPrefix: 'Tweede Kamer der Staten-Generaal',
      },
    }
    ```

### 6. Assumptions and Clarifications

- **Profile-Commission Linking**:
  - Each profile is linked to a specific commission via `CommissionId`.
- **Static List**:
  - The profile list is static at this stage. But make it easy to switch to using an api.
- **Error Handling**:
  - The existing error-handling flow uses a message bar for user notifications. Check the current setup for this in the existing code and ask the user if your approach is correct before implementing.

### 7. Failsafe Design

- Ensure the implementation is robust:
  - Handle missing or empty data gracefully (e.g., omit optional fields like `Role`, `EmailAddress`, `POBoxNumber`, `ZipCode`, `City`, `StreetName`, `HouseNumber`).
  - Continue execution even if one content control insertion fails, logging the error to the console and collecting it for the final error message.
  - Use try-catch blocks or equivalent (withErrorHandler, see rest of the application) to isolate failures during content control population.
  - Include detailed debug logs to the console for each failure point (e.g., missing content control, empty data, insertion failure, invalid `CommissionId`) to aid troubleshooting.

### 8. Testing Requirements

- **Test Cases**:
  - Verify step activation with all content controls present.
  - Verify step deactivation when any content control is missing.
  - Test profile selection with complete data (all fields present).
  - Test profile selection with partial data (e.g., only `EmailAddress`).
  - Test commission population with partial data (e.g., only `CommissionName` or `StreetName`).
  - Test invalid `CommissionId` (no matching commission, verify console log and message bar).
  - Test content control population failures (e.g., simulate locked content control).
  - Verify a single error message in the message bar lists all collected errors (e.g., insertion failures, missing `CommissionId`).
  - Test performance with a large profile list (e.g., 100+ profiles).

## Process

- First, present your implementation suggestions to the user
- Make a task list for all of your tasks
- Ask for feedback before implementing
- Add tests and review your code after each task / each coding step
- Do a final review at the end, when all tasks are completed

## Testing Requirements

See `ai/requirements/profilestep_tests.md` for detailed testing specifications.
