using Microsoft.Graph;
using System;
using System.Collections.Generic;
using System.Configuration;
using System.Diagnostics;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using System.Web.Http;

namespace TK_TaakvensterWeb.Controllers
{
    /// <summary>
    /// Controller for handling Microsoft Graph API requests
    /// </summary>
    public class GraphController : ApiController
    {
        #region Configuration
        // Configuration values from appsettings.config
        private readonly string _clientId;
        private readonly string _clientSecret;
        private readonly string _tenantId;
        private readonly string _authority;
        private readonly string _graphScope;
        #endregion
        
        private readonly IGraphAuthenticationService _authService;
        private readonly ISharePointService _sharePointService;
        
        /// <summary>
        /// Constructor with service initialization
        /// </summary>
        public GraphController()
        {
            // Load configuration from appsettings.config
            _clientId = ConfigurationManager.AppSettings["Graph:ClientId"];
            _clientSecret = ConfigurationManager.AppSettings["Graph:ClientSecret"];
            _tenantId = ConfigurationManager.AppSettings["Graph:TenantId"];
            _authority = ConfigurationManager.AppSettings["Graph:Authority"];
            _graphScope = ConfigurationManager.AppSettings["Graph:Scope"];
            
            // Validate configuration
            ValidateConfiguration();
            
            // Initialize services
            // TODO: Use proper dependency injection instead of creating services here
            _authService = new GraphAuthenticationService(_clientId, _clientSecret, _tenantId, _authority, _graphScope);
            _sharePointService = new SharePointService();
        }

        /// <summary>
        /// Validates that all required configuration values are set
        /// </summary>
        private void ValidateConfiguration()
        {
            var missingKeys = new List<string>();
            
            if (string.IsNullOrEmpty(_clientId)) missingKeys.Add("Graph:ClientId");
            if (string.IsNullOrEmpty(_clientSecret)) missingKeys.Add("Graph:ClientSecret");
            if (string.IsNullOrEmpty(_tenantId)) missingKeys.Add("Graph:TenantId");
            if (string.IsNullOrEmpty(_authority)) missingKeys.Add("Graph:Authority");
            if (string.IsNullOrEmpty(_graphScope)) missingKeys.Add("Graph:Scope");
            
            if (missingKeys.Any())
            {
                string missingValues = string.Join(", ", missingKeys);
                throw new InvalidOperationException($"The following configuration values are missing: {missingValues}. Check your appsettings.config file.");
            }
        
            Debug.WriteLine("All required configuration values are set");
        }

        [HttpPost]
        [Route("api/graph/getlist")]
        public async Task<IHttpActionResult> GetSharePointList([FromBody] SharePointListRequest request)
        {
            try
            {
                // Basic request validation
                if (request == null)
                    return BadRequest("Invalid request");
                
                if (string.IsNullOrEmpty(request.Token))
                    return BadRequest("No token provided");

                // Get Graph client with OBO flow
                var graphClient = await _authService.GetGraphClientWithOBO(request.Token);
                if (graphClient == null)
                    return Content(HttpStatusCode.BadRequest, _authService.GetLastError() ?? "Failed to initialize Graph client");

                // Resolve site ID from URL if needed
                string siteId = request.SiteId;
                if (string.IsNullOrEmpty(siteId) && !string.IsNullOrEmpty(request.SiteUrl))
                {
                    try
                    {
                        var site = await _sharePointService.GetSiteByUrl(graphClient, request.SiteUrl, request.HostName);
                        siteId = site.Id;
                    }
                    catch (ServiceException ex)
                    {
                        return Content(HttpStatusCode.NotFound, $"Site not found: {ex.Message}");
                    }
                }

                // Resolve list ID from name if needed
                string listId = request.ListId;
                if (string.IsNullOrEmpty(listId) && !string.IsNullOrEmpty(request.ListName) && !string.IsNullOrEmpty(siteId))
                {
                    var targetList = await _sharePointService.GetListByName(graphClient, siteId, request.ListName);
                    if (targetList == null)
                    {
                        return Content(HttpStatusCode.BadRequest, new {
                            error = new {
                                code = "ListNotFound",
                                message = $"List '{request.ListName}' not found in site '{request.SiteUrl}'"
                            }
                        });
                    }
                    
                    listId = targetList.Id;
                }

                // Validate required IDs
                if (string.IsNullOrEmpty(siteId) || string.IsNullOrEmpty(listId))
                    return BadRequest("Site ID and List ID are required");

                // Fetch and process list items
                var items = await _sharePointService.GetListItems(
                    graphClient, siteId, listId, request.Filter, request.OrderBy, request.Top);
                
                var processedItems = items.Select(_sharePointService.ProcessListItem).ToList();
                return Ok(new { value = processedItems });
            }
            catch (ServiceException ex)
            {
                Debug.WriteLine($"Graph Service Exception: {ex.Message}");
                return Content(HttpStatusCode.BadRequest, ex.Message);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in GetSharePointList: {ex.Message}");
                return InternalServerError(ex);
            }
        }

        // Methods moved to service classes
    }

    public class SharePointListRequest
    {
        public string Token { get; set; }
        public string SiteId { get; set; }
        public string SiteUrl { get; set; }
        public string HostName { get; set; }
        public string ListId { get; set; }
        public string ListName { get; set; }
        public string Filter { get; set; }
        public string OrderBy { get; set; }
        public int? Top { get; set; }
    }
}