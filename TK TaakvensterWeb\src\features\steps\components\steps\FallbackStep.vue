<template>
    <div class="error">
      <p>Sorry, this step failed to load.</p>
      <p v-if="error">{{ error }}</p>
    </div>
  </template>
  
  <script setup>
  defineProps({
    error: {
      type: String,
      default: null,
    },
  });
  </script>
  
  <style scoped>
  .error {
    color: #d32f2f;
    background-color: #ffebee;
    padding: 16px;
    border-radius: 4px;
    margin-top: 8px;
    text-align: center;
  }
  .error p {
    margin: 0 0 8px;
  }
  </style>