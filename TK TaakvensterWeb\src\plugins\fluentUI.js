// fluentUI.js - Plugin for Fluent UI Web Components in Vue (Beta Version)

// Import the design system provider
import { FluentDesignSystem } from '@fluentui/web-components';

// Import individual components from their specific paths (beta version)
import '@fluentui/web-components/button.js';
import '@fluentui/web-components/listbox.js';
import '@fluentui/web-components/message-bar.js';

// Import component definitions for registration
import { ButtonDefinition, ListboxDefinition, MessageBarDefinition } from '@fluentui/web-components';

// Create a plugin that registers Fluent UI Web Components with Vue
export default {
  install(app, options = {}) {
    // Define the components we want to register
    const defaultComponents = [
      ButtonDefinition,
      ListboxDefinition,
      MessageBarDefinition
    ];
    
    // Merge default components with any additional ones specified in options
    const componentsToRegister = options.additionalComponents 
      ? [...defaultComponents, ...options.additionalComponents]
      : defaultComponents;
    
    // Allow overriding the default components entirely if needed
    const finalComponents = options.components || componentsToRegister;
    
    // Register the components with the design system
    finalComponents.forEach(component => {
      component.define(FluentDesignSystem.registry);
    });
    
    // Configure Vue to recognize Fluent UI custom elements
    app.config.compilerOptions = {
      ...app.config.compilerOptions,
      isCustomElement: tag => tag.startsWith('fluent-')
    };
    
    console.log(`Fluent UI Web Components plugin initialized with ${finalComponents.length} components`);
  }
}
